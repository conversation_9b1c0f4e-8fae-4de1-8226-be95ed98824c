"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const transaction_controller_1 = require("../controllers/transaction.controller");
const auth_middleware_1 = __importDefault(require("../middlewares/auth.middleware"));
const router = (0, express_1.Router)();
const transactionController = new transaction_controller_1.TransactionController();
router.use(auth_middleware_1.default.authenticate);
router.post('/purchase', transactionController.createPurchaseTransaction);
router.post('/purchase/confirm', transactionController.confirmPurchase);
router.post('/purchase/cancel', transactionController.cancelPurchase);
router.get('/my', transactionController.getMyTransactions);
router.get('/:id', transactionController.getTransactionById);
router.get('/:id/status', transactionController.getTransactionStatus);
router.get('/', auth_middleware_1.default.requireAdmin, transactionController.getAllTransactions);
router.put('/:id/status', auth_middleware_1.default.requireAdmin, transactionController.updateTransactionStatus);
exports.default = router;
//# sourceMappingURL=transaction.routes.js.map