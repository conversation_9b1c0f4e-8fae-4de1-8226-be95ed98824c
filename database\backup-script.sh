#!/bin/bash

# AIGC Service Hub 数据库备份脚本
# 支持自动备份、压缩和清理

set -e

# 配置变量
DB_HOST=${POSTGRES_HOST:-"postgres"}
DB_PORT=${POSTGRES_PORT:-5432}
DB_NAME=${POSTGRES_DB:-"aigc_service_hub"}
DB_USER=${POSTGRES_USER:-"postgres"}
DB_PASSWORD=${POSTGRES_PASSWORD:-"postgres123"}
BACKUP_DIR=${BACKUP_DIR:-"/backups"}
BACKUP_RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}
BACKUP_COMPRESSION=${BACKUP_COMPRESSION:-"gzip"}

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 生成备份文件名
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/${DB_NAME}_backup_$TIMESTAMP.sql"
COMPRESSED_FILE="$BACKUP_FILE.gz"

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 错误处理
handle_error() {
    log "ERROR: $1"
    exit 1
}

# 检查依赖
check_dependencies() {
    log "检查依赖..."
    
    if ! command -v pg_dump &> /dev/null; then
        handle_error "pg_dump 命令未找到"
    fi
    
    if [ "$BACKUP_COMPRESSION" = "gzip" ] && ! command -v gzip &> /dev/null; then
        handle_error "gzip 命令未找到"
    fi
    
    log "依赖检查完成"
}

# 测试数据库连接
test_connection() {
    log "测试数据库连接..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" &> /dev/null; then
        handle_error "无法连接到数据库 $DB_HOST:$DB_PORT"
    fi
    
    log "数据库连接测试成功"
}

# 执行备份
perform_backup() {
    log "开始备份数据库 $DB_NAME..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 执行备份
    pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --no-owner \
        --no-privileges \
        --create \
        --clean \
        --if-exists \
        --format=plain \
        --encoding=UTF8 \
        --file="$BACKUP_FILE" || handle_error "备份失败"
    
    log "数据库备份完成: $BACKUP_FILE"
    
    # 压缩备份文件
    if [ "$BACKUP_COMPRESSION" = "gzip" ]; then
        log "压缩备份文件..."
        gzip "$BACKUP_FILE" || handle_error "压缩失败"
        log "备份文件压缩完成: $COMPRESSED_FILE"
        FINAL_BACKUP_FILE="$COMPRESSED_FILE"
    else
        FINAL_BACKUP_FILE="$BACKUP_FILE"
    fi
    
    # 验证备份文件
    if [ ! -f "$FINAL_BACKUP_FILE" ]; then
        handle_error "备份文件不存在: $FINAL_BACKUP_FILE"
    fi
    
    BACKUP_SIZE=$(du -h "$FINAL_BACKUP_FILE" | cut -f1)
    log "备份完成，文件大小: $BACKUP_SIZE"
}

# 清理旧备份
cleanup_old_backups() {
    log "清理 $BACKUP_RETENTION_DAYS 天前的备份文件..."
    
    find "$BACKUP_DIR" -name "${DB_NAME}_backup_*.sql*" -type f -mtime +$BACKUP_RETENTION_DAYS -delete
    
    REMAINING_BACKUPS=$(find "$BACKUP_DIR" -name "${DB_NAME}_backup_*.sql*" -type f | wc -l)
    log "清理完成，剩余备份文件: $REMAINING_BACKUPS 个"
}

# 生成备份报告
generate_report() {
    log "生成备份报告..."
    
    REPORT_FILE="$BACKUP_DIR/backup_report_$(date +"%Y%m%d").txt"
    
    cat > "$REPORT_FILE" << EOF
AIGC Service Hub 数据库备份报告
================================

备份时间: $(date)
数据库主机: $DB_HOST:$DB_PORT
数据库名称: $DB_NAME
备份文件: $FINAL_BACKUP_FILE
文件大小: $BACKUP_SIZE
保留天数: $BACKUP_RETENTION_DAYS

备份文件列表:
$(find "$BACKUP_DIR" -name "${DB_NAME}_backup_*.sql*" -type f -printf "%T@ %Tc %p\n" | sort -n | cut -d' ' -f2-)

磁盘使用情况:
$(df -h "$BACKUP_DIR")

备份验证:
$(file "$FINAL_BACKUP_FILE")

EOF
    
    log "备份报告生成完成: $REPORT_FILE"
}

# 发送通知（可选）
send_notification() {
    if [ -n "$BACKUP_NOTIFICATION_EMAIL" ]; then
        log "发送备份通知邮件..."
        
        # 这里可以集成邮件通知逻辑
        # 例如使用 sendmail 或 AWS SES
        
        log "备份通知已发送"
    fi
}

# 主函数
main() {
    log "开始数据库备份流程..."
    
    # 检查参数
    if [ $# -gt 0 ]; then
        case "$1" in
            --help|-h)
                echo "数据库备份脚本使用说明:"
                echo "  $0                  # 执行备份"
                echo "  $0 --test          # 测试连接"
                echo "  $0 --list          # 列出备份文件"
                echo "  $0 --cleanup       # 清理旧备份"
                echo "  $0 --restore FILE  # 恢复备份"
                echo "  $0 --help          # 显示帮助"
                exit 0
                ;;
            --test)
                test_connection
                exit 0
                ;;
            --list)
                echo "备份文件列表:"
                find "$BACKUP_DIR" -name "${DB_NAME}_backup_*.sql*" -type f -printf "%T@ %Tc %p\n" | sort -rn | cut -d' ' -f2-
                exit 0
                ;;
            --cleanup)
                cleanup_old_backups
                exit 0
                ;;
            --restore)
                if [ -z "$2" ]; then
                    handle_error "请指定要恢复的备份文件"
                fi
                restore_backup "$2"
                exit 0
                ;;
            *)
                handle_error "未知参数: $1"
                ;;
        esac
    fi
    
    # 执行备份流程
    check_dependencies
    test_connection
    perform_backup
    cleanup_old_backups
    generate_report
    send_notification
    
    log "数据库备份流程完成"
}

# 恢复备份函数
restore_backup() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        handle_error "备份文件不存在: $backup_file"
    fi
    
    log "开始恢复备份: $backup_file"
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 检查文件类型
    if [[ "$backup_file" == *.gz ]]; then
        log "解压缩备份文件..."
        gunzip -c "$backup_file" | psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" || handle_error "恢复失败"
    else
        psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "$backup_file" || handle_error "恢复失败"
    fi
    
    log "数据库恢复完成"
}

# 执行主函数
main "$@"