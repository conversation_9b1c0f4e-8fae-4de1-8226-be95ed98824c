"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileController = void 0;
const file_service_1 = require("../services/file.service");
const errors_1 = require("../utils/errors");
const logger_1 = require("../utils/logger");
class FileController {
    constructor() {
        this.uploadFile = async (req, res, next) => {
            try {
                const user = req.user;
                const file = req.file;
                if (!file) {
                    throw new errors_1.ValidationError('No file uploaded');
                }
                const fileData = {
                    originalName: file.originalname,
                    fileName: file.filename,
                    filePath: file.path,
                    fileSize: file.size,
                    mimeType: file.mimetype,
                    uploadedBy: user.id,
                };
                const uploadedFile = await this.fileService.uploadFile(fileData);
                logger_1.logger.info(`File uploaded successfully: ${file.originalname}`, {
                    userId: user.id,
                    fileId: uploadedFile.id,
                });
                res.status(201).json({
                    success: true,
                    data: uploadedFile,
                    message: 'File uploaded successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.uploadMultipleFiles = async (req, res, next) => {
            try {
                const user = req.user;
                const files = req.files;
                if (!files || files.length === 0) {
                    throw new errors_1.ValidationError('No files uploaded');
                }
                const fileDataArray = files.map(file => ({
                    originalName: file.originalname,
                    fileName: file.filename,
                    filePath: file.path,
                    fileSize: file.size,
                    mimeType: file.mimetype,
                    uploadedBy: user.id,
                }));
                const uploadedFiles = await this.fileService.uploadMultipleFiles(fileDataArray);
                logger_1.logger.info(`Multiple files uploaded successfully: ${files.length} files`, {
                    userId: user.id,
                    fileCount: files.length,
                });
                res.status(201).json({
                    success: true,
                    data: uploadedFiles,
                    message: `${files.length} files uploaded successfully`,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getFiles = async (req, res, next) => {
            try {
                const user = req.user;
                const { page = 1, limit = 20, search, fileType } = req.query;
                const filters = {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    search: search,
                    fileType: fileType,
                    userId: user.userRole === 'ADMIN' ? undefined : user.id,
                };
                const result = await this.fileService.getFiles(filters);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getUserFiles = async (req, res, next) => {
            try {
                const user = req.user;
                const { userId } = req.params;
                const { page = 1, limit = 20 } = req.query;
                if (user.userRole !== 'ADMIN' && user.id !== parseInt(userId)) {
                    throw new errors_1.AuthorizationError('You can only view your own files');
                }
                const filters = {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    userId: parseInt(userId),
                };
                const result = await this.fileService.getFiles(filters);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.downloadFile = async (req, res, next) => {
            try {
                const user = req.user;
                const { fileId } = req.params;
                const file = await this.fileService.getFileById(parseInt(fileId));
                if (!file) {
                    throw new errors_1.NotFoundError('File not found');
                }
                if (user.userRole !== 'ADMIN' && file.uploadedBy !== user.id) {
                    throw new errors_1.AuthorizationError('You can only download your own files');
                }
                const filePath = await this.fileService.getFilePath(file.id);
                res.download(filePath, file.originalName, (err) => {
                    if (err) {
                        logger_1.logger.error('File download error:', err);
                        next(new errors_1.FileUploadError('Failed to download file'));
                    }
                    else {
                        logger_1.logger.info(`File downloaded: ${file.originalName}`, {
                            userId: user.id,
                            fileId: file.id,
                        });
                    }
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getFileInfo = async (req, res, next) => {
            try {
                const user = req.user;
                const { fileId } = req.params;
                const file = await this.fileService.getFileById(parseInt(fileId));
                if (!file) {
                    throw new errors_1.NotFoundError('File not found');
                }
                if (user.userRole !== 'ADMIN' && file.uploadedBy !== user.id) {
                    throw new errors_1.AuthorizationError('You can only view your own files');
                }
                res.json({
                    success: true,
                    data: file,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteFile = async (req, res, next) => {
            try {
                const user = req.user;
                const { fileId } = req.params;
                const file = await this.fileService.getFileById(parseInt(fileId));
                if (!file) {
                    throw new errors_1.NotFoundError('File not found');
                }
                if (user.userRole !== 'ADMIN' && file.uploadedBy !== user.id) {
                    throw new errors_1.AuthorizationError('You can only delete your own files');
                }
                await this.fileService.deleteFile(parseInt(fileId));
                logger_1.logger.info(`File deleted: ${file.originalName}`, {
                    userId: user.id,
                    fileId: file.id,
                });
                res.json({
                    success: true,
                    message: 'File deleted successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.updateFile = async (req, res, next) => {
            try {
                const user = req.user;
                const { fileId } = req.params;
                const { description, tags } = req.body;
                const file = await this.fileService.getFileById(parseInt(fileId));
                if (!file) {
                    throw new errors_1.NotFoundError('File not found');
                }
                if (user.userRole !== 'ADMIN' && file.uploadedBy !== user.id) {
                    throw new errors_1.AuthorizationError('You can only update your own files');
                }
                const updatedFile = await this.fileService.updateFile(parseInt(fileId), {
                    description,
                    tags,
                });
                res.json({
                    success: true,
                    data: updatedFile,
                    message: 'File updated successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getThumbnail = async (req, res, next) => {
            try {
                const user = req.user;
                const { fileId } = req.params;
                const file = await this.fileService.getFileById(parseInt(fileId));
                if (!file) {
                    throw new errors_1.NotFoundError('File not found');
                }
                if (user.userRole !== 'ADMIN' && file.uploadedBy !== user.id) {
                    throw new errors_1.AuthorizationError('You can only view your own files');
                }
                const thumbnailPath = await this.fileService.getThumbnailPath(parseInt(fileId));
                if (!thumbnailPath) {
                    throw new errors_1.NotFoundError('Thumbnail not found');
                }
                res.sendFile(thumbnailPath);
            }
            catch (error) {
                next(error);
            }
        };
        this.fileService = new file_service_1.FileService();
    }
}
exports.FileController = FileController;
//# sourceMappingURL=file.controller.js.map