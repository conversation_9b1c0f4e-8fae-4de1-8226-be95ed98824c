"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config({ path: path_1.default.resolve(__dirname, '../../.env') });
exports.config = {
    app: {
        env: process.env.NODE_ENV || 'development',
        port: parseInt(process.env.PORT || '3000'),
        name: process.env.APP_NAME || 'AIGC Service Hub',
        version: process.env.API_VERSION || 'v1',
        corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3001'],
    },
    database: {
        url: process.env.DATABASE_URL || 'postgresql://postgres:postgres123@localhost:5432/aigc_service_hub',
        poolSize: parseInt(process.env.DATABASE_POOL_SIZE || '20'),
        ssl: process.env.NODE_ENV === 'production',
    },
    redis: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        password: process.env.REDIS_PASSWORD || '',
        db: parseInt(process.env.REDIS_DB || '0'),
    },
    aws: {
        region: process.env.AWS_REGION || 'us-west-2',
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
        s3: {
            privateBucket: process.env.S3_PRIVATE_BUCKET || 'aigc-private-assets',
            publicBucket: process.env.S3_PUBLIC_BUCKET || 'aigc-public-assets',
        },
        ses: {
            region: process.env.AWS_SES_REGION || 'us-west-2',
            fromEmail: process.env.FROM_EMAIL || '<EMAIL>',
        },
    },
    jwt: {
        secret: process.env.JWT_SECRET || 'your-jwt-secret-key',
        expiresIn: process.env.JWT_EXPIRES_IN || '1d',
        refreshExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',
    },
    paypal: {
        clientId: process.env.PAYPAL_CLIENT_ID || '',
        clientSecret: process.env.PAYPAL_CLIENT_SECRET || '',
        sandbox: process.env.PAYPAL_SANDBOX === 'true',
        webhookId: process.env.PAYPAL_WEBHOOK_ID || '',
    },
    oauth: {
        google: {
            clientId: process.env.GOOGLE_CLIENT_ID || '',
            clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
            redirectUri: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3000/auth/google/callback',
        },
        github: {
            clientId: process.env.GITHUB_CLIENT_ID || '',
            clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
            redirectUri: process.env.GITHUB_REDIRECT_URI || 'http://localhost:3000/auth/github/callback',
        },
    },
    email: {
        host: process.env.SMTP_HOST || 'localhost',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
            user: process.env.SMTP_USER || '',
            pass: process.env.SMTP_PASSWORD || '',
        },
        from: process.env.FROM_EMAIL || '<EMAIL>',
    },
    upload: {
        maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '32212254720'),
        uploadUrlExpiresIn: parseInt(process.env.UPLOAD_URL_EXPIRES_IN || '900'),
        downloadUrlExpiresIn: parseInt(process.env.DOWNLOAD_URL_EXPIRES_IN || '300'),
        allowedMimeTypes: [
            'application/zip',
            'application/x-zip-compressed',
            'application/x-rar-compressed',
            'application/x-7z-compressed',
            'application/gzip',
            'application/x-tar',
            'application/octet-stream',
        ],
    },
    cache: {
        defaultTtl: parseInt(process.env.CACHE_DEFAULT_TTL || '300'),
        assetsTtl: parseInt(process.env.CACHE_ASSETS_TTL || '300'),
        userProfileTtl: parseInt(process.env.CACHE_USER_PROFILE_TTL || '600'),
        transactionsTtl: parseInt(process.env.CACHE_TRANSACTIONS_TTL || '60'),
        systemConfigsTtl: parseInt(process.env.CACHE_SYSTEM_CONFIGS_TTL || '3600'),
    },
    rateLimit: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'),
        maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
        sensitiveOperations: {
            windowMs: parseInt(process.env.SENSITIVE_RATE_LIMIT_WINDOW_MS || '3600000'),
            maxRequests: parseInt(process.env.SENSITIVE_RATE_LIMIT_MAX_REQUESTS || '10'),
        },
    },
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: process.env.LOG_FILE || 'logs/app.log',
        maxSize: process.env.LOG_MAX_SIZE || '10m',
        maxFiles: parseInt(process.env.LOG_MAX_FILES || '5'),
        enableConsole: process.env.NODE_ENV !== 'production',
    },
    system: {
        pointsRate: parseInt(process.env.POINTS_RATE || '100'),
        commission: {
            individual: {
                base: parseInt(process.env.COMMISSION_INDIVIDUAL_BASE || '5'),
                increment: parseInt(process.env.COMMISSION_INDIVIDUAL_INCREMENT || '5'),
                max: parseInt(process.env.COMMISSION_INDIVIDUAL_MAX || '50'),
            },
            enterprise: {
                base: parseInt(process.env.COMMISSION_ENTERPRISE_BASE || '8'),
                increment: parseInt(process.env.COMMISSION_ENTERPRISE_INCREMENT || '8'),
                max: parseInt(process.env.COMMISSION_ENTERPRISE_MAX || '56'),
            },
        },
        ledger: {
            pendingDays: parseInt(process.env.LEDGER_PENDING_DAYS || '7'),
        },
        withdrawal: {
            minAmount: parseFloat(process.env.MIN_WITHDRAWAL_AMOUNT || '10.00'),
        },
    },
    monitoring: {
        enableMetrics: process.env.ENABLE_METRICS === 'true',
        metricsInterval: parseInt(process.env.METRICS_INTERVAL || '60000'),
    },
    frontend: {
        url: process.env.FRONTEND_URL || 'http://localhost:3001',
        loginSuccessUrl: process.env.FRONTEND_LOGIN_SUCCESS_URL || 'http://localhost:3001/dashboard',
        loginFailureUrl: process.env.FRONTEND_LOGIN_FAILURE_URL || 'http://localhost:3001/login?error=oauth_failed',
    },
    swagger: {
        title: 'AIGC Service Hub API',
        description: 'API documentation for AIGC Service Hub MVP 1.0',
        version: '1.0.0',
        enabled: process.env.NODE_ENV !== 'production',
    },
};
const requiredEnvVars = [
    'JWT_SECRET',
    'DATABASE_URL',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'S3_PRIVATE_BUCKET',
    'PAYPAL_CLIENT_ID',
    'PAYPAL_CLIENT_SECRET',
];
if (exports.config.app.env === 'production') {
    for (const envVar of requiredEnvVars) {
        if (!process.env[envVar]) {
            throw new Error(`Missing required environment variable: ${envVar}`);
        }
    }
}
exports.default = exports.config;
//# sourceMappingURL=index.js.map