import { Router } from 'express';
import { FinanceController } from '@/controllers/finance.controller';
import authMiddleware from '@/middlewares/auth.middleware';

const router = Router();
const financeController = new FinanceController();

// 所有财务路由都需要认证
router.use(authMiddleware.authenticate);

// 提现相关路由
router.post('/withdraw', financeController.createWithdrawalRequest);
router.get('/withdrawals', financeController.getWithdrawalRequests);
router.get('/withdrawals/:id', financeController.getWithdrawalById);

// 收益统计
router.get('/earnings', financeController.getEarnings);
router.get('/balance', financeController.getBalance);

// 管理员路由
router.get('/admin/withdrawals', authMiddleware.requireAdmin, financeController.getAllWithdrawals);
router.put('/admin/withdrawals/:id/approve', authMiddleware.requireAdmin, financeController.approveWithdrawal);
router.put('/admin/withdrawals/:id/reject', authMiddleware.requireAdmin, financeController.rejectWithdrawal);
router.get('/admin/stats', authMiddleware.requireAdmin, financeController.getPlatformStats);

export default router;