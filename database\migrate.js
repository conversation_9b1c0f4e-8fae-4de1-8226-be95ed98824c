#!/usr/bin/env node

/**
 * AIGC Service Hub 数据库迁移管理工具
 * 支持迁移执行、回滚、状态查看等功能
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
const crypto = require('crypto');

// 配置
const config = {
  database: {
    host: process.env.POSTGRES_HOST || 'localhost',
    port: parseInt(process.env.POSTGRES_PORT) || 5432,
    database: process.env.POSTGRES_DB || 'aigc_service_hub',
    user: process.env.POSTGRES_USER || 'postgres',
    password: process.env.POSTGRES_PASSWORD || 'postgres123',
  },
  migrationsDir: path.join(__dirname, 'migrations'),
  migrationsTable: 'migration_history',
};

// 创建数据库连接池
const pool = new Pool(config.database);

// 日志函数
const log = (message) => {
  console.log(`[${new Date().toISOString()}] ${message}`);
};

const error = (message) => {
  console.error(`[${new Date().toISOString()}] ERROR: ${message}`);
};

// 迁移管理器类
class MigrationManager {
  constructor() {
    this.pool = pool;
    this.migrationsDir = config.migrationsDir;
    this.migrationsTable = config.migrationsTable;
  }

  // 初始化迁移表
  async initMigrationTable() {
    const client = await this.pool.connect();
    try {
      await client.query(`
        CREATE TABLE IF NOT EXISTS ${this.migrationsTable} (
          id SERIAL PRIMARY KEY,
          version VARCHAR(50) NOT NULL,
          name VARCHAR(255) NOT NULL,
          executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          execution_time_ms INTEGER,
          success BOOLEAN DEFAULT TRUE,
          error_message TEXT,
          checksum VARCHAR(64),
          UNIQUE(version)
        )
      `);
      log('迁移表初始化完成');
    } catch (err) {
      error(`迁移表初始化失败: ${err.message}`);
      throw err;
    } finally {
      client.release();
    }
  }

  // 获取所有迁移文件
  getMigrationFiles() {
    if (!fs.existsSync(this.migrationsDir)) {
      fs.mkdirSync(this.migrationsDir, { recursive: true });
    }

    const files = fs.readdirSync(this.migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();

    return files.map(file => {
      const filePath = path.join(this.migrationsDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      const version = file.replace('.sql', '');
      const checksum = crypto.createHash('md5').update(content).digest('hex');
      
      return {
        version,
        name: file,
        filePath,
        content,
        checksum,
      };
    });
  }

  // 获取已执行的迁移
  async getExecutedMigrations() {
    const client = await this.pool.connect();
    try {
      const result = await client.query(
        `SELECT version, name, executed_at, success, checksum FROM ${this.migrationsTable} ORDER BY version`
      );
      return result.rows;
    } catch (err) {
      error(`获取已执行迁移失败: ${err.message}`);
      throw err;
    } finally {
      client.release();
    }
  }

  // 获取待执行的迁移
  async getPendingMigrations() {
    const allMigrations = this.getMigrationFiles();
    const executedMigrations = await this.getExecutedMigrations();
    const executedVersions = new Set(executedMigrations.map(m => m.version));

    return allMigrations.filter(migration => !executedVersions.has(migration.version));
  }

  // 执行单个迁移
  async executeMigration(migration) {
    const client = await this.pool.connect();
    const startTime = Date.now();
    
    try {
      log(`开始执行迁移: ${migration.name}`);
      
      // 开始事务
      await client.query('BEGIN');
      
      // 执行迁移SQL
      await client.query(migration.content);
      
      // 记录迁移历史
      const executionTime = Date.now() - startTime;
      await client.query(
        `INSERT INTO ${this.migrationsTable} (version, name, execution_time_ms, success, checksum) 
         VALUES ($1, $2, $3, $4, $5)`,
        [migration.version, migration.name, executionTime, true, migration.checksum]
      );
      
      // 提交事务
      await client.query('COMMIT');
      
      log(`迁移执行成功: ${migration.name} (${executionTime}ms)`);
      return true;
      
    } catch (err) {
      // 回滚事务
      await client.query('ROLLBACK');
      
      // 记录失败的迁移
      const executionTime = Date.now() - startTime;
      try {
        await client.query(
          `INSERT INTO ${this.migrationsTable} (version, name, execution_time_ms, success, error_message, checksum) 
           VALUES ($1, $2, $3, $4, $5, $6)`,
          [migration.version, migration.name, executionTime, false, err.message, migration.checksum]
        );
      } catch (insertErr) {
        error(`记录失败迁移时出错: ${insertErr.message}`);
      }
      
      error(`迁移执行失败: ${migration.name} - ${err.message}`);
      throw err;
      
    } finally {
      client.release();
    }
  }

  // 执行所有待执行的迁移
  async runMigrations() {
    await this.initMigrationTable();
    
    const pendingMigrations = await this.getPendingMigrations();
    
    if (pendingMigrations.length === 0) {
      log('没有待执行的迁移');
      return;
    }
    
    log(`发现 ${pendingMigrations.length} 个待执行的迁移`);
    
    for (const migration of pendingMigrations) {
      await this.executeMigration(migration);
    }
    
    log('所有迁移执行完成');
  }

  // 回滚迁移
  async rollbackMigration(version) {
    const client = await this.pool.connect();
    
    try {
      log(`开始回滚迁移: ${version}`);
      
      // 检查迁移是否存在
      const result = await client.query(
        `SELECT * FROM ${this.migrationsTable} WHERE version = $1 AND success = true`,
        [version]
      );
      
      if (result.rows.length === 0) {
        throw new Error(`未找到成功执行的迁移: ${version}`);
      }
      
      // 查找回滚文件
      const rollbackFile = path.join(this.migrationsDir, `${version}_rollback.sql`);
      
      if (!fs.existsSync(rollbackFile)) {
        throw new Error(`回滚文件不存在: ${rollbackFile}`);
      }
      
      const rollbackContent = fs.readFileSync(rollbackFile, 'utf8');
      
      // 开始事务
      await client.query('BEGIN');
      
      // 执行回滚SQL
      await client.query(rollbackContent);
      
      // 删除迁移记录
      await client.query(
        `DELETE FROM ${this.migrationsTable} WHERE version = $1`,
        [version]
      );
      
      // 提交事务
      await client.query('COMMIT');
      
      log(`迁移回滚成功: ${version}`);
      
    } catch (err) {
      // 回滚事务
      await client.query('ROLLBACK');
      error(`迁移回滚失败: ${version} - ${err.message}`);
      throw err;
      
    } finally {
      client.release();
    }
  }

  // 查看迁移状态
  async showStatus() {
    await this.initMigrationTable();
    
    const allMigrations = this.getMigrationFiles();
    const executedMigrations = await this.getExecutedMigrations();
    const executedMap = new Map(executedMigrations.map(m => [m.version, m]));
    
    console.log('\n迁移状态:');
    console.log('========================================');
    
    for (const migration of allMigrations) {
      const executed = executedMap.get(migration.version);
      
      if (executed) {
        const status = executed.success ? '✓' : '✗';
        const time = executed.executed_at ? new Date(executed.executed_at).toLocaleString() : 'N/A';
        console.log(`${status} ${migration.version} - ${migration.name} (${time})`);
      } else {
        console.log(`  ${migration.version} - ${migration.name} (待执行)`);
      }
    }
    
    console.log('========================================\n');
  }

  // 创建新的迁移文件
  async createMigration(name) {
    const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\..+/, '');
    const version = `${timestamp.slice(0, 8)}_${timestamp.slice(8, 14)}`;
    const fileName = `${version}_${name.replace(/\s+/g, '_').toLowerCase()}.sql`;
    const filePath = path.join(this.migrationsDir, fileName);
    
    const template = `-- 迁移文件: ${fileName}
-- 描述: ${name}
-- 版本: ${version}
-- 创建时间: ${new Date().toISOString()}

-- 迁移开始标记
-- Migration: ${version}
-- Description: ${name}

-- 在此处添加您的迁移SQL语句
-- 例如:
-- CREATE TABLE example (
--     id SERIAL PRIMARY KEY,
--     name VARCHAR(255) NOT NULL,
--     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
-- );

-- 记录迁移执行
INSERT INTO migration_history (version, name, execution_time_ms) VALUES 
('${version}', '${fileName}', 0);

-- 迁移完成标记
-- Migration ${version} completed
`;
    
    fs.writeFileSync(filePath, template);
    log(`迁移文件创建成功: ${filePath}`);
    
    // 同时创建回滚文件模板
    const rollbackFileName = `${version}_${name.replace(/\s+/g, '_').toLowerCase()}_rollback.sql`;
    const rollbackFilePath = path.join(this.migrationsDir, rollbackFileName);
    
    const rollbackTemplate = `-- 回滚文件: ${rollbackFileName}
-- 描述: 回滚 ${name}
-- 版本: ${version}
-- 创建时间: ${new Date().toISOString()}

-- 回滚开始标记
-- Rollback: ${version}
-- Description: Rollback ${name}

-- 在此处添加您的回滚SQL语句
-- 例如:
-- DROP TABLE IF EXISTS example;

-- 回滚完成标记
-- Rollback ${version} completed
`;
    
    fs.writeFileSync(rollbackFilePath, rollbackTemplate);
    log(`回滚文件创建成功: ${rollbackFilePath}`);
  }

  // 验证迁移文件完整性
  async validateMigrations() {
    const allMigrations = this.getMigrationFiles();
    const executedMigrations = await this.getExecutedMigrations();
    const executedMap = new Map(executedMigrations.map(m => [m.version, m]));
    
    let hasErrors = false;
    
    for (const migration of allMigrations) {
      const executed = executedMap.get(migration.version);
      
      if (executed && executed.checksum !== migration.checksum) {
        error(`迁移文件已被修改: ${migration.name}`);
        error(`  预期校验和: ${executed.checksum}`);
        error(`  实际校验和: ${migration.checksum}`);
        hasErrors = true;
      }
    }
    
    if (hasErrors) {
      throw new Error('迁移文件完整性验证失败');
    }
    
    log('迁移文件完整性验证通过');
  }

  // 关闭数据库连接
  async close() {
    await this.pool.end();
  }
}

// 命令行处理
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  const migrationManager = new MigrationManager();
  
  try {
    switch (command) {
      case 'up':
      case 'run':
        await migrationManager.runMigrations();
        break;
        
      case 'down':
      case 'rollback':
        const version = args[1];
        if (!version) {
          error('请指定要回滚的迁移版本');
          process.exit(1);
        }
        await migrationManager.rollbackMigration(version);
        break;
        
      case 'status':
        await migrationManager.showStatus();
        break;
        
      case 'create':
        const name = args.slice(1).join(' ');
        if (!name) {
          error('请指定迁移名称');
          process.exit(1);
        }
        await migrationManager.createMigration(name);
        break;
        
      case 'validate':
        await migrationManager.validateMigrations();
        break;
        
      case 'init':
        await migrationManager.initMigrationTable();
        break;
        
      default:
        console.log(`
使用方法: node migrate.js <command> [args]

命令:
  up, run              执行所有待执行的迁移
  down, rollback <ver> 回滚指定版本的迁移
  status               显示迁移状态
  create <name>        创建新的迁移文件
  validate             验证迁移文件完整性
  init                 初始化迁移表
  
示例:
  node migrate.js up
  node migrate.js status
  node migrate.js create "add user table"
  node migrate.js rollback 001_initial_schema
  node migrate.js validate
        `);
        break;
    }
  } catch (err) {
    error(err.message);
    process.exit(1);
  } finally {
    await migrationManager.close();
  }
}

// 如果直接运行此文件，则执行main函数
if (require.main === module) {
  main();
}

module.exports = MigrationManager;