export interface AdminFilters {
    page: number;
    limit: number;
    search?: string;
    role?: string;
    status?: string;
    category?: string;
    type?: string;
}
export declare class AdminService {
    constructor();
    getUsers(filters: AdminFilters): Promise<{
        users: any[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getUserById(userId: number): Promise<any>;
    updateUser(userId: number, updateData: any): Promise<any>;
    deleteUser(userId: number): Promise<void>;
    activateUser(userId: number): Promise<void>;
    deactivateUser(userId: number): Promise<void>;
    getAssets(filters: AdminFilters): Promise<{
        assets: any[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getAssetById(assetId: number): Promise<any>;
    updateAsset(assetId: number, updateData: any): Promise<any>;
    deleteAsset(assetId: number): Promise<void>;
    approveAsset(assetId: number): Promise<void>;
    rejectAsset(assetId: number, reason: string): Promise<void>;
    getTransactions(filters: AdminFilters): Promise<{
        transactions: never[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getTransactionById(transactionId: number): Promise<null>;
    refundTransaction(transactionId: number, reason: string): Promise<void>;
    getWithdrawals(filters: AdminFilters): Promise<{
        withdrawals: never[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getWithdrawalById(withdrawalId: number): Promise<null>;
    approveWithdrawal(withdrawalId: number): Promise<void>;
    rejectWithdrawal(withdrawalId: number, reason: string): Promise<void>;
    getOverviewStats(): Promise<{
        totalUsers: number;
        totalAssets: number;
        totalTransactions: number;
        totalRevenue: number;
    }>;
    getUserStats(): Promise<{
        totalUsers: number;
        activeUsers: number;
        newUsersThisMonth: number;
    }>;
    getAssetStats(): Promise<{
        totalAssets: number;
        approvedAssets: number;
        pendingAssets: number;
    }>;
    getTransactionStats(): Promise<{
        totalTransactions: number;
        successfulTransactions: number;
        failedTransactions: number;
    }>;
    getRevenueStats(): Promise<{
        totalRevenue: number;
        monthlyRevenue: number;
        yearlyRevenue: number;
    }>;
    getSettings(): Promise<{
        siteName: string;
        maintenanceMode: boolean;
        registrationEnabled: boolean;
    }>;
    updateSettings(settings: any): Promise<any>;
    getSecurityLogs(filters: AdminFilters): Promise<{
        logs: never[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getErrorLogs(filters: AdminFilters): Promise<{
        logs: never[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getAuditLogs(filters: AdminFilters): Promise<{
        logs: never[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getSystemHealth(): Promise<{
        database: string;
        redis: string;
        storage: string;
    }>;
    getSystemMetrics(): Promise<{
        cpuUsage: number;
        memoryUsage: number;
        diskUsage: number;
    }>;
    getReports(filters: AdminFilters): Promise<{
        reports: never[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    resolveReport(reportId: number, action: string, reason: string): Promise<void>;
    bulkActivateUsers(userIds: number[]): Promise<void>;
    bulkDeactivateUsers(userIds: number[]): Promise<void>;
    bulkApproveAssets(assetIds: number[]): Promise<void>;
    bulkRejectAssets(assetIds: number[], reason: string): Promise<void>;
}
//# sourceMappingURL=admin.service.d.ts.map