"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const admin_controller_1 = require("../controllers/admin.controller");
const auth_middleware_1 = __importDefault(require("../middlewares/auth.middleware"));
const router = (0, express_1.Router)();
const adminController = new admin_controller_1.AdminController();
router.use(auth_middleware_1.default.authenticate);
router.use(auth_middleware_1.default.requireAdmin);
router.get('/users', adminController.getUsers);
router.get('/users/:userId', adminController.getUserById);
router.put('/users/:userId', adminController.updateUser);
router.delete('/users/:userId', adminController.deleteUser);
router.post('/users/:userId/activate', adminController.activateUser);
router.post('/users/:userId/deactivate', adminController.deactivateUser);
router.get('/assets', adminController.getAssets);
router.get('/assets/:assetId', adminController.getAssetById);
router.put('/assets/:assetId', adminController.updateAsset);
router.delete('/assets/:assetId', adminController.deleteAsset);
router.post('/assets/:assetId/approve', adminController.approveAsset);
router.post('/assets/:assetId/reject', adminController.rejectAsset);
router.get('/transactions', adminController.getTransactions);
router.get('/transactions/:transactionId', adminController.getTransactionById);
router.post('/transactions/:transactionId/refund', adminController.refundTransaction);
router.get('/withdrawals', adminController.getWithdrawals);
router.get('/withdrawals/:withdrawalId', adminController.getWithdrawalById);
router.post('/withdrawals/:withdrawalId/approve', adminController.approveWithdrawal);
router.post('/withdrawals/:withdrawalId/reject', adminController.rejectWithdrawal);
router.get('/stats/overview', adminController.getOverviewStats);
router.get('/stats/users', adminController.getUserStats);
router.get('/stats/assets', adminController.getAssetStats);
router.get('/stats/transactions', adminController.getTransactionStats);
router.get('/stats/revenue', adminController.getRevenueStats);
router.get('/settings', adminController.getSettings);
router.put('/settings', adminController.updateSettings);
router.get('/logs/security', adminController.getSecurityLogs);
router.get('/logs/error', adminController.getErrorLogs);
router.get('/logs/audit', adminController.getAuditLogs);
router.get('/health', adminController.getSystemHealth);
router.get('/metrics', adminController.getSystemMetrics);
router.get('/reports', adminController.getReports);
router.post('/reports/:reportId/resolve', adminController.resolveReport);
router.post('/bulk/users/activate', adminController.bulkActivateUsers);
router.post('/bulk/users/deactivate', adminController.bulkDeactivateUsers);
router.post('/bulk/assets/approve', adminController.bulkApproveAssets);
router.post('/bulk/assets/reject', adminController.bulkRejectAssets);
exports.default = router;
//# sourceMappingURL=admin.routes.js.map