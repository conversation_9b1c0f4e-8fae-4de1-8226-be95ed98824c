import { Request, Response } from 'express';
import { TransactionService } from '@/services/transaction.service';
import { PurchaseRequest, TransactionQuery, Currency } from '@/types';
import { ValidationError } from '@/utils/errors';
import { asyncHandler } from '@/utils/errors';
import { logger, logBusinessOperation } from '@/utils/logger';

export class TransactionController {
  private transactionService: TransactionService;

  constructor() {
    this.transactionService = new TransactionService();
  }

  // 创建购买交易
  createPurchaseTransaction = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const purchaseData: PurchaseRequest = req.body;
    const transaction = await this.transactionService.createPurchaseTransaction(req.user.id, purchaseData);

    res.status(201).json({
      success: true,
      data: { transaction },
      message: 'Purchase transaction created successfully',
    });
  });

  // 确认购买
  confirmPurchase = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const { transactionId, paypalTransactionId } = req.body;
    
    if (!transactionId) {
      throw new ValidationError('Transaction ID is required');
    }

    const transaction = await this.transactionService.confirmPurchase(transactionId, paypalTransactionId);

    res.json({
      success: true,
      data: { transaction },
      message: 'Purchase confirmed successfully',
    });
  });

  // 取消购买
  cancelPurchase = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const { transactionId } = req.body;
    
    if (!transactionId) {
      throw new ValidationError('Transaction ID is required');
    }

    await this.transactionService.cancelTransaction(transactionId);

    res.json({
      success: true,
      message: 'Purchase cancelled successfully',
    });
  });

  // 获取我的交易
  getMyTransactions = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const query: TransactionQuery = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      currency: req.query.currency as Currency,
      status: req.query.status as any,
      startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
      endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
    };

    const result = await this.transactionService.getUserTransactions(req.user.id, query);

    res.json({
      success: true,
      data: result,
      message: 'Transactions retrieved successfully',
    });
  });

  // 获取交易详情
  getTransactionById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const transactionId = parseInt(req.params.id);
    
    if (isNaN(transactionId)) {
      throw new ValidationError('Invalid transaction ID');
    }

    const transaction = await this.transactionService.getTransactionById(transactionId);

    res.json({
      success: true,
      data: { transaction },
      message: 'Transaction details retrieved successfully',
    });
  });

  // 获取交易状态
  getTransactionStatus = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const transactionId = parseInt(req.params.id);
    
    if (isNaN(transactionId)) {
      throw new ValidationError('Invalid transaction ID');
    }

    const transaction = await this.transactionService.getTransactionById(transactionId);

    res.json({
      success: true,
      data: { 
        id: transaction.id,
        status: transaction.status,
        createdAt: transaction.createdAt,
        completedAt: transaction.completedAt,
      },
      message: 'Transaction status retrieved successfully',
    });
  });

  // 获取所有交易（管理员）
  getAllTransactions = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const query: TransactionQuery = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      currency: req.query.currency as Currency,
      status: req.query.status as any,
      startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
      endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
    };

    const result = await this.transactionService.getAllTransactions(query);

    res.json({
      success: true,
      data: result,
      message: 'All transactions retrieved successfully',
    });
  });

  // 更新交易状态（管理员）
  updateTransactionStatus = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const transactionId = parseInt(req.params.id);
    const { status } = req.body;
    
    if (isNaN(transactionId)) {
      throw new ValidationError('Invalid transaction ID');
    }

    if (!status) {
      throw new ValidationError('Status is required');
    }

    await this.transactionService.updateTransactionStatus(transactionId, status);

    res.json({
      success: true,
      message: 'Transaction status updated successfully',
    });
  });
}

export default TransactionController;