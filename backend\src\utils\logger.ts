import winston from 'winston';
import config from '@/config';

// 创建Winston日志记录器
const logger = winston.createLogger({
  level: config.logging.level,
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'aigc-service-hub' },
  transports: [
    // 错误日志文件
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),
    // 综合日志文件
    new winston.transports.File({
      filename: config.logging.file,
      maxsize: 5242880, // 5MB
      maxFiles: config.logging.maxFiles,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),
  ],
});

// 开发环境添加控制台输出
if (config.logging.enableConsole) {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ level, message, timestamp, ...meta }) => {
        return `${timestamp} [${level}]: ${message} ${
          Object.keys(meta).length ? JSON.stringify(meta) : ''
        }`;
      })
    )
  }));
}

// 创建不同级别的日志方法
export const logError = (message: string, error?: Error, meta?: any) => {
  logger.error(message, { error: error?.stack || error, ...meta });
};

export const logWarn = (message: string, meta?: any) => {
  logger.warn(message, meta);
};

export const logInfo = (message: string, meta?: any) => {
  logger.info(message, meta);
};

export const logDebug = (message: string, meta?: any) => {
  logger.debug(message, meta);
};

// 创建请求日志中间件
export const createRequestLogger = () => {
  return (req: any, res: any, next: any) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      const logData = {
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        duration: `${duration}ms`,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        userId: req.user?.id || 'anonymous',
      };
      
      if (res.statusCode >= 400) {
        logger.warn('HTTP Request', logData);
      } else {
        logger.info('HTTP Request', logData);
      }
    });
    
    next();
  };
};

// 创建错误日志中间件
export const createErrorLogger = () => {
  return (error: any, req: any, res: any, next: any) => {
    const logData = {
      error: error.stack || error,
      method: req.method,
      url: req.url,
      statusCode: error.statusCode || 500,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.id || 'anonymous',
      body: req.body,
      query: req.query,
      params: req.params,
    };
    
    logger.error('HTTP Error', logData);
    next(error);
  };
};

// 数据库操作日志
export const logDatabaseOperation = (operation: string, table: string, meta?: any) => {
  logger.debug('Database Operation', {
    operation,
    table,
    ...meta,
  });
};

// 业务操作日志
export const logBusinessOperation = (operation: string, userId: number, meta?: any) => {
  logger.info('Business Operation', {
    operation,
    userId,
    ...meta,
  });
};

// 安全事件日志
export const logSecurityEvent = (event: string, userId?: number, meta?: any) => {
  logger.warn('Security Event', {
    event,
    userId,
    timestamp: new Date().toISOString(),
    ...meta,
  });
};

// 性能监控日志
export const logPerformance = (operation: string, duration: number, meta?: any) => {
  const level = duration > 5000 ? 'warn' : duration > 1000 ? 'info' : 'debug';
  logger.log(level, 'Performance', {
    operation,
    duration: `${duration}ms`,
    ...meta,
  });
};

// 财务操作日志
export const logFinancialOperation = (operation: string, amount: number, userId: number, meta?: any) => {
  logger.info('Financial Operation', {
    operation,
    amount,
    userId,
    timestamp: new Date().toISOString(),
    ...meta,
  });
};

// 文件操作日志
export const logFileOperation = (operation: string, fileKey: string, userId: number, meta?: any) => {
  logger.info('File Operation', {
    operation,
    fileKey,
    userId,
    timestamp: new Date().toISOString(),
    ...meta,
  });
};

// 缓存操作日志
export const logCacheOperation = (operation: string, key: string, hit?: boolean, meta?: any) => {
  logger.debug('Cache Operation', {
    operation,
    key,
    hit,
    ...meta,
  });
};

// 第三方服务日志
export const logThirdPartyService = (service: string, operation: string, success: boolean, meta?: any) => {
  const level = success ? 'info' : 'error';
  logger.log(level, 'Third Party Service', {
    service,
    operation,
    success,
    timestamp: new Date().toISOString(),
    ...meta,
  });
};

// 导出主要的logger实例
export { logger };
export default logger;