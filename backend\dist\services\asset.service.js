"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetService = void 0;
const connection_1 = require("../database/connection");
const types_1 = require("../types");
const errors_1 = require("../utils/errors");
const logger_1 = require("../utils/logger");
class AssetService {
    constructor() {
        this.db = (0, connection_1.getDb)();
    }
    async createAsset(creatorId, assetData) {
        const client = await this.db.connect();
        try {
            await client.query('BEGIN');
            const { title, description, assetType, priceUsd, pricePoints, categories, styles, coverImageUrl } = assetData;
            const assetQuery = `
        INSERT INTO assets (creator_id, title, description, asset_type, price_usd, price_points, 
                           s3_file_key, cover_image_url, status)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING id, creator_id, title, description, asset_type, price_usd, price_points, 
                  s3_file_key, cover_image_url, status, download_count, file_size, 
                  created_at, published_at, updated_at
      `;
            const assetResult = await client.query(assetQuery, [
                creatorId,
                title,
                description,
                assetType,
                priceUsd,
                pricePoints,
                '',
                coverImageUrl,
                types_1.AssetStatus.DRAFT
            ]);
            const asset = assetResult.rows[0];
            if (categories && categories.length > 0) {
                await this.createOrGetTags(client, categories, types_1.TagType.CATEGORY);
                await this.linkAssetTags(client, asset.id, categories);
            }
            if (styles && styles.length > 0) {
                await this.createOrGetTags(client, styles, types_1.TagType.STYLE);
                await this.linkAssetTags(client, asset.id, styles);
            }
            await client.query('COMMIT');
            (0, logger_1.logBusinessOperation)('ASSET_CREATE', creatorId, { assetId: asset.id, title });
            return {
                id: asset.id,
                creatorId: asset.creator_id,
                title: asset.title,
                description: asset.description,
                assetType: asset.asset_type,
                priceUsd: asset.price_usd,
                pricePoints: asset.price_points,
                s3FileKey: asset.s3_file_key,
                coverImageUrl: asset.cover_image_url,
                status: asset.status,
                downloadCount: asset.download_count,
                fileSize: asset.file_size,
                createdAt: asset.created_at,
                publishedAt: asset.published_at,
                updatedAt: asset.updated_at,
            };
        }
        catch (error) {
            await client.query('ROLLBACK');
            logger_1.logger.error('Failed to create asset:', error);
            throw error;
        }
        finally {
            client.release();
        }
    }
    async updateAsset(assetId, creatorId, updates) {
        const client = await this.db.connect();
        try {
            await client.query('BEGIN');
            const ownershipResult = await client.query('SELECT creator_id FROM assets WHERE id = $1', [assetId]);
            if (ownershipResult.rows.length === 0) {
                throw new errors_1.AssetNotFoundError();
            }
            if (ownershipResult.rows[0].creator_id !== creatorId) {
                throw new errors_1.AuthorizationError('You can only update your own assets');
            }
            const updateFields = [];
            const values = [];
            let paramIndex = 1;
            const allowedUpdates = ['title', 'description', 'priceUsd', 'pricePoints', 'coverImageUrl'];
            for (const [key, value] of Object.entries(updates)) {
                if (allowedUpdates.includes(key) && value !== undefined) {
                    const dbField = key.replace(/([A-Z])/g, '_$1').toLowerCase();
                    updateFields.push(`${dbField} = $${paramIndex}`);
                    values.push(value);
                    paramIndex++;
                }
            }
            if (updateFields.length === 0) {
                throw new errors_1.ValidationError('No valid fields to update');
            }
            updateFields.push('updated_at = CURRENT_TIMESTAMP');
            values.push(assetId);
            const updateQuery = `
        UPDATE assets 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, creator_id, title, description, asset_type, price_usd, price_points, 
                  s3_file_key, cover_image_url, status, download_count, file_size, 
                  created_at, published_at, updated_at
      `;
            const result = await client.query(updateQuery, values);
            const asset = result.rows[0];
            if (updates.categories) {
                await this.updateAssetTags(client, assetId, updates.categories, types_1.TagType.CATEGORY);
            }
            if (updates.styles) {
                await this.updateAssetTags(client, assetId, updates.styles, types_1.TagType.STYLE);
            }
            await client.query('COMMIT');
            (0, logger_1.logBusinessOperation)('ASSET_UPDATE', creatorId, { assetId, updates });
            return {
                id: asset.id,
                creatorId: asset.creator_id,
                title: asset.title,
                description: asset.description,
                assetType: asset.asset_type,
                priceUsd: asset.price_usd,
                pricePoints: asset.price_points,
                s3FileKey: asset.s3_file_key,
                coverImageUrl: asset.cover_image_url,
                status: asset.status,
                downloadCount: asset.download_count,
                fileSize: asset.file_size,
                createdAt: asset.created_at,
                publishedAt: asset.published_at,
                updatedAt: asset.updated_at,
            };
        }
        catch (error) {
            await client.query('ROLLBACK');
            logger_1.logger.error('Failed to update asset:', error);
            throw error;
        }
        finally {
            client.release();
        }
    }
    async updateAssetFile(assetId, creatorId, s3FileKey, fileSize) {
        try {
            const query = `
        UPDATE assets 
        SET s3_file_key = $1, file_size = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $3 AND creator_id = $4
      `;
            const result = await this.db.query(query, [s3FileKey, fileSize, assetId, creatorId]);
            if (result.rowCount === 0) {
                throw new errors_1.AssetNotFoundError();
            }
            (0, logger_1.logBusinessOperation)('ASSET_FILE_UPDATE', creatorId, { assetId, s3FileKey, fileSize });
        }
        catch (error) {
            logger_1.logger.error('Failed to update asset file:', error);
            throw error;
        }
    }
    async publishAsset(assetId, creatorId) {
        try {
            const query = `
        UPDATE assets 
        SET status = $1, published_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2 AND creator_id = $3 AND status = $4
        RETURNING id, creator_id, title, description, asset_type, price_usd, price_points, 
                  s3_file_key, cover_image_url, status, download_count, file_size, 
                  created_at, published_at, updated_at
      `;
            const result = await this.db.query(query, [types_1.AssetStatus.PUBLISHED, assetId, creatorId, types_1.AssetStatus.DRAFT]);
            if (result.rows.length === 0) {
                throw new errors_1.AssetNotFoundError();
            }
            const asset = result.rows[0];
            (0, logger_1.logBusinessOperation)('ASSET_PUBLISH', creatorId, { assetId });
            return {
                id: asset.id,
                creatorId: asset.creator_id,
                title: asset.title,
                description: asset.description,
                assetType: asset.asset_type,
                priceUsd: asset.price_usd,
                pricePoints: asset.price_points,
                s3FileKey: asset.s3_file_key,
                coverImageUrl: asset.cover_image_url,
                status: asset.status,
                downloadCount: asset.download_count,
                fileSize: asset.file_size,
                createdAt: asset.created_at,
                publishedAt: asset.published_at,
                updatedAt: asset.updated_at,
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to publish asset:', error);
            throw error;
        }
    }
    async getAssetById(assetId, includePrivate = false) {
        try {
            const query = `
        SELECT 
          a.id, a.creator_id, a.title, a.description, a.asset_type, a.price_usd, a.price_points, 
          a.s3_file_key, a.cover_image_url, a.status, a.download_count, a.file_size, 
          a.created_at, a.published_at, a.updated_at,
          u.display_name as creator_name,
          u.user_role as creator_role
        FROM assets a
        JOIN users u ON a.creator_id = u.id
        WHERE a.id = $1 ${includePrivate ? '' : "AND a.status = 'PUBLISHED'"}
      `;
            const result = await this.db.query(query, [assetId]);
            if (result.rows.length === 0) {
                throw new errors_1.AssetNotFoundError();
            }
            const asset = result.rows[0];
            const tagsQuery = `
        SELECT t.id, t.name, t.type
        FROM tags t
        JOIN asset_tags at ON t.id = at.tag_id
        WHERE at.asset_id = $1
      `;
            const tagsResult = await this.db.query(tagsQuery, [assetId]);
            const tags = tagsResult.rows.map(row => ({
                id: row.id,
                name: row.name,
                type: row.type,
                createdAt: new Date(),
            }));
            return {
                id: asset.id,
                creatorId: asset.creator_id,
                title: asset.title,
                description: asset.description,
                assetType: asset.asset_type,
                priceUsd: asset.price_usd,
                pricePoints: asset.price_points,
                s3FileKey: asset.s3_file_key,
                coverImageUrl: asset.cover_image_url,
                status: asset.status,
                downloadCount: asset.download_count,
                fileSize: asset.file_size,
                createdAt: asset.created_at,
                publishedAt: asset.published_at,
                updatedAt: asset.updated_at,
                tags,
                creator: {
                    name: asset.creator_name,
                    role: asset.creator_role,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get asset by ID:', error);
            throw error;
        }
    }
    async getAssetList(query) {
        try {
            const { page = 1, limit = 20, category, style, assetType, sortBy = 'created_at', sortOrder = 'desc', search } = query;
            const offset = (page - 1) * limit;
            const conditions = ["a.status = 'PUBLISHED'"];
            const params = [];
            let paramIndex = 1;
            if (category) {
                conditions.push(`EXISTS (
          SELECT 1 FROM asset_tags at 
          JOIN tags t ON at.tag_id = t.id 
          WHERE at.asset_id = a.id AND t.name = $${paramIndex} AND t.type = 'CATEGORY'
        )`);
                params.push(category);
                paramIndex++;
            }
            if (style) {
                conditions.push(`EXISTS (
          SELECT 1 FROM asset_tags at 
          JOIN tags t ON at.tag_id = t.id 
          WHERE at.asset_id = a.id AND t.name = $${paramIndex} AND t.type = 'STYLE'
        )`);
                params.push(style);
                paramIndex++;
            }
            if (assetType) {
                conditions.push(`a.asset_type = $${paramIndex}`);
                params.push(assetType);
                paramIndex++;
            }
            if (search) {
                conditions.push(`(a.title ILIKE $${paramIndex} OR a.description ILIKE $${paramIndex})`);
                params.push(`%${search}%`);
                paramIndex++;
            }
            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
            const orderClause = `ORDER BY a.${sortBy} ${sortOrder.toUpperCase()}`;
            const assetsQuery = `
        SELECT 
          a.id, a.creator_id, a.title, a.description, a.asset_type, a.price_usd, a.price_points, 
          a.cover_image_url, a.download_count, a.created_at, a.published_at,
          u.display_name as creator_name
        FROM assets a
        JOIN users u ON a.creator_id = u.id
        ${whereClause}
        ${orderClause}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
            const countQuery = `
        SELECT COUNT(*) as total
        FROM assets a
        ${whereClause}
      `;
            params.push(limit, offset);
            const [assetsResult, countResult] = await Promise.all([
                this.db.query(assetsQuery, params),
                this.db.query(countQuery, params.slice(0, -2))
            ]);
            const assets = assetsResult.rows.map(row => ({
                id: row.id,
                creatorId: row.creator_id,
                title: row.title,
                description: row.description,
                assetType: row.asset_type,
                priceUsd: row.price_usd,
                pricePoints: row.price_points,
                coverImageUrl: row.cover_image_url,
                downloadCount: row.download_count,
                createdAt: row.created_at,
                publishedAt: row.published_at,
                creatorName: row.creator_name,
            }));
            const total = parseInt(countResult.rows[0].total);
            const totalPages = Math.ceil(total / limit);
            return {
                assets,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get asset list:', error);
            throw error;
        }
    }
    async getCreatorAssets(creatorId, query) {
        try {
            const { page = 1, limit = 20, assetType, sortBy = 'created_at', sortOrder = 'desc' } = query;
            const offset = (page - 1) * limit;
            const conditions = ['a.creator_id = $1'];
            const params = [creatorId];
            let paramIndex = 2;
            if (assetType) {
                conditions.push(`a.asset_type = $${paramIndex}`);
                params.push(assetType);
                paramIndex++;
            }
            const whereClause = `WHERE ${conditions.join(' AND ')}`;
            const orderClause = `ORDER BY a.${sortBy} ${sortOrder.toUpperCase()}`;
            const assetsQuery = `
        SELECT 
          a.id, a.creator_id, a.title, a.description, a.asset_type, a.price_usd, a.price_points, 
          a.cover_image_url, a.status, a.download_count, a.file_size, a.created_at, a.published_at, a.updated_at
        FROM assets a
        ${whereClause}
        ${orderClause}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
            const countQuery = `
        SELECT COUNT(*) as total
        FROM assets a
        ${whereClause}
      `;
            params.push(limit, offset);
            const [assetsResult, countResult] = await Promise.all([
                this.db.query(assetsQuery, params),
                this.db.query(countQuery, params.slice(0, -2))
            ]);
            const assets = assetsResult.rows.map(row => ({
                id: row.id,
                creatorId: row.creator_id,
                title: row.title,
                description: row.description,
                assetType: row.asset_type,
                priceUsd: row.price_usd,
                pricePoints: row.price_points,
                coverImageUrl: row.cover_image_url,
                status: row.status,
                downloadCount: row.download_count,
                fileSize: row.file_size,
                createdAt: row.created_at,
                publishedAt: row.published_at,
                updatedAt: row.updated_at,
            }));
            const total = parseInt(countResult.rows[0].total);
            const totalPages = Math.ceil(total / limit);
            return {
                assets,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get creator assets:', error);
            throw error;
        }
    }
    async getAssetStats(assetId) {
        try {
            const query = `
        SELECT 
          a.download_count,
          COUNT(t.id) as total_sales,
          COALESCE(SUM(CASE WHEN t.currency = 'USD' THEN t.amount_usd ELSE 0 END), 0) as total_usd_revenue,
          COALESCE(SUM(CASE WHEN t.currency = 'POINTS' THEN t.amount_points ELSE 0 END), 0) as total_points_revenue
        FROM assets a
        LEFT JOIN transactions t ON a.id = t.asset_id AND t.status = 'COMPLETED'
        WHERE a.id = $1
        GROUP BY a.id, a.download_count
      `;
            const result = await this.db.query(query, [assetId]);
            if (result.rows.length === 0) {
                throw new errors_1.AssetNotFoundError();
            }
            const stats = result.rows[0];
            return {
                totalSales: parseInt(stats.total_sales),
                totalUsdRevenue: parseFloat(stats.total_usd_revenue),
                totalPointsRevenue: parseInt(stats.total_points_revenue),
                downloadCount: stats.download_count,
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get asset stats:', error);
            throw error;
        }
    }
    async incrementDownloadCount(assetId) {
        try {
            const query = `
        UPDATE assets 
        SET download_count = download_count + 1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `;
            await this.db.query(query, [assetId]);
        }
        catch (error) {
            logger_1.logger.error('Failed to increment download count:', error);
            throw error;
        }
    }
    async deleteAsset(assetId, creatorId) {
        try {
            const query = `
        UPDATE assets 
        SET status = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2 AND creator_id = $3
      `;
            const result = await this.db.query(query, [types_1.AssetStatus.ARCHIVED, assetId, creatorId]);
            if (result.rowCount === 0) {
                throw new errors_1.AssetNotFoundError();
            }
            (0, logger_1.logBusinessOperation)('ASSET_DELETE', creatorId, { assetId });
        }
        catch (error) {
            logger_1.logger.error('Failed to delete asset:', error);
            throw error;
        }
    }
    async getAllTags() {
        try {
            const query = `
        SELECT id, name, type, created_at
        FROM tags
        ORDER BY type, name
      `;
            const result = await this.db.query(query);
            return result.rows.map(row => ({
                id: row.id,
                name: row.name,
                type: row.type,
                createdAt: row.created_at,
            }));
        }
        catch (error) {
            logger_1.logger.error('Failed to get all tags:', error);
            throw error;
        }
    }
    async createOrGetTags(client, tagNames, tagType) {
        for (const tagName of tagNames) {
            const existingTag = await client.query('SELECT id FROM tags WHERE name = $1 AND type = $2', [tagName, tagType]);
            if (existingTag.rows.length === 0) {
                await client.query('INSERT INTO tags (name, type) VALUES ($1, $2)', [tagName, tagType]);
            }
        }
    }
    async linkAssetTags(client, assetId, tagNames) {
        for (const tagName of tagNames) {
            const tagResult = await client.query('SELECT id FROM tags WHERE name = $1', [tagName]);
            if (tagResult.rows.length > 0) {
                const tagId = tagResult.rows[0].id;
                await client.query('INSERT INTO asset_tags (asset_id, tag_id) VALUES ($1, $2) ON CONFLICT DO NOTHING', [assetId, tagId]);
            }
        }
    }
    async updateAssetTags(client, assetId, tagNames, tagType) {
        await client.query(`
      DELETE FROM asset_tags 
      WHERE asset_id = $1 AND tag_id IN (
        SELECT id FROM tags WHERE type = $2
      )
    `, [assetId, tagType]);
        await this.createOrGetTags(client, tagNames, tagType);
        await this.linkAssetTags(client, assetId, tagNames);
    }
}
exports.AssetService = AssetService;
exports.default = AssetService;
//# sourceMappingURL=asset.service.js.map