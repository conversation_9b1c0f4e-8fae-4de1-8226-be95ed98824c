import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User, UserRole, LoginForm, RegisterForm, AuthResponse } from '../../types';
import { authAPI } from '../../services/api';
import { STORAGE_KEYS } from '../../constants';

// 异步actions
export const login = createAsyncThunk(
  'auth/login',
  async (credentials: LoginForm, { rejectWithValue }) => {
    try {
      const response = await authAPI.login(credentials);
      // 保存tokens到localStorage
      localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.data.accessToken);
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.data.data.refreshToken);
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(response.data.data.user));
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '登录失败' });
    }
  }
);

export const register = createAsyncThunk(
  'auth/register',
  async (userData: RegisterForm, { rejectWithValue }) => {
    try {
      const response = await authAPI.register(userData);
      localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.data.accessToken);
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.data.data.refreshToken);
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(response.data.data.user));
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '注册失败' });
    }
  }
);

export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { rejectWithValue }) => {
    try {
      const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }
      const response = await authAPI.refreshToken({ refreshToken });
      localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.data.accessToken);
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.data.data.refreshToken);
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '刷新令牌失败' });
    }
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      await authAPI.logout();
      // 清除localStorage
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER_INFO);
      return null;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '退出登录失败' });
    }
  }
);

export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      const response = await authAPI.getCurrentUser();
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(response.data.data));
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取用户信息失败' });
    }
  }
);

// OAuth登录
export const loginWithOAuth = createAsyncThunk(
  'auth/loginWithOAuth',
  async (params: { provider: string; code: string }, { rejectWithValue }) => {
    try {
      const response = await authAPI.oauthLogin(params.provider, params.code);
      localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.data.accessToken);
      localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.data.data.refreshToken);
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(response.data.data.user));
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: 'OAuth登录失败' });
    }
  }
);

// 状态接口
interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  loginAttempts: number;
  lastLoginTime: number | null;
  sessionExpiry: number | null;
}

// 初始状态
const initialState: AuthState = {
  user: null,
  accessToken: localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN),
  refreshToken: localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN),
  isAuthenticated: false,
  isLoading: false,
  error: null,
  loginAttempts: 0,
  lastLoginTime: null,
  sessionExpiry: null,
};

// 检查初始认证状态
const checkInitialAuth = (): Partial<AuthState> => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  const userInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO);
  
  if (token && userInfo) {
    try {
      const user = JSON.parse(userInfo);
      return {
        user,
        accessToken: token,
        isAuthenticated: true,
      };
    } catch (error) {
      // 如果解析失败，清除localStorage
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER_INFO);
    }
  }
  return {};
};

// 创建slice
const authSlice = createSlice({
  name: 'auth',
  initialState: {
    ...initialState,
    ...checkInitialAuth(),
  },
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(state.user));
      }
    },
    setSessionExpiry: (state, action: PayloadAction<number>) => {
      state.sessionExpiry = action.payload;
    },
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0;
    },
  },
  extraReducers: (builder) => {
    // 登录
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.accessToken = action.payload.accessToken;
        state.refreshToken = action.payload.refreshToken;
        state.isAuthenticated = true;
        state.loginAttempts = 0;
        state.lastLoginTime = Date.now();
        state.error = null;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.loginAttempts += 1;
        state.isAuthenticated = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
      });

    // 注册
    builder
      .addCase(register.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.accessToken = action.payload.accessToken;
        state.refreshToken = action.payload.refreshToken;
        state.isAuthenticated = true;
        state.lastLoginTime = Date.now();
        state.error = null;
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 刷新令牌
    builder
      .addCase(refreshToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.isLoading = false;
        state.accessToken = action.payload.accessToken;
        state.refreshToken = action.payload.refreshToken;
        state.error = null;
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
      });

    // 退出登录
    builder
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.isAuthenticated = false;
        state.error = null;
        state.loginAttempts = 0;
        state.lastLoginTime = null;
        state.sessionExpiry = null;
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        // 即使退出登录失败，也要清除本地状态
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.isAuthenticated = false;
      });

    // 获取当前用户
    builder
      .addCase(getCurrentUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(getCurrentUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
      });

    // OAuth登录
    builder
      .addCase(loginWithOAuth.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginWithOAuth.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.accessToken = action.payload.accessToken;
        state.refreshToken = action.payload.refreshToken;
        state.isAuthenticated = true;
        state.lastLoginTime = Date.now();
        state.error = null;
      })
      .addCase(loginWithOAuth.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.isAuthenticated = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
      });
  },
});

// 导出actions
export const { clearError, setLoading, updateUser, setSessionExpiry, resetLoginAttempts } = authSlice.actions;

// 选择器
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectUser = (state: { auth: AuthState }) => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated;
export const selectIsLoading = (state: { auth: AuthState }) => state.auth.isLoading;
export const selectError = (state: { auth: AuthState }) => state.auth.error;
export const selectAccessToken = (state: { auth: AuthState }) => state.auth.accessToken;
export const selectUserRole = (state: { auth: AuthState }) => state.auth.user?.userRole;
export const selectIsCreator = (state: { auth: AuthState }) => {
  const role = state.auth.user?.userRole;
  return role === UserRole.PERSONAL_CREATOR || role === UserRole.ENTERPRISE_CREATOR;
};
export const selectIsAdmin = (state: { auth: AuthState }) => state.auth.user?.userRole === UserRole.ADMIN;

// 导出reducer
export default authSlice.reducer;