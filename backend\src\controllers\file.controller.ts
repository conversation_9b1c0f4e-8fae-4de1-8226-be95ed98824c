import { Request, Response, NextFunction } from 'express';
import { FileService } from '@/services/file.service';
import { 
  ValidationError, 
  NotFoundError, 
  AuthorizationError,
  FileUploadError 
} from '@/utils/errors';
import { logger } from '@/utils/logger';
import { User } from '@/types';

export class FileController {
  private fileService: FileService;

  constructor() {
    this.fileService = new FileService();
  }

  // 上传单个文件
  uploadFile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user as User;
      const file = req.file;

      if (!file) {
        throw new ValidationError('No file uploaded');
      }

      const fileData = {
        originalName: file.originalname,
        fileName: file.filename,
        filePath: file.path,
        fileSize: file.size,
        mimeType: file.mimetype,
        uploadedBy: user.id,
      };

      const uploadedFile = await this.fileService.uploadFile(fileData);

      logger.info(`File uploaded successfully: ${file.originalname}`, {
        userId: user.id,
        fileId: uploadedFile.id,
      });

      res.status(201).json({
        success: true,
        data: uploadedFile,
        message: 'File uploaded successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  // 上传多个文件
  uploadMultipleFiles = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user as User;
      const files = req.files as Express.Multer.File[];

      if (!files || files.length === 0) {
        throw new ValidationError('No files uploaded');
      }

      const fileDataArray = files.map(file => ({
        originalName: file.originalname,
        fileName: file.filename,
        filePath: file.path,
        fileSize: file.size,
        mimeType: file.mimetype,
        uploadedBy: user.id,
      }));

      const uploadedFiles = await this.fileService.uploadMultipleFiles(fileDataArray);

      logger.info(`Multiple files uploaded successfully: ${files.length} files`, {
        userId: user.id,
        fileCount: files.length,
      });

      res.status(201).json({
        success: true,
        data: uploadedFiles,
        message: `${files.length} files uploaded successfully`,
      });
    } catch (error) {
      next(error);
    }
  };

  // 获取文件列表
  getFiles = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user as User;
      const { page = 1, limit = 20, search, fileType } = req.query;

      const filters = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        search: search as string,
        fileType: fileType as string,
        userId: user.userRole === 'ADMIN' ? undefined : user.id,
      };

      const result = await this.fileService.getFiles(filters);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  // 获取用户的文件列表
  getUserFiles = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user as User;
      const { userId } = req.params;
      const { page = 1, limit = 20 } = req.query;

      // 检查权限：只能查看自己的文件或管理员可以查看所有用户的文件
      if (user.userRole !== 'ADMIN' && user.id !== parseInt(userId)) {
        throw new AuthorizationError('You can only view your own files');
      }

      const filters = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        userId: parseInt(userId),
      };

      const result = await this.fileService.getFiles(filters);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  // 下载文件
  downloadFile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user as User;
      const { fileId } = req.params;

      const file = await this.fileService.getFileById(parseInt(fileId));

      if (!file) {
        throw new NotFoundError('File not found');
      }

      // 检查权限：只能下载自己的文件或管理员可以下载所有文件
      if (user.userRole !== 'ADMIN' && file.uploadedBy !== user.id) {
        throw new AuthorizationError('You can only download your own files');
      }

      const filePath = await this.fileService.getFilePath(file.id);

      res.download(filePath, file.originalName, (err) => {
        if (err) {
          logger.error('File download error:', err);
          next(new FileUploadError('Failed to download file'));
        } else {
          logger.info(`File downloaded: ${file.originalName}`, {
            userId: user.id,
            fileId: file.id,
          });
        }
      });
    } catch (error) {
      next(error);
    }
  };

  // 获取文件信息
  getFileInfo = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user as User;
      const { fileId } = req.params;

      const file = await this.fileService.getFileById(parseInt(fileId));

      if (!file) {
        throw new NotFoundError('File not found');
      }

      // 检查权限：只能查看自己的文件或管理员可以查看所有文件
      if (user.userRole !== 'ADMIN' && file.uploadedBy !== user.id) {
        throw new AuthorizationError('You can only view your own files');
      }

      res.json({
        success: true,
        data: file,
      });
    } catch (error) {
      next(error);
    }
  };

  // 删除文件
  deleteFile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user as User;
      const { fileId } = req.params;

      const file = await this.fileService.getFileById(parseInt(fileId));

      if (!file) {
        throw new NotFoundError('File not found');
      }

      // 检查权限：只能删除自己的文件或管理员可以删除所有文件
      if (user.userRole !== 'ADMIN' && file.uploadedBy !== user.id) {
        throw new AuthorizationError('You can only delete your own files');
      }

      await this.fileService.deleteFile(parseInt(fileId));

      logger.info(`File deleted: ${file.originalName}`, {
        userId: user.id,
        fileId: file.id,
      });

      res.json({
        success: true,
        message: 'File deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  // 更新文件信息
  updateFile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user as User;
      const { fileId } = req.params;
      const { description, tags } = req.body;

      const file = await this.fileService.getFileById(parseInt(fileId));

      if (!file) {
        throw new NotFoundError('File not found');
      }

      // 检查权限：只能更新自己的文件或管理员可以更新所有文件
      if (user.userRole !== 'ADMIN' && file.uploadedBy !== user.id) {
        throw new AuthorizationError('You can only update your own files');
      }

      const updatedFile = await this.fileService.updateFile(parseInt(fileId), {
        description,
        tags,
      });

      res.json({
        success: true,
        data: updatedFile,
        message: 'File updated successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  // 获取文件缩略图
  getThumbnail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const user = req.user as User;
      const { fileId } = req.params;

      const file = await this.fileService.getFileById(parseInt(fileId));

      if (!file) {
        throw new NotFoundError('File not found');
      }

      // 检查权限：只能查看自己的文件缩略图或管理员可以查看所有文件
      if (user.userRole !== 'ADMIN' && file.uploadedBy !== user.id) {
        throw new AuthorizationError('You can only view your own files');
      }

      const thumbnailPath = await this.fileService.getThumbnailPath(parseInt(fileId));

      if (!thumbnailPath) {
        throw new NotFoundError('Thumbnail not found');
      }

      res.sendFile(thumbnailPath);
    } catch (error) {
      next(error);
    }
  };
}