{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/utils/errors.ts"], "names": [], "mappings": ";;;AACA,MAAa,QAAS,SAAQ,KAAK;IAMjC,YACE,OAAe,EACf,aAAqB,GAAG,EACxB,OAAe,uBAAuB,EACtC,OAAa,EACb,gBAAyB,IAAI;QAE7B,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAGnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QAGlC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAzBD,4BAyBC;AAGD,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;CACF;AAJD,0CAIC;AAED,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,UAAkB,uBAAuB;QACnD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,sBAAsB,CAAC,CAAC;IAC9C,CAAC;CACF;AAJD,kDAIC;AAED,MAAa,kBAAmB,SAAQ,QAAQ;IAC9C,YAAY,UAAkB,0BAA0B;QACtD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAC;IAC7C,CAAC;CACF;AAJD,gDAIC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,oBAAoB;QAChD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;IACnC,CAAC;CACF;AAJD,sCAIC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;CACF;AAJD,sCAIC;AAED,MAAa,cAAe,SAAQ,QAAQ;IAC1C,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAC;IAC7C,CAAC;CACF;AAJD,wCAIC;AAED,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,UAAkB,uBAAuB;QACnD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,uBAAuB,CAAC,CAAC;IAC/C,CAAC;CACF;AAJD,kDAIC;AAED,MAAa,uBAAwB,SAAQ,QAAQ;IACnD,YAAY,UAAkB,qBAAqB;QACjD,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,qBAAqB,CAAC,CAAC;IAC7C,CAAC;CACF;AAJD,0DAIC;AAGD,MAAa,uBAAwB,SAAQ,mBAAmB;IAC9D;QACE,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACrC,CAAC;CACF;AAJD,0DAIC;AAED,MAAa,iBAAkB,SAAQ,mBAAmB;IACxD;QACE,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAC7B,CAAC;CACF;AAJD,8CAIC;AAED,MAAa,iBAAkB,SAAQ,mBAAmB;IACxD;QACE,KAAK,CAAC,eAAe,CAAC,CAAC;IACzB,CAAC;CACF;AAJD,8CAIC;AAED,MAAa,iBAAkB,SAAQ,aAAa;IAClD;QACE,KAAK,CAAC,gBAAgB,CAAC,CAAC;IAC1B,CAAC;CACF;AAJD,8CAIC;AAED,MAAa,kBAAmB,SAAQ,aAAa;IACnD;QACE,KAAK,CAAC,iBAAiB,CAAC,CAAC;IAC3B,CAAC;CACF;AAJD,gDAIC;AAED,MAAa,wBAAyB,SAAQ,aAAa;IACzD;QACE,KAAK,CAAC,uBAAuB,CAAC,CAAC;IACjC,CAAC;CACF;AAJD,4DAIC;AAED,MAAa,uBAAwB,SAAQ,QAAQ;IACnD,YAAY,QAAgB,EAAE,SAAiB;QAC7C,KAAK,CAAC,kCAAkC,QAAQ,gBAAgB,SAAS,EAAE,EAAE,GAAG,EAAE,qBAAqB,EAAE;YACvG,QAAQ;YACR,SAAS;SACV,CAAC,CAAC;IACL,CAAC;CACF;AAPD,0DAOC;AAED,MAAa,wBAAyB,SAAQ,QAAQ;IACpD,YAAY,QAAgB,EAAE,SAAiB;QAC7C,KAAK,CAAC,mCAAmC,QAAQ,gBAAgB,SAAS,EAAE,EAAE,GAAG,EAAE,sBAAsB,EAAE;YACzG,QAAQ;YACR,SAAS;SACV,CAAC,CAAC;IACL,CAAC;CACF;AAPD,4DAOC;AAED,MAAa,sBAAuB,SAAQ,kBAAkB;IAC5D;QACE,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAC/B,CAAC;CACF;AAJD,wDAIC;AAED,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,mBAAmB,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;CACF;AAJD,0CAIC;AAED,MAAa,sBAAuB,SAAQ,QAAQ;IAClD,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,0BAA0B,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC;CACF;AAJD,wDAIC;AAED,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,kBAAkB,EAAE,OAAO,CAAC,CAAC;IACnD,CAAC;CACF;AAJD,0CAIC;AAED,MAAa,0BAA2B,SAAQ,QAAQ;IACtD,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,8BAA8B,EAAE,OAAO,CAAC,CAAC;IAC/D,CAAC;CACF;AAJD,gEAIC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,gBAAgB,EAAE,OAAO,CAAC,CAAC;IACjD,CAAC;CACF;AAJD,sCAIC;AAED,MAAa,UAAW,SAAQ,QAAQ;IACtC,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;CACF;AAJD,gCAIC;AAED,MAAa,OAAQ,SAAQ,QAAQ;IACnC,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;CACF;AAJD,0BAIC;AAED,MAAa,UAAW,SAAQ,QAAQ;IACtC,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;CACF;AAJD,gCAIC;AAED,MAAa,WAAY,SAAQ,QAAQ;IACvC,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IAC/C,CAAC;CACF;AAJD,kCAIC;AAED,MAAa,UAAW,SAAQ,QAAQ;IACtC,YAAY,OAAe,EAAE,OAAa;QACxC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;CACF;AAJD,gCAIC;AAGM,MAAM,UAAU,GAAG,CAAC,KAAU,EAAqB,EAAE;IAC1D,OAAO,KAAK,YAAY,QAAQ,CAAC;AACnC,CAAC,CAAC;AAFW,QAAA,UAAU,cAErB;AAEK,MAAM,kBAAkB,GAAG,CAAC,KAAU,EAAW,EAAE;IACxD,OAAO,IAAA,kBAAU,EAAC,KAAK,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC;AAClD,CAAC,CAAC;AAFW,QAAA,kBAAkB,sBAE7B;AAGK,MAAM,mBAAmB,GAAG,CAAC,KAAuB,EAAE,EAAE;IAC7D,IAAI,IAAA,kBAAU,EAAC,KAAK,CAAC,EAAE,CAAC;QACtB,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB;SACF,CAAC;IACJ,CAAC;IAGD,OAAO;QACL,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,uBAAuB;YAC7B,OAAO,EAAE,8BAA8B;SACxC;KACF,CAAC;AACJ,CAAC,CAAC;AApBW,QAAA,mBAAmB,uBAoB9B;AAGK,MAAM,YAAY,GAAG,CAAC,KAAU,EAAE,GAAQ,EAAE,GAAQ,EAAE,IAAS,EAAE,EAAE;IAExE,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;IAGxC,IAAI,IAAA,kBAAU,EAAC,KAAK,CAAC,EAAE,CAAC;QACtB,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAA,2BAAmB,EAAC,KAAK,CAAC,CAAC,CAAC;IACvE,CAAC;IAGD,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACrC,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,mBAAmB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAChF,OAAO,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAA,2BAAmB,EAAC,eAAe,CAAC,CAAC,CAAC;IAC3F,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACzC,OAAO,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAA,2BAAmB,EAAC,QAAQ,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACvC,MAAM,YAAY,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAC7C,OAAO,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAA,2BAAmB,EAAC,YAAY,CAAC,CAAC,CAAC;IACrF,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACrC,MAAM,aAAa,GAAG,IAAI,eAAe,CAAC,qBAAqB,CAAC,CAAC;QACjE,OAAO,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAA,2BAAmB,EAAC,aAAa,CAAC,CAAC,CAAC;IACvF,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC3B,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,yBAAyB,CAAC,CAAC;QACnE,OAAO,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAA,2BAAmB,EAAC,aAAa,CAAC,CAAC,CAAC;IACvF,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC3B,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,+BAA+B,CAAC,CAAC;QACzE,OAAO,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAA,2BAAmB,EAAC,aAAa,CAAC,CAAC,CAAC;IACvF,CAAC;IAGD,MAAM,WAAW,GAAG,IAAI,mBAAmB,EAAE,CAAC;IAC9C,OAAO,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAA,2BAAmB,EAAC,WAAW,CAAC,CAAC,CAAC;AACnF,CAAC,CAAC;AA3CW,QAAA,YAAY,gBA2CvB;AAGK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAQ,EAAE,GAAQ,EAAE,IAAS,EAAE,EAAE;QACvC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB;AAGK,MAAM,aAAa,GAAG,CAAC,KAAU,EAAY,EAAE;IACpD,IAAI,IAAA,kBAAU,EAAC,KAAK,CAAC,EAAE,CAAC;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,mBAAmB,CAAC,KAAK,CAAC,OAAO,IAAI,8BAA8B,CAAC,CAAC;AAClF,CAAC,CAAC;AANW,QAAA,aAAa,iBAMxB"}