import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>pp<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>pography,
  InputBase,
  IconButton,
  Button,
  Avatar,
  Menu,
  MenuItem,
  Tabs,
  Tab,
  Chip,
  useTheme,
  useMediaQuery,
  alpha,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Share as ShareIcon,
  Language as LanguageIcon,
  AccountCircle,
  Login as LoginIcon,
  Menu as MenuIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AssetSearchParams, PaginationParams } from '../../../types';

// 扩展的搜索参数类型
type ExtendedSearchParams = AssetSearchParams & PaginationParams;

interface HomeHeaderProps {
  onSearch: (query: string, filters?: ExtendedSearchParams) => void;
  onFiltersChange: (filters: ExtendedSearchParams) => void;
  categories: any[];
  tags: any[];
  searchQuery: string;
  currentFilters: ExtendedSearchParams;
}

const HomeHeader: React.FC<HomeHeaderProps> = ({
  onSearch,
  onFiltersChange,
  categories,
  tags,
  searchQuery,
  currentFilters
}) => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // 状态管理
  const [searchValue, setSearchValue] = useState(searchQuery);
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);
  const [languageMenuAnchor, setLanguageMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedMainTab, setSelectedMainTab] = useState(0);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedStyles, setSelectedStyles] = useState<string[]>([]);

  // 主菜单项（对应asset_type）
  const mainMenuItems = [
    { label: t('home.menu.home'), value: 'home' },
    { label: t('home.menu.models'), value: 'MODEL' },
    { label: t('home.menu.lora'), value: 'LORA' },
    { label: t('home.menu.workflows'), value: 'WORKFLOW' },
    { label: t('home.menu.prompts'), value: 'PROMPT' },
    { label: t('home.menu.tools'), value: 'TOOL' },
    { label: t('home.menu.challenges'), value: 'challenges' },
    { label: t('home.menu.bounties'), value: 'bounties' }
  ];

  // 分类过滤（从数据库tags表获取，type='CATEGORY'）
  const categoryTags = categories.filter(cat => cat.type === 'CATEGORY');
  
  // 风格过滤（从数据库tags表获取，type='STYLE'）
  const styleTags = tags.filter(tag => tag.type === 'STYLE');

  // 处理搜索
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(searchValue, currentFilters);
  };

  // 处理主菜单切换
  const handleMainTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedMainTab(newValue);
    const selectedItem = mainMenuItems[newValue];
    
    if (selectedItem.value === 'home') {
      onFiltersChange({ ...currentFilters, type: undefined });
    } else if (['challenges', 'bounties'].includes(selectedItem.value)) {
      // 处理挑战和悬赏页面导航
      navigate(`/${selectedItem.value}`);
    } else {
      // 处理资源类型过滤
      onFiltersChange({ ...currentFilters, type: selectedItem.value as any });
    }
  };

  // 处理分类过滤
  const handleCategoryToggle = (categoryId: string) => {
    const newCategories = selectedCategories.includes(categoryId)
      ? selectedCategories.filter(id => id !== categoryId)
      : [...selectedCategories, categoryId];
    
    setSelectedCategories(newCategories);
    onFiltersChange({ 
      ...currentFilters, 
      category: newCategories.length > 0 ? newCategories.join(',') : undefined 
    });
  };

  // 处理风格过滤
  const handleStyleToggle = (styleId: string) => {
    const newStyles = selectedStyles.includes(styleId)
      ? selectedStyles.filter(id => id !== styleId)
      : [...selectedStyles, styleId];
    
    setSelectedStyles(newStyles);
    onFiltersChange({ 
      ...currentFilters, 
      tags: newStyles.length > 0 ? newStyles : undefined 
    });
  };

  // 处理语言切换
  const handleLanguageChange = (language: string) => {
    i18n.changeLanguage(language);
    setLanguageMenuAnchor(null);
  };

  return (
    <Box sx={{ height: 156 }}>
      {/* 1. 页眉导航栏 (80px) */}
      <AppBar 
        position="static" 
        elevation={1}
        sx={{ 
          height: 80,
          backgroundColor: 'background.paper',
          color: 'text.primary',
          borderBottom: `1px solid ${theme.palette.divider}`
        }}
      >
        <Toolbar sx={{ height: '100%', px: { xs: 2, md: 4 } }}>
          {/* 左侧 LOGO */}
          <Box sx={{ display: 'flex', alignItems: 'center', mr: 4 }}>
            <Typography
              variant="h5"
              component="div"
              sx={{
                fontWeight: 'bold',
                color: 'primary.main',
                cursor: 'pointer'
              }}
              onClick={() => navigate('/')}
            >
              AIGC Service Hub
            </Typography>
          </Box>

          {/* 中间搜索框 */}
          <Box 
            component="form"
            onSubmit={handleSearchSubmit}
            sx={{ 
              flexGrow: 1, 
              maxWidth: 600,
              mx: { xs: 1, md: 4 }
            }}
          >
            <Box
              sx={{
                position: 'relative',
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.common.black, 0.05),
                '&:hover': {
                  backgroundColor: alpha(theme.palette.common.black, 0.08),
                },
                width: '100%',
              }}
            >
              <Box
                sx={{
                  padding: theme.spacing(0, 2),
                  height: '100%',
                  position: 'absolute',
                  pointerEvents: 'none',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <SearchIcon />
              </Box>
              <InputBase
                placeholder={t('home.search.placeholder')}
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                sx={{
                  color: 'inherit',
                  width: '100%',
                  '& .MuiInputBase-input': {
                    padding: theme.spacing(1.5, 1, 1.5, 0),
                    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
                    transition: theme.transitions.create('width'),
                    width: '100%',
                  },
                }}
              />
            </Box>
          </Box>

          {/* 右侧按钮组 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* 发布按钮 */}
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/assets/upload')}
              sx={{ 
                display: { xs: 'none', md: 'flex' },
                borderRadius: 2
              }}
            >
              {t('home.header.publish')}
            </Button>

            {/* 分享按钮 */}
            <IconButton
              color="inherit"
              onClick={() => {
                navigator.share?.({
                  title: 'AIGC Service Hub',
                  url: window.location.href
                });
              }}
              sx={{ display: { xs: 'none', sm: 'flex' } }}
            >
              <ShareIcon />
            </IconButton>

            {/* 语言切换 */}
            <IconButton
              color="inherit"
              onClick={(e) => setLanguageMenuAnchor(e.currentTarget)}
            >
              <LanguageIcon />
            </IconButton>

            {/* 用户头像/登录 */}
            <IconButton
              color="inherit"
              onClick={(e) => setUserMenuAnchor(e.currentTarget)}
            >
              <Avatar sx={{ width: 32, height: 32 }}>
                <AccountCircle />
              </Avatar>
            </IconButton>

            {/* 移动端菜单 */}
            {isMobile && (
              <IconButton color="inherit">
                <MenuIcon />
              </IconButton>
            )}
          </Box>
        </Toolbar>
      </AppBar>

      {/* 2. 主菜单栏 (30px) */}
      <Box
        sx={{
          height: 30,
          backgroundColor: 'background.paper',
          borderBottom: `1px solid ${theme.palette.divider}`,
          position: 'sticky',
          top: 0,
          zIndex: theme.zIndex.appBar - 1
        }}
      >
        <Tabs
          value={selectedMainTab}
          onChange={handleMainTabChange}
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            height: '100%',
            minHeight: 30,
            '& .MuiTab-root': {
              minHeight: 30,
              fontSize: '0.875rem',
              textTransform: 'none',
              px: 2
            }
          }}
        >
          {mainMenuItems.map((item, index) => (
            <Tab key={item.value} label={item.label} />
          ))}
        </Tabs>
      </Box>

      {/* 3. 分类过滤栏 (26px) */}
      <Box
        sx={{
          height: 26,
          backgroundColor: 'background.default',
          borderBottom: `1px solid ${theme.palette.divider}`,
          display: 'flex',
          alignItems: 'center',
          px: 2,
          overflow: 'auto'
        }}
      >
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <Typography variant="caption" sx={{ mr: 1, whiteSpace: 'nowrap' }}>
            {t('home.filters.categories')}:
          </Typography>
          {categoryTags.map((category) => (
            <Chip
              key={category.id}
              label={category.name}
              size="small"
              variant={selectedCategories.includes(category.id) ? 'filled' : 'outlined'}
              onClick={() => handleCategoryToggle(category.id)}
              sx={{ 
                height: 20,
                fontSize: '0.75rem',
                cursor: 'pointer'
              }}
            />
          ))}
        </Box>
      </Box>

      {/* 4. 风格过滤栏 (20px) */}
      <Box
        sx={{
          height: 20,
          backgroundColor: 'background.default',
          borderBottom: `1px solid ${theme.palette.divider}`,
          display: 'flex',
          alignItems: 'center',
          px: 2,
          overflow: 'auto'
        }}
      >
        <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>
          <Typography variant="caption" sx={{ mr: 1, whiteSpace: 'nowrap', fontSize: '0.7rem' }}>
            {t('home.filters.styles')}:
          </Typography>
          {styleTags.slice(0, 15).map((style) => (
            <Chip
              key={style.id}
              label={style.name}
              size="small"
              variant={selectedStyles.includes(style.id) ? 'filled' : 'outlined'}
              onClick={() => handleStyleToggle(style.id)}
              sx={{ 
                height: 16,
                fontSize: '0.7rem',
                cursor: 'pointer',
                '& .MuiChip-label': {
                  px: 0.5
                }
              }}
            />
          ))}
        </Box>
      </Box>

      {/* 用户菜单 */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={() => setUserMenuAnchor(null)}
      >
        <MenuItem onClick={() => navigate('/login')}>
          <LoginIcon sx={{ mr: 1 }} />
          {t('home.header.login')}
        </MenuItem>
        <MenuItem onClick={() => navigate('/profile')}>
          <AccountCircle sx={{ mr: 1 }} />
          {t('home.header.profile')}
        </MenuItem>
      </Menu>

      {/* 语言菜单 */}
      <Menu
        anchorEl={languageMenuAnchor}
        open={Boolean(languageMenuAnchor)}
        onClose={() => setLanguageMenuAnchor(null)}
      >
        <MenuItem onClick={() => handleLanguageChange('zh')}>
          中文
        </MenuItem>
        <MenuItem onClick={() => handleLanguageChange('en')}>
          English
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default HomeHeader;
