"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetController = void 0;
const asset_service_1 = require("../services/asset.service");
const errors_1 = require("../utils/errors");
const errors_2 = require("../utils/errors");
class AssetController {
    constructor() {
        this.createAsset = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const assetData = req.body;
            const asset = await this.assetService.createAsset(req.user.id, assetData);
            res.status(201).json({
                success: true,
                data: { asset },
                message: 'Asset created successfully',
            });
        });
        this.updateAsset = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const assetId = parseInt(req.params.id);
            if (isNaN(assetId)) {
                throw new errors_1.ValidationError('Invalid asset ID');
            }
            const updates = req.body;
            const asset = await this.assetService.updateAsset(assetId, req.user.id, updates);
            res.json({
                success: true,
                data: { asset },
                message: 'Asset updated successfully',
            });
        });
        this.publishAsset = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const assetId = parseInt(req.params.id);
            if (isNaN(assetId)) {
                throw new errors_1.ValidationError('Invalid asset ID');
            }
            const asset = await this.assetService.publishAsset(assetId, req.user.id);
            res.json({
                success: true,
                data: { asset },
                message: 'Asset published successfully',
            });
        });
        this.deleteAsset = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const assetId = parseInt(req.params.id);
            if (isNaN(assetId)) {
                throw new errors_1.ValidationError('Invalid asset ID');
            }
            await this.assetService.deleteAsset(assetId, req.user.id);
            res.json({
                success: true,
                message: 'Asset deleted successfully',
            });
        });
        this.getAssetById = (0, errors_2.asyncHandler)(async (req, res) => {
            const assetId = parseInt(req.params.id);
            if (isNaN(assetId)) {
                throw new errors_1.ValidationError('Invalid asset ID');
            }
            const includePrivate = req.user && req.user.userRole === 'ADMIN';
            const asset = await this.assetService.getAssetById(assetId, includePrivate);
            res.json({
                success: true,
                data: { asset },
                message: 'Asset details retrieved successfully',
            });
        });
        this.getAssetList = (0, errors_2.asyncHandler)(async (req, res) => {
            const query = {
                page: parseInt(req.query.page) || 1,
                limit: parseInt(req.query.limit) || 20,
                category: req.query.category,
                style: req.query.style,
                assetType: req.query.assetType,
                sortBy: req.query.sortBy,
                sortOrder: req.query.sortOrder,
                search: req.query.search,
            };
            const result = await this.assetService.getAssetList(query);
            res.json({
                success: true,
                data: result,
                message: 'Asset list retrieved successfully',
            });
        });
        this.getCreatorAssets = (0, errors_2.asyncHandler)(async (req, res) => {
            const creatorId = parseInt(req.params.creatorId);
            if (isNaN(creatorId)) {
                throw new errors_1.ValidationError('Invalid creator ID');
            }
            const query = {
                page: parseInt(req.query.page) || 1,
                limit: parseInt(req.query.limit) || 20,
                assetType: req.query.assetType,
                sortBy: req.query.sortBy,
                sortOrder: req.query.sortOrder,
            };
            const result = await this.assetService.getCreatorAssets(creatorId, query);
            res.json({
                success: true,
                data: result,
                message: 'Creator assets retrieved successfully',
            });
        });
        this.getMyAssets = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const query = {
                page: parseInt(req.query.page) || 1,
                limit: parseInt(req.query.limit) || 20,
                assetType: req.query.assetType,
                sortBy: req.query.sortBy,
                sortOrder: req.query.sortOrder,
            };
            const result = await this.assetService.getCreatorAssets(req.user.id, query);
            res.json({
                success: true,
                data: result,
                message: 'My assets retrieved successfully',
            });
        });
        this.getAssetStats = (0, errors_2.asyncHandler)(async (req, res) => {
            const assetId = parseInt(req.params.id);
            if (isNaN(assetId)) {
                throw new errors_1.ValidationError('Invalid asset ID');
            }
            const stats = await this.assetService.getAssetStats(assetId);
            res.json({
                success: true,
                data: stats,
                message: 'Asset stats retrieved successfully',
            });
        });
        this.updateAssetFile = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const assetId = parseInt(req.params.id);
            if (isNaN(assetId)) {
                throw new errors_1.ValidationError('Invalid asset ID');
            }
            const { s3FileKey, fileSize } = req.body;
            if (!s3FileKey || !fileSize) {
                throw new errors_1.ValidationError('s3FileKey and fileSize are required');
            }
            await this.assetService.updateAssetFile(assetId, req.user.id, s3FileKey, fileSize);
            res.json({
                success: true,
                message: 'Asset file updated successfully',
            });
        });
        this.downloadAsset = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const assetId = parseInt(req.params.id);
            if (isNaN(assetId)) {
                throw new errors_1.ValidationError('Invalid asset ID');
            }
            await this.assetService.incrementDownloadCount(assetId);
            res.json({
                success: true,
                data: {
                    downloadUrl: `https://example.com/download/${assetId}`,
                    expiresAt: new Date(Date.now() + 3600000).toISOString(),
                },
                message: 'Download URL generated successfully',
            });
        });
        this.assetService = new asset_service_1.AssetService();
    }
}
exports.AssetController = AssetController;
exports.default = AssetController;
//# sourceMappingURL=asset.controller.js.map