import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { uploadAPI } from '../../services/api';

// 上传状态
type UploadStatus = 'idle' | 'pending' | 'uploading' | 'processing' | 'success' | 'error' | 'paused' | 'canceled';

// 文件类型
type FileType = 'image' | 'video' | 'audio' | 'model' | 'document' | 'other';

// 上传文件信息
interface UploadFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: FileType;
  mimeType: string;
  preview?: string;
  status: UploadStatus;
  progress: number;
  uploadedBytes: number;
  totalBytes: number;
  speed: number; // bytes per second
  timeRemaining: number; // seconds
  error: string | null;
  retryCount: number;
  maxRetries: number;
  chunkSize: number;
  chunks: UploadChunk[];
  metadata: Record<string, any>;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  canceledAt?: string;
}

// 上传块信息
interface UploadChunk {
  id: string;
  index: number;
  start: number;
  end: number;
  size: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  retryCount: number;
  error: string | null;
  etag?: string;
  uploadedAt?: string;
}

// 上传会话
interface UploadSession {
  id: string;
  fileId: string;
  uploadId: string;
  bucket: string;
  key: string;
  parts: Array<{
    partNumber: number;
    etag: string;
  }>;
  createdAt: string;
  expiresAt: string;
}

// 上传配置
interface UploadConfig {
  maxFileSize: number; // bytes
  maxFiles: number;
  allowedTypes: string[];
  chunkSize: number;
  maxConcurrentUploads: number;
  maxConcurrentChunks: number;
  retryAttempts: number;
  retryDelay: number;
  autoStart: boolean;
  generatePreview: boolean;
  validateFiles: boolean;
  compressionEnabled: boolean;
  compressionQuality: number;
}

// 上传统计
interface UploadStats {
  totalFiles: number;
  totalSize: number;
  uploadedFiles: number;
  uploadedSize: number;
  failedFiles: number;
  averageSpeed: number;
  totalTime: number;
  successRate: number;
}

// 上传队列
interface UploadQueue {
  files: UploadFile[];
  activeUploads: string[];
  completedUploads: string[];
  failedUploads: string[];
  pausedUploads: string[];
  canceledUploads: string[];
}

// 异步actions
export const uploadFile = createAsyncThunk(
  'upload/uploadFile',
  async (params: { file: File; config?: Partial<UploadConfig> }, { dispatch, getState, rejectWithValue }) => {
    try {
      const { file, config } = params;
      
      // 创建上传会话
      const sessionResponse = await uploadAPI.createUploadSession({
        fileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        ...config,
      });
      
      const session = sessionResponse.data.data;
      
      // 开始分块上传
      const result = await uploadAPI.uploadFileChunks({
        file,
        session,
        onProgress: (progress) => {
          dispatch(updateUploadProgress({ fileId: session.fileId, progress }));
        },
        onChunkComplete: (chunkIndex, etag) => {
          dispatch(updateChunkStatus({ 
            fileId: session.fileId, 
            chunkIndex, 
            status: 'success',
            etag 
          }));
        },
        onChunkError: (chunkIndex, error) => {
          dispatch(updateChunkStatus({ 
            fileId: session.fileId, 
            chunkIndex, 
            status: 'error',
            error: error.message 
          }));
        },
      });
      
      return result.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '上传失败' });
    }
  }
);

export const pauseUpload = createAsyncThunk(
  'upload/pauseUpload',
  async (fileId: string, { rejectWithValue }) => {
    try {
      const response = await uploadAPI.pauseUpload(fileId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '暂停上传失败' });
    }
  }
);

export const resumeUpload = createAsyncThunk(
  'upload/resumeUpload',
  async (fileId: string, { rejectWithValue }) => {
    try {
      const response = await uploadAPI.resumeUpload(fileId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '恢复上传失败' });
    }
  }
);

export const cancelUpload = createAsyncThunk(
  'upload/cancelUpload',
  async (fileId: string, { rejectWithValue }) => {
    try {
      const response = await uploadAPI.cancelUpload(fileId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '取消上传失败' });
    }
  }
);

export const retryUpload = createAsyncThunk(
  'upload/retryUpload',
  async (fileId: string, { rejectWithValue }) => {
    try {
      const response = await uploadAPI.retryUpload(fileId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '重试上传失败' });
    }
  }
);

// 默认配置
const defaultConfig: UploadConfig = {
  maxFileSize: 30 * 1024 * 1024 * 1024, // 30GB
  maxFiles: 10,
  allowedTypes: [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'video/mp4', 'video/webm', 'video/mov', 'video/avi',
    'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/flac',
    'model/gltf+json', 'model/gltf-binary', 'application/octet-stream',
    'application/pdf', 'application/zip', 'application/x-rar-compressed',
  ],
  chunkSize: 5 * 1024 * 1024, // 5MB
  maxConcurrentUploads: 3,
  maxConcurrentChunks: 4,
  retryAttempts: 3,
  retryDelay: 1000,
  autoStart: true,
  generatePreview: true,
  validateFiles: true,
  compressionEnabled: false,
  compressionQuality: 0.8,
};

// 初始状态
const initialState = {
  // 上传队列
  queue: {
    files: [],
    activeUploads: [],
    completedUploads: [],
    failedUploads: [],
    pausedUploads: [],
    canceledUploads: [],
  } as UploadQueue,
  
  // 上传会话
  sessions: [] as UploadSession[],
  
  // 配置
  config: defaultConfig,
  
  // 统计信息
  stats: {
    totalFiles: 0,
    totalSize: 0,
    uploadedFiles: 0,
    uploadedSize: 0,
    failedFiles: 0,
    averageSpeed: 0,
    totalTime: 0,
    successRate: 0,
  } as UploadStats,
  
  // 全局状态
  isUploading: false,
  isPaused: false,
  dragAndDrop: {
    isDragging: false,
    dragCount: 0,
  },
  
  // 错误状态
  error: null as string | null,
  
  // 预览状态
  previewFiles: [] as UploadFile[],
  selectedFiles: [] as string[],
  
  // 过滤器
  filters: {
    status: 'all' as UploadStatus | 'all',
    type: 'all' as FileType | 'all',
    sortBy: 'createdAt' as 'createdAt' | 'name' | 'size' | 'progress',
    sortOrder: 'desc' as 'asc' | 'desc',
  },
};

// 工具函数
const generateFileId = (): string => {
  return `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

const getFileType = (mimeType: string): FileType => {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';
  if (mimeType.startsWith('model/')) return 'model';
  if (mimeType.includes('pdf') || mimeType.includes('document')) return 'document';
  return 'other';
};

const calculateChunks = (file: File, chunkSize: number): UploadChunk[] => {
  const chunks: UploadChunk[] = [];
  const totalChunks = Math.ceil(file.size / chunkSize);
  
  for (let i = 0; i < totalChunks; i++) {
    const start = i * chunkSize;
    const end = Math.min(start + chunkSize, file.size);
    
    chunks.push({
      id: `chunk-${i}`,
      index: i,
      start,
      end,
      size: end - start,
      status: 'pending',
      progress: 0,
      retryCount: 0,
      error: null,
    });
  }
  
  return chunks;
};

const calculateStats = (files: UploadFile[]): UploadStats => {
  const totalFiles = files.length;
  const totalSize = files.reduce((sum, file) => sum + file.size, 0);
  const uploadedFiles = files.filter(file => file.status === 'success').length;
  const uploadedSize = files.filter(file => file.status === 'success').reduce((sum, file) => sum + file.size, 0);
  const failedFiles = files.filter(file => file.status === 'error').length;
  
  const completedFiles = files.filter(file => file.completedAt && file.startedAt);
  const totalTime = completedFiles.reduce((sum, file) => {
    const startTime = new Date(file.startedAt!).getTime();
    const endTime = new Date(file.completedAt!).getTime();
    return sum + (endTime - startTime);
  }, 0);
  
  const averageSpeed = totalTime > 0 ? uploadedSize / (totalTime / 1000) : 0;
  const successRate = totalFiles > 0 ? uploadedFiles / totalFiles : 0;
  
  return {
    totalFiles,
    totalSize,
    uploadedFiles,
    uploadedSize,
    failedFiles,
    averageSpeed,
    totalTime,
    successRate,
  };
};

// 创建slice
const uploadSlice = createSlice({
  name: 'upload',
  initialState,
  reducers: {
    // 添加文件到队列
    addFiles: (state, action: PayloadAction<File[]>) => {
      const files = action.payload;
      const now = new Date().toISOString();
      
      files.forEach(file => {
        const fileType = getFileType(file.type);
        const chunks = calculateChunks(file, state.config.chunkSize);
        
        const uploadFile: UploadFile = {
          id: generateFileId(),
          file,
          name: file.name,
          size: file.size,
          type: fileType,
          mimeType: file.type,
          status: 'idle',
          progress: 0,
          uploadedBytes: 0,
          totalBytes: file.size,
          speed: 0,
          timeRemaining: 0,
          error: null,
          retryCount: 0,
          maxRetries: state.config.retryAttempts,
          chunkSize: state.config.chunkSize,
          chunks,
          metadata: {},
          createdAt: now,
        };
        
        state.queue.files.push(uploadFile);
      });
      
      state.stats = calculateStats(state.queue.files);
    },
    
    // 移除文件
    removeFile: (state, action: PayloadAction<string>) => {
      const fileId = action.payload;
      state.queue.files = state.queue.files.filter(file => file.id !== fileId);
      state.queue.activeUploads = state.queue.activeUploads.filter(id => id !== fileId);
      state.queue.completedUploads = state.queue.completedUploads.filter(id => id !== fileId);
      state.queue.failedUploads = state.queue.failedUploads.filter(id => id !== fileId);
      state.queue.pausedUploads = state.queue.pausedUploads.filter(id => id !== fileId);
      state.queue.canceledUploads = state.queue.canceledUploads.filter(id => id !== fileId);
      state.stats = calculateStats(state.queue.files);
    },
    
    // 清空队列
    clearQueue: (state) => {
      state.queue.files = [];
      state.queue.activeUploads = [];
      state.queue.completedUploads = [];
      state.queue.failedUploads = [];
      state.queue.pausedUploads = [];
      state.queue.canceledUploads = [];
      state.stats = calculateStats([]);
    },
    
    // 更新上传进度
    updateUploadProgress: (state, action: PayloadAction<{ fileId: string; progress: number }>) => {
      const { fileId, progress } = action.payload;
      const file = state.queue.files.find(f => f.id === fileId);
      
      if (file) {
        file.progress = progress;
        file.uploadedBytes = Math.round(file.totalBytes * progress / 100);
        
        // 计算速度和剩余时间
        if (file.startedAt) {
          const elapsedTime = (Date.now() - new Date(file.startedAt).getTime()) / 1000;
          file.speed = file.uploadedBytes / elapsedTime;
          file.timeRemaining = file.speed > 0 ? (file.totalBytes - file.uploadedBytes) / file.speed : 0;
        }
      }
    },
    
    // 更新块状态
    updateChunkStatus: (state, action: PayloadAction<{
      fileId: string;
      chunkIndex: number;
      status: UploadChunk['status'];
      error?: string;
      etag?: string;
    }>) => {
      const { fileId, chunkIndex, status, error, etag } = action.payload;
      const file = state.queue.files.find(f => f.id === fileId);
      
      if (file && file.chunks[chunkIndex]) {
        const chunk = file.chunks[chunkIndex];
        chunk.status = status;
        chunk.error = error || null;
        chunk.etag = etag;
        
        if (status === 'success') {
          chunk.progress = 100;
          chunk.uploadedAt = new Date().toISOString();
        }
      }
    },
    
    // 更新文件状态
    updateFileStatus: (state, action: PayloadAction<{
      fileId: string;
      status: UploadStatus;
      error?: string;
    }>) => {
      const { fileId, status, error } = action.payload;
      const file = state.queue.files.find(f => f.id === fileId);
      
      if (file) {
        file.status = status;
        file.error = error || null;
        
        const now = new Date().toISOString();
        
        if (status === 'uploading' && !file.startedAt) {
          file.startedAt = now;
        } else if (status === 'success') {
          file.completedAt = now;
          file.progress = 100;
          file.uploadedBytes = file.totalBytes;
        } else if (status === 'canceled') {
          file.canceledAt = now;
        }
        
        // 更新队列状态
        state.queue.activeUploads = state.queue.activeUploads.filter(id => id !== fileId);
        state.queue.completedUploads = state.queue.completedUploads.filter(id => id !== fileId);
        state.queue.failedUploads = state.queue.failedUploads.filter(id => id !== fileId);
        state.queue.pausedUploads = state.queue.pausedUploads.filter(id => id !== fileId);
        state.queue.canceledUploads = state.queue.canceledUploads.filter(id => id !== fileId);
        
        if (status === 'uploading') {
          state.queue.activeUploads.push(fileId);
        } else if (status === 'success') {
          state.queue.completedUploads.push(fileId);
        } else if (status === 'error') {
          state.queue.failedUploads.push(fileId);
        } else if (status === 'paused') {
          state.queue.pausedUploads.push(fileId);
        } else if (status === 'canceled') {
          state.queue.canceledUploads.push(fileId);
        }
      }
      
      state.stats = calculateStats(state.queue.files);
    },
    
    // 更新配置
    updateConfig: (state, action: PayloadAction<Partial<UploadConfig>>) => {
      state.config = { ...state.config, ...action.payload };
    },
    
    // 设置拖拽状态
    setDragState: (state, action: PayloadAction<{ isDragging: boolean; dragCount?: number }>) => {
      const { isDragging, dragCount } = action.payload;
      state.dragAndDrop.isDragging = isDragging;
      if (dragCount !== undefined) {
        state.dragAndDrop.dragCount = dragCount;
      }
    },
    
    // 设置预览文件
    setPreviewFiles: (state, action: PayloadAction<UploadFile[]>) => {
      state.previewFiles = action.payload;
    },
    
    // 选择文件
    selectFile: (state, action: PayloadAction<string>) => {
      const fileId = action.payload;
      if (!state.selectedFiles.includes(fileId)) {
        state.selectedFiles.push(fileId);
      }
    },
    
    // 取消选择文件
    deselectFile: (state, action: PayloadAction<string>) => {
      const fileId = action.payload;
      state.selectedFiles = state.selectedFiles.filter(id => id !== fileId);
    },
    
    // 全选文件
    selectAllFiles: (state) => {
      state.selectedFiles = state.queue.files.map(file => file.id);
    },
    
    // 取消全选
    deselectAllFiles: (state) => {
      state.selectedFiles = [];
    },
    
    // 设置过滤器
    setFilters: (state, action: PayloadAction<Partial<typeof initialState.filters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // 设置全局上传状态
    setUploadingState: (state, action: PayloadAction<boolean>) => {
      state.isUploading = action.payload;
    },
    
    // 设置暂停状态
    setPausedState: (state, action: PayloadAction<boolean>) => {
      state.isPaused = action.payload;
    },
    
    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    
    // 重置上传状态
    resetUpload: (state) => {
      Object.assign(state, initialState);
    },
  },
  extraReducers: (builder) => {
    // 上传文件
    builder
      .addCase(uploadFile.pending, (state, action) => {
        state.isUploading = true;
        state.error = null;
      })
      .addCase(uploadFile.fulfilled, (state, action) => {
        state.isUploading = false;
        state.error = null;
      })
      .addCase(uploadFile.rejected, (state, action) => {
        state.isUploading = false;
        state.error = action.payload as string;
      });

    // 暂停上传
    builder.addCase(pauseUpload.fulfilled, (state, action) => {
      const fileId = action.meta.arg;
      const file = state.queue.files.find(f => f.id === fileId);
      if (file) {
        file.status = 'paused';
      }
    });

    // 恢复上传
    builder.addCase(resumeUpload.fulfilled, (state, action) => {
      const fileId = action.meta.arg;
      const file = state.queue.files.find(f => f.id === fileId);
      if (file) {
        file.status = 'uploading';
      }
    });

    // 取消上传
    builder.addCase(cancelUpload.fulfilled, (state, action) => {
      const fileId = action.meta.arg;
      const file = state.queue.files.find(f => f.id === fileId);
      if (file) {
        file.status = 'canceled';
      }
    });

    // 重试上传
    builder.addCase(retryUpload.fulfilled, (state, action) => {
      const fileId = action.meta.arg;
      const file = state.queue.files.find(f => f.id === fileId);
      if (file) {
        file.retryCount += 1;
        file.status = 'uploading';
      }
    });
  },
});

// 导出actions
export const {
  addFiles,
  removeFile,
  clearQueue,
  updateUploadProgress,
  updateChunkStatus,
  updateFileStatus,
  updateConfig,
  setDragState,
  setPreviewFiles,
  selectFile,
  deselectFile,
  selectAllFiles,
  deselectAllFiles,
  setFilters,
  setUploadingState,
  setPausedState,
  setError,
  clearError,
  resetUpload,
} = uploadSlice.actions;

// 选择器
export const selectUpload = (state: { upload: typeof initialState }) => state.upload;
export const selectUploadQueue = (state: { upload: typeof initialState }) => state.upload.queue;
export const selectUploadStats = (state: { upload: typeof initialState }) => state.upload.stats;
export const selectUploadConfig = (state: { upload: typeof initialState }) => state.upload.config;
export const selectUploadFiles = (state: { upload: typeof initialState }) => state.upload.queue.files;
export const selectActiveUploads = (state: { upload: typeof initialState }) => 
  state.upload.queue.files.filter(file => state.upload.queue.activeUploads.includes(file.id));
export const selectCompletedUploads = (state: { upload: typeof initialState }) => 
  state.upload.queue.files.filter(file => state.upload.queue.completedUploads.includes(file.id));
export const selectFailedUploads = (state: { upload: typeof initialState }) => 
  state.upload.queue.files.filter(file => state.upload.queue.failedUploads.includes(file.id));
export const selectIsUploading = (state: { upload: typeof initialState }) => state.upload.isUploading;
export const selectIsPaused = (state: { upload: typeof initialState }) => state.upload.isPaused;
export const selectDragAndDrop = (state: { upload: typeof initialState }) => state.upload.dragAndDrop;
export const selectSelectedFiles = (state: { upload: typeof initialState }) => state.upload.selectedFiles;
export const selectUploadFilters = (state: { upload: typeof initialState }) => state.upload.filters;
export const selectUploadError = (state: { upload: typeof initialState }) => state.upload.error;

// 过滤后的文件列表
export const selectFilteredFiles = (state: { upload: typeof initialState }) => {
  const { files } = state.upload.queue;
  const { status, type, sortBy, sortOrder } = state.upload.filters;
  
  let filtered = files;
  
  // 按状态过滤
  if (status !== 'all') {
    filtered = filtered.filter(file => file.status === status);
  }
  
  // 按类型过滤
  if (type !== 'all') {
    filtered = filtered.filter(file => file.type === type);
  }
  
  // 排序
  filtered.sort((a, b) => {
    let aValue: any;
    let bValue: any;
    
    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case 'size':
        aValue = a.size;
        bValue = b.size;
        break;
      case 'progress':
        aValue = a.progress;
        bValue = b.progress;
        break;
      case 'createdAt':
      default:
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
        break;
    }
    
    if (sortOrder === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });
  
  return filtered;
};

// 导出reducer
export default uploadSlice.reducer;