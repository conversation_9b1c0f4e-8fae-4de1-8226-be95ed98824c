#!/bin/bash

# ===========================================
# AIGC Service Hub MVP 1.0 - 日志查看脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AIGC Service Hub MVP 1.0 - 日志查看脚本

使用方法: $0 [OPTIONS] [SERVICE]

SERVICE:
    postgres            PostgreSQL数据库日志
    redis              Redis缓存日志
    backend            后端API服务日志
    frontend           前端应用日志
    nginx              Nginx代理日志
    adminer            数据库管理工具日志
    mailhog            邮件测试工具日志
    all                所有服务日志 (默认)

OPTIONS:
    -h, --help          显示帮助信息
    -v, --verbose       详细输出
    -f, --follow        实时跟踪日志
    -t, --tail N        显示最后N行日志 (默认: 100)
    -s, --since TIME    显示指定时间之后的日志
    -e, --env ENV       指定环境 (dev, test, prod)
    -g, --grep PATTERN  过滤日志内容
    -o, --output FILE   输出到文件
    -c, --container     显示容器日志
    -l, --local         显示本地日志文件
    --no-color          禁用颜色输出
    --timestamps        显示时间戳
    --details           显示详细信息

示例:
    $0                      # 查看所有服务日志
    $0 backend              # 查看后端服务日志
    $0 -f postgres          # 实时跟踪PostgreSQL日志
    $0 -t 50 backend        # 显示后端最后50行日志
    $0 -g "ERROR" backend   # 过滤包含ERROR的日志
    $0 -o logs.txt all      # 输出所有日志到文件

EOF
}

# 确定环境和配置文件
determine_environment() {
    if [ -n "$ENVIRONMENT" ]; then
        case $ENVIRONMENT in
            dev|development)
                COMPOSE_FILE="docker-compose.dev.yml"
                ;;
            test|testing)
                COMPOSE_FILE="docker-compose.test.yml"
                ;;
            prod|production)
                COMPOSE_FILE="docker-compose.prod.yml"
                ;;
            *)
                COMPOSE_FILE="docker-compose.yml"
                ;;
        esac
    else
        # 自动检测环境
        if [ -f "docker-compose.dev.yml" ] && docker-compose -f docker-compose.dev.yml ps | grep -q "Up"; then
            COMPOSE_FILE="docker-compose.dev.yml"
            ENVIRONMENT="dev"
        elif [ -f "docker-compose.prod.yml" ] && docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
            COMPOSE_FILE="docker-compose.prod.yml"
            ENVIRONMENT="prod"
        elif [ -f "docker-compose.test.yml" ] && docker-compose -f docker-compose.test.yml ps | grep -q "Up"; then
            COMPOSE_FILE="docker-compose.test.yml"
            ENVIRONMENT="test"
        else
            COMPOSE_FILE="docker-compose.yml"
            ENVIRONMENT="default"
        fi
    fi
    
    log_info "使用环境: $ENVIRONMENT"
    log_info "配置文件: $COMPOSE_FILE"
}

# 检查服务状态
check_service_status() {
    log_info "检查服务状态..."
    
    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "配置文件不存在: $COMPOSE_FILE"
        exit 1
    fi
    
    echo
    docker-compose -f "$COMPOSE_FILE" ps
    echo
}

# 显示容器日志
show_container_logs() {
    local service=$1
    
    log_info "显示容器日志: $service"
    
    # 构建docker-compose logs命令
    local cmd="docker-compose -f $COMPOSE_FILE logs"
    
    # 添加选项
    if [ "$FOLLOW" = true ]; then
        cmd="$cmd --follow"
    fi
    
    if [ -n "$TAIL_LINES" ]; then
        cmd="$cmd --tail=$TAIL_LINES"
    fi
    
    if [ -n "$SINCE_TIME" ]; then
        cmd="$cmd --since=$SINCE_TIME"
    fi
    
    if [ "$TIMESTAMPS" = true ]; then
        cmd="$cmd --timestamps"
    fi
    
    if [ "$NO_COLOR" = true ]; then
        cmd="$cmd --no-color"
    fi
    
    # 添加服务名
    if [ "$service" != "all" ]; then
        cmd="$cmd $service"
    fi
    
    # 执行命令
    if [ -n "$GREP_PATTERN" ]; then
        eval "$cmd" | grep --color=auto "$GREP_PATTERN"
    else
        eval "$cmd"
    fi
}

# 显示本地日志文件
show_local_logs() {
    local service=$1
    
    log_info "显示本地日志文件: $service"
    
    case $service in
        postgres)
            show_postgres_logs
            ;;
        redis)
            show_redis_logs
            ;;
        backend)
            show_backend_logs
            ;;
        frontend)
            show_frontend_logs
            ;;
        nginx)
            show_nginx_logs
            ;;
        all)
            show_all_local_logs
            ;;
        *)
            log_error "不支持的服务: $service"
            ;;
    esac
}

# 显示PostgreSQL日志
show_postgres_logs() {
    local log_files=(
        "database/postgres-data/log/postgresql-*.log"
        "database/backups/postgres*.log"
    )
    
    for pattern in "${log_files[@]}"; do
        for file in $pattern; do
            if [ -f "$file" ]; then
                log_info "PostgreSQL日志: $file"
                show_log_file "$file"
            fi
        done
    done
}

# 显示Redis日志
show_redis_logs() {
    local log_files=(
        "database/redis-data/redis.log"
        "database/backups/redis*.log"
    )
    
    for file in "${log_files[@]}"; do
        if [ -f "$file" ]; then
            log_info "Redis日志: $file"
            show_log_file "$file"
        fi
    done
}

# 显示后端日志
show_backend_logs() {
    local log_files=(
        "backend/logs/app.log"
        "backend/logs/error.log"
        "backend/logs/access.log"
        "backend/logs/combined.log"
    )
    
    for file in "${log_files[@]}"; do
        if [ -f "$file" ]; then
            log_info "后端日志: $file"
            show_log_file "$file"
        fi
    done
}

# 显示前端日志
show_frontend_logs() {
    local log_files=(
        "frontend/logs/build.log"
        "frontend/logs/error.log"
    )
    
    for file in "${log_files[@]}"; do
        if [ -f "$file" ]; then
            log_info "前端日志: $file"
            show_log_file "$file"
        fi
    done
}

# 显示Nginx日志
show_nginx_logs() {
    local log_files=(
        "nginx/logs/access.log"
        "nginx/logs/error.log"
        "nginx/logs/api.log"
        "nginx/logs/auth.log"
        "nginx/logs/upload.log"
    )
    
    for file in "${log_files[@]}"; do
        if [ -f "$file" ]; then
            log_info "Nginx日志: $file"
            show_log_file "$file"
        fi
    done
}

# 显示所有本地日志
show_all_local_logs() {
    log_info "显示所有本地日志文件..."
    
    show_postgres_logs
    show_redis_logs
    show_backend_logs
    show_frontend_logs
    show_nginx_logs
}

# 显示日志文件内容
show_log_file() {
    local file=$1
    
    if [ ! -f "$file" ]; then
        log_warning "日志文件不存在: $file"
        return
    fi
    
    # 构建命令
    local cmd
    
    if [ "$FOLLOW" = true ]; then
        cmd="tail -f"
    else
        cmd="tail"
    fi
    
    if [ -n "$TAIL_LINES" ]; then
        cmd="$cmd -n $TAIL_LINES"
    fi
    
    cmd="$cmd $file"
    
    # 应用过滤器
    if [ -n "$GREP_PATTERN" ]; then
        cmd="$cmd | grep --color=auto $GREP_PATTERN"
    fi
    
    # 应用时间戳
    if [ "$TIMESTAMPS" = true ]; then
        cmd="$cmd | while read line; do echo \"$(date '+%Y-%m-%d %H:%M:%S') \$line\"; done"
    fi
    
    # 执行命令
    eval "$cmd"
}

# 输出日志到文件
output_to_file() {
    local service=$1
    
    log_info "输出日志到文件: $OUTPUT_FILE"
    
    # 创建输出目录
    mkdir -p "$(dirname "$OUTPUT_FILE")"
    
    # 添加头部信息
    cat > "$OUTPUT_FILE" << EOF
AIGC Service Hub MVP 1.0 - 日志导出
=====================================

导出时间: $(date)
环境: $ENVIRONMENT
服务: $service
配置文件: $COMPOSE_FILE

=====================================

EOF
    
    # 输出日志内容
    if [ "$SHOW_CONTAINER" = true ]; then
        show_container_logs "$service" >> "$OUTPUT_FILE"
    else
        show_local_logs "$service" >> "$OUTPUT_FILE"
    fi
    
    log_success "日志已导出到: $OUTPUT_FILE"
}

# 显示日志统计
show_log_statistics() {
    log_info "日志统计信息:"
    
    # 容器日志统计
    if [ "$SHOW_CONTAINER" = true ]; then
        echo "容器日志统计:"
        docker-compose -f "$COMPOSE_FILE" ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}"
    fi
    
    # 本地日志文件统计
    echo
    echo "本地日志文件统计:"
    find . -name "*.log" -type f -exec ls -lh {} \; | sort -k5 -hr | head -10
    
    # 磁盘使用情况
    echo
    echo "日志目录磁盘使用:"
    du -sh backend/logs nginx/logs database/backups scripts/logs 2>/dev/null || true
}

# 清理旧日志
cleanup_logs() {
    log_info "清理旧日志文件..."
    
    # 清理超过7天的日志
    find backend/logs -name "*.log" -mtime +7 -delete 2>/dev/null || true
    find nginx/logs -name "*.log" -mtime +7 -delete 2>/dev/null || true
    find scripts/logs -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    # 清理空的日志文件
    find . -name "*.log" -size 0 -delete 2>/dev/null || true
    
    log_success "旧日志清理完成"
}

# 主函数
main() {
    # 默认参数
    SERVICE="all"
    VERBOSE=false
    FOLLOW=false
    TAIL_LINES=100
    SINCE_TIME=""
    ENVIRONMENT=""
    GREP_PATTERN=""
    OUTPUT_FILE=""
    SHOW_CONTAINER=true
    SHOW_LOCAL=false
    NO_COLOR=false
    TIMESTAMPS=false
    SHOW_DETAILS=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--follow)
                FOLLOW=true
                shift
                ;;
            -t|--tail)
                TAIL_LINES=$2
                shift 2
                ;;
            -s|--since)
                SINCE_TIME=$2
                shift 2
                ;;
            -e|--env)
                ENVIRONMENT=$2
                shift 2
                ;;
            -g|--grep)
                GREP_PATTERN=$2
                shift 2
                ;;
            -o|--output)
                OUTPUT_FILE=$2
                shift 2
                ;;
            -c|--container)
                SHOW_CONTAINER=true
                SHOW_LOCAL=false
                shift
                ;;
            -l|--local)
                SHOW_CONTAINER=false
                SHOW_LOCAL=true
                shift
                ;;
            --no-color)
                NO_COLOR=true
                shift
                ;;
            --timestamps)
                TIMESTAMPS=true
                shift
                ;;
            --details)
                SHOW_DETAILS=true
                shift
                ;;
            postgres|redis|backend|frontend|nginx|adminer|mailhog|all)
                SERVICE=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置详细输出
    if [ "$VERBOSE" = true ]; then
        set -x
    fi
    
    log_info "查看 AIGC Service Hub MVP 1.0 日志"
    
    # 确定环境
    determine_environment
    
    # 显示详细信息
    if [ "$SHOW_DETAILS" = true ]; then
        check_service_status
        show_log_statistics
        echo
    fi
    
    # 输出日志
    if [ -n "$OUTPUT_FILE" ]; then
        output_to_file "$SERVICE"
    else
        if [ "$SHOW_CONTAINER" = true ]; then
            show_container_logs "$SERVICE"
        else
            show_local_logs "$SERVICE"
        fi
    fi
}

# 执行主函数
main "$@"