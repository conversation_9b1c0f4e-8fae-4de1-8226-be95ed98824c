import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Transaction, TransactionStatus, TransactionType, PaginationParams } from '../../types';
import { transactionsAPI } from '../../services/api';

// 异步actions
export const fetchTransactions = createAsyncThunk(
  'transactions/fetchTransactions',
  async (params: PaginationParams & {
    status?: TransactionStatus;
    type?: TransactionType;
    startDate?: string;
    endDate?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await transactionsAPI.getTransactions(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取交易记录失败' });
    }
  }
);

export const fetchTransactionById = createAsyncThunk(
  'transactions/fetchTransactionById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await transactionsAPI.getTransactionById(id);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取交易详情失败' });
    }
  }
);

export const createPaymentIntent = createAsyncThunk(
  'transactions/createPaymentIntent',
  async (params: {
    assetIds: string[];
    paymentMethod: 'paypal' | 'stripe';
    currency: string;
  }, { rejectWithValue }) => {
    try {
      const response = await transactionsAPI.createPaymentIntent(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '创建支付意图失败' });
    }
  }
);

export const confirmPayment = createAsyncThunk(
  'transactions/confirmPayment',
  async (params: {
    paymentIntentId: string;
    paymentMethodId: string;
    metadata?: Record<string, any>;
  }, { rejectWithValue }) => {
    try {
      const response = await transactionsAPI.confirmPayment(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '确认支付失败' });
    }
  }
);

export const cancelPayment = createAsyncThunk(
  'transactions/cancelPayment',
  async (paymentIntentId: string, { rejectWithValue }) => {
    try {
      const response = await transactionsAPI.cancelPayment(paymentIntentId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '取消支付失败' });
    }
  }
);

export const refundTransaction = createAsyncThunk(
  'transactions/refundTransaction',
  async (params: {
    transactionId: string;
    amount?: number;
    reason?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await transactionsAPI.refundTransaction(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '退款失败' });
    }
  }
);

export const createWithdrawalRequest = createAsyncThunk(
  'transactions/createWithdrawalRequest',
  async (params: {
    amount: number;
    method: 'paypal' | 'bank_transfer';
    details: Record<string, any>;
  }, { rejectWithValue }) => {
    try {
      const response = await transactionsAPI.createWithdrawalRequest(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '创建提现申请失败' });
    }
  }
);

export const fetchWithdrawalRequests = createAsyncThunk(
  'transactions/fetchWithdrawalRequests',
  async (params: PaginationParams & { status?: string }, { rejectWithValue }) => {
    try {
      const response = await transactionsAPI.getWithdrawalRequests(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取提现申请失败' });
    }
  }
);

export const fetchEarningsStats = createAsyncThunk(
  'transactions/fetchEarningsStats',
  async (params: {
    startDate?: string;
    endDate?: string;
    groupBy?: 'day' | 'week' | 'month';
  }, { rejectWithValue }) => {
    try {
      const response = await transactionsAPI.getEarningsStats(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取收益统计失败' });
    }
  }
);

export const fetchPaymentMethods = createAsyncThunk(
  'transactions/fetchPaymentMethods',
  async (_, { rejectWithValue }) => {
    try {
      const response = await transactionsAPI.getPaymentMethods();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取支付方式失败' });
    }
  }
);

// 支付意图接口
interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  clientSecret: string;
  metadata: Record<string, any>;
  createdAt: string;
}

// 提现申请接口
interface WithdrawalRequest {
  id: string;
  userId: string;
  amount: number;
  method: 'paypal' | 'bank_transfer';
  details: Record<string, any>;
  status: 'pending' | 'processing' | 'completed' | 'rejected';
  createdAt: string;
  processedAt?: string;
  rejectionReason?: string;
}

// 收益统计接口
interface EarningsStats {
  totalEarnings: number;
  totalTransactions: number;
  totalRefunds: number;
  periodEarnings: Array<{
    period: string;
    earnings: number;
    transactions: number;
  }>;
  topAssets: Array<{
    assetId: string;
    assetTitle: string;
    earnings: number;
    sales: number;
  }>;
}

// 支付方式接口
interface PaymentMethod {
  id: string;
  type: 'paypal' | 'stripe' | 'bank_transfer';
  name: string;
  description: string;
  isActive: boolean;
  fees: {
    fixed: number;
    percentage: number;
  };
  supportedCurrencies: string[];
}

// 分页响应接口
interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

// 状态接口
interface TransactionsState {
  // 交易列表
  transactions: Transaction[];
  currentTransaction: Transaction | null;
  
  // 支付相关
  paymentIntent: PaymentIntent | null;
  paymentMethods: PaymentMethod[];
  
  // 提现相关
  withdrawalRequests: WithdrawalRequest[];
  
  // 统计数据
  earningsStats: EarningsStats | null;
  
  // 分页信息
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  
  // 过滤器
  filters: {
    status: TransactionStatus | '';
    type: TransactionType | '';
    startDate: string;
    endDate: string;
    sortBy: string;
    sortOrder: 'asc' | 'desc';
  };
  
  // 加载状态
  isLoading: boolean;
  isLoadingMore: boolean;
  isCreatingPayment: boolean;
  isConfirmingPayment: boolean;
  isCancelingPayment: boolean;
  isRefunding: boolean;
  isCreatingWithdrawal: boolean;
  isLoadingStats: boolean;
  
  // 错误状态
  error: string | null;
  paymentError: string | null;
  
  // 支付状态
  paymentStatus: 'idle' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  
  // 余额信息
  balance: {
    available: number;
    pending: number;
    total: number;
  };
}

// 初始状态
const initialState: TransactionsState = {
  transactions: [],
  currentTransaction: null,
  paymentIntent: null,
  paymentMethods: [],
  withdrawalRequests: [],
  earningsStats: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
  },
  filters: {
    status: '',
    type: '',
    startDate: '',
    endDate: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
  },
  isLoading: false,
  isLoadingMore: false,
  isCreatingPayment: false,
  isConfirmingPayment: false,
  isCancelingPayment: false,
  isRefunding: false,
  isCreatingWithdrawal: false,
  isLoadingStats: false,
  error: null,
  paymentError: null,
  paymentStatus: 'idle',
  balance: {
    available: 0,
    pending: 0,
    total: 0,
  },
};

// 创建slice
const transactionsSlice = createSlice({
  name: 'transactions',
  initialState,
  reducers: {
    // 清除错误
    clearError: (state) => {
      state.error = null;
      state.paymentError = null;
    },
    
    // 设置过滤器
    setFilters: (state, action: PayloadAction<Partial<TransactionsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // 重置过滤器
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },
    
    // 设置支付状态
    setPaymentStatus: (state, action: PayloadAction<TransactionsState['paymentStatus']>) => {
      state.paymentStatus = action.payload;
    },
    
    // 重置支付状态
    resetPaymentStatus: (state) => {
      state.paymentStatus = 'idle';
      state.paymentIntent = null;
      state.paymentError = null;
    },
    
    // 更新余额
    updateBalance: (state, action: PayloadAction<Partial<TransactionsState['balance']>>) => {
      state.balance = { ...state.balance, ...action.payload };
    },
    
    // 添加新交易
    addTransaction: (state, action: PayloadAction<Transaction>) => {
      state.transactions.unshift(action.payload);
      state.pagination.totalItems += 1;
    },
    
    // 更新交易状态
    updateTransactionStatus: (state, action: PayloadAction<{
      id: string;
      status: TransactionStatus;
      updatedAt?: string;
    }>) => {
      const { id, status, updatedAt } = action.payload;
      const transaction = state.transactions.find(t => t.id === id);
      
      if (transaction) {
        transaction.status = status;
        if (updatedAt) {
          transaction.updatedAt = updatedAt;
        }
      }
      
      if (state.currentTransaction?.id === id) {
        state.currentTransaction.status = status;
        if (updatedAt) {
          state.currentTransaction.updatedAt = updatedAt;
        }
      }
    },
    
    // 重置交易状态
    resetTransactions: (state) => {
      state.transactions = [];
      state.pagination = initialState.pagination;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // 获取交易列表
    builder
      .addCase(fetchTransactions.pending, (state, action) => {
        if (action.meta.arg.page === 1) {
          state.isLoading = true;
        } else {
          state.isLoadingMore = true;
        }
        state.error = null;
      })
      .addCase(fetchTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        
        const { data, pagination } = action.payload.data as PaginatedResponse<Transaction>;
        
        if (action.meta.arg.page === 1) {
          state.transactions = data;
        } else {
          state.transactions.push(...data);
        }
        
        state.pagination = pagination;
        state.error = null;
      })
      .addCase(fetchTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        state.error = action.payload as string;
      });

    // 获取交易详情
    builder
      .addCase(fetchTransactionById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTransactionById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentTransaction = action.payload.data;
        state.error = null;
      })
      .addCase(fetchTransactionById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 创建支付意图
    builder
      .addCase(createPaymentIntent.pending, (state) => {
        state.isCreatingPayment = true;
        state.paymentError = null;
      })
      .addCase(createPaymentIntent.fulfilled, (state, action) => {
        state.isCreatingPayment = false;
        state.paymentIntent = action.payload.data;
        state.paymentStatus = 'processing';
        state.paymentError = null;
      })
      .addCase(createPaymentIntent.rejected, (state, action) => {
        state.isCreatingPayment = false;
        state.paymentError = action.payload as string;
        state.paymentStatus = 'failed';
      });

    // 确认支付
    builder
      .addCase(confirmPayment.pending, (state) => {
        state.isConfirmingPayment = true;
        state.paymentError = null;
      })
      .addCase(confirmPayment.fulfilled, (state, action) => {
        state.isConfirmingPayment = false;
        state.paymentStatus = 'succeeded';
        state.transactions.unshift(action.payload.data);
        state.paymentError = null;
      })
      .addCase(confirmPayment.rejected, (state, action) => {
        state.isConfirmingPayment = false;
        state.paymentError = action.payload as string;
        state.paymentStatus = 'failed';
      });

    // 取消支付
    builder
      .addCase(cancelPayment.pending, (state) => {
        state.isCancelingPayment = true;
        state.paymentError = null;
      })
      .addCase(cancelPayment.fulfilled, (state) => {
        state.isCancelingPayment = false;
        state.paymentStatus = 'canceled';
        state.paymentIntent = null;
        state.paymentError = null;
      })
      .addCase(cancelPayment.rejected, (state, action) => {
        state.isCancelingPayment = false;
        state.paymentError = action.payload as string;
      });

    // 退款
    builder
      .addCase(refundTransaction.pending, (state) => {
        state.isRefunding = true;
        state.error = null;
      })
      .addCase(refundTransaction.fulfilled, (state, action) => {
        state.isRefunding = false;
        const refundedTransaction = action.payload.data;

        // 更新交易状态
        const index = state.transactions.findIndex(t => t.id === refundedTransaction.id);
        if (index >= 0) {
          state.transactions[index] = refundedTransaction;
        }

        if (state.currentTransaction?.id === refundedTransaction.id) {
          state.currentTransaction = refundedTransaction;
        }
        
        state.error = null;
      })
      .addCase(refundTransaction.rejected, (state, action) => {
        state.isRefunding = false;
        state.error = action.payload as string;
      });

    // 创建提现申请
    builder
      .addCase(createWithdrawalRequest.pending, (state) => {
        state.isCreatingWithdrawal = true;
        state.error = null;
      })
      .addCase(createWithdrawalRequest.fulfilled, (state, action) => {
        state.isCreatingWithdrawal = false;
        state.withdrawalRequests.unshift(action.payload.data);
        state.error = null;
      })
      .addCase(createWithdrawalRequest.rejected, (state, action) => {
        state.isCreatingWithdrawal = false;
        state.error = action.payload as string;
      });

    // 获取提现申请
    builder
      .addCase(fetchWithdrawalRequests.fulfilled, (state, action) => {
        state.withdrawalRequests = action.payload.data.data;
      });

    // 获取收益统计
    builder
      .addCase(fetchEarningsStats.pending, (state) => {
        state.isLoadingStats = true;
        state.error = null;
      })
      .addCase(fetchEarningsStats.fulfilled, (state, action) => {
        state.isLoadingStats = false;
        state.earningsStats = action.payload.data;
        state.error = null;
      })
      .addCase(fetchEarningsStats.rejected, (state, action) => {
        state.isLoadingStats = false;
        state.error = action.payload as string;
      });

    // 获取支付方式
    builder
      .addCase(fetchPaymentMethods.fulfilled, (state, action) => {
        state.paymentMethods = action.payload.data;
      });
  },
});

// 导出actions
export const {
  clearError,
  setFilters,
  resetFilters,
  setPaymentStatus,
  resetPaymentStatus,
  updateBalance,
  addTransaction,
  updateTransactionStatus,
  resetTransactions,
} = transactionsSlice.actions;

// 选择器
export const selectTransactions = (state: { transactions: TransactionsState }) => state.transactions;
export const selectTransactionsList = (state: { transactions: TransactionsState }) => state.transactions.transactions;
export const selectCurrentTransaction = (state: { transactions: TransactionsState }) => state.transactions.currentTransaction;
export const selectPaymentIntent = (state: { transactions: TransactionsState }) => state.transactions.paymentIntent;
export const selectPaymentMethods = (state: { transactions: TransactionsState }) => state.transactions.paymentMethods;
export const selectWithdrawalRequests = (state: { transactions: TransactionsState }) => state.transactions.withdrawalRequests;
export const selectEarningsStats = (state: { transactions: TransactionsState }) => state.transactions.earningsStats;
export const selectPagination = (state: { transactions: TransactionsState }) => state.transactions.pagination;
export const selectFilters = (state: { transactions: TransactionsState }) => state.transactions.filters;
export const selectIsLoading = (state: { transactions: TransactionsState }) => state.transactions.isLoading;
export const selectPaymentStatus = (state: { transactions: TransactionsState }) => state.transactions.paymentStatus;
export const selectBalance = (state: { transactions: TransactionsState }) => state.transactions.balance;
export const selectError = (state: { transactions: TransactionsState }) => state.transactions.error;
export const selectPaymentError = (state: { transactions: TransactionsState }) => state.transactions.paymentError;

// 导出reducer
export default transactionsSlice.reducer;