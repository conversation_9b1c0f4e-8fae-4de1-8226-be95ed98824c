interface RegisterRequest {
    email: string;
    password: string;
    displayName: string;
    userRole: string;
}
interface LoginRequest {
    email: string;
    password: string;
}
interface AuthResponse {
    accessToken: string;
    refreshToken: string;
    user: {
        id: number;
        email: string;
        displayName: string;
        userRole: string;
        pointsBalance: number;
        isActive: boolean;
        createdAt: string;
        updatedAt: string;
    };
}
export declare class AuthService {
    private db;
    constructor();
    register(registerData: RegisterRequest): Promise<AuthResponse>;
    login(loginData: LoginRequest): Promise<AuthResponse>;
    refreshToken(refreshToken: string): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
    verifyAccessToken(token: string): Promise<any>;
    changePassword(userId: number, currentPassword: string, newPassword: string): Promise<void>;
    private generateTokens;
    requestPasswordReset(email: string): Promise<void>;
    resetPassword(resetToken: string, newPassword: string): Promise<void>;
    checkEmailAvailability(email: string): Promise<{
        available: boolean;
    }>;
    private findUserByEmail;
    private findUserById;
}
export default AuthService;
//# sourceMappingURL=auth.service.d.ts.map