import { configureStore } from '@reduxjs/toolkit';
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import authSlice from './slices/authSlice';
import uiSlice from './slices/uiSlice';
import assetsSlice from './slices/assetsSlice';
import transactionsSlice from './slices/transactionsSlice';
import userSlice from './slices/userSlice';
import themeSlice from './slices/themeSlice';
import notificationSlice from './slices/notificationSlice';
import uploadSlice from './slices/uploadSlice';

// 配置Redux store
export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    assets: assetsSlice,
    transactions: transactionsSlice,
    user: userSlice,
    theme: themeSlice,
    notification: notificationSlice,
    upload: uploadSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        ignoredPaths: ['upload.files'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// 导出store类型
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 导出类型化的hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// 导出store
export default store;