"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.closeDatabase = exports.initDatabase = exports.getRedis = exports.getDb = exports.dbManager = exports.DatabaseManager = exports.createRedisClient = exports.createPostgresPool = void 0;
const pg_1 = require("pg");
const ioredis_1 = __importDefault(require("ioredis"));
const config_1 = __importDefault(require("../config"));
const logger_1 = require("../utils/logger");
let pgPool = null;
const createPostgresPool = () => {
    if (pgPool) {
        return pgPool;
    }
    const poolConfig = {
        connectionString: config_1.default.database.url,
        max: config_1.default.database.poolSize,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
        ssl: config_1.default.database.ssl ? { rejectUnauthorized: false } : false,
    };
    pgPool = new pg_1.Pool(poolConfig);
    pgPool.on('connect', (client) => {
        logger_1.logger.info('PostgreSQL client connected');
    });
    pgPool.on('error', (err) => {
        logger_1.logger.error('PostgreSQL pool error:', err);
    });
    pgPool.on('remove', () => {
        logger_1.logger.info('PostgreSQL client removed');
    });
    return pgPool;
};
exports.createPostgresPool = createPostgresPool;
let redisClient = null;
const createRedisClient = () => {
    if (redisClient) {
        return redisClient;
    }
    const redisConfig = {
        host: config_1.default.redis.url.replace('redis://', '').split(':')[0],
        port: parseInt(config_1.default.redis.url.split(':')[2] || '6379'),
        password: config_1.default.redis.password || undefined,
        db: config_1.default.redis.db,
        connectTimeout: 10000,
        commandTimeout: 5000,
        lazyConnect: true,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        keyPrefix: 'aigc:',
    };
    redisClient = new ioredis_1.default(redisConfig);
    redisClient.on('connect', () => {
        logger_1.logger.info('Redis client connected');
    });
    redisClient.on('ready', () => {
        logger_1.logger.info('Redis client ready');
    });
    redisClient.on('error', (err) => {
        logger_1.logger.error('Redis client error:', err);
    });
    redisClient.on('close', () => {
        logger_1.logger.info('Redis client closed');
    });
    redisClient.on('reconnecting', () => {
        logger_1.logger.info('Redis client reconnecting');
    });
    return redisClient;
};
exports.createRedisClient = createRedisClient;
class DatabaseManager {
    constructor() {
        this.pgPool = null;
        this.redisClient = null;
        this.isConnected = false;
    }
    async connect() {
        try {
            this.pgPool = (0, exports.createPostgresPool)();
            const client = await this.pgPool.connect();
            const result = await client.query('SELECT NOW()');
            client.release();
            logger_1.logger.info('PostgreSQL connected successfully at:', result.rows[0].now);
            this.redisClient = (0, exports.createRedisClient)();
            await this.redisClient.ping();
            logger_1.logger.info('Redis connected successfully');
            this.isConnected = true;
        }
        catch (error) {
            logger_1.logger.error('Database connection failed:', error);
            throw error;
        }
    }
    async disconnect() {
        try {
            if (this.pgPool) {
                await this.pgPool.end();
                this.pgPool = null;
            }
            if (this.redisClient) {
                await this.redisClient.quit();
                this.redisClient = null;
            }
            this.isConnected = false;
            logger_1.logger.info('Database connections closed');
        }
        catch (error) {
            logger_1.logger.error('Error closing database connections:', error);
            throw error;
        }
    }
    getPostgresPool() {
        if (!this.pgPool) {
            throw new Error('PostgreSQL pool not initialized');
        }
        return this.pgPool;
    }
    getRedisClient() {
        if (!this.redisClient) {
            throw new Error('Redis client not initialized');
        }
        return this.redisClient;
    }
    async healthCheck() {
        const health = {
            postgres: false,
            redis: false,
            timestamp: new Date().toISOString(),
        };
        try {
            if (this.pgPool) {
                const client = await this.pgPool.connect();
                await client.query('SELECT 1');
                client.release();
                health.postgres = true;
            }
            if (this.redisClient) {
                await this.redisClient.ping();
                health.redis = true;
            }
        }
        catch (error) {
            logger_1.logger.error('Health check failed:', error);
        }
        return health;
    }
    getConnectionStats() {
        const stats = {
            postgres: {
                totalCount: 0,
                idleCount: 0,
                waitingCount: 0,
            },
            redis: {
                status: 'disconnected',
            },
        };
        if (this.pgPool) {
            stats.postgres.totalCount = this.pgPool.totalCount;
            stats.postgres.idleCount = this.pgPool.idleCount;
            stats.postgres.waitingCount = this.pgPool.waitingCount;
        }
        if (this.redisClient) {
            stats.redis.status = this.redisClient.status;
        }
        return stats;
    }
}
exports.DatabaseManager = DatabaseManager;
exports.dbManager = new DatabaseManager();
const getDb = () => exports.dbManager.getPostgresPool();
exports.getDb = getDb;
const getRedis = () => exports.dbManager.getRedisClient();
exports.getRedis = getRedis;
const initDatabase = async () => {
    await exports.dbManager.connect();
};
exports.initDatabase = initDatabase;
const closeDatabase = async () => {
    await exports.dbManager.disconnect();
};
exports.closeDatabase = closeDatabase;
process.on('SIGINT', async () => {
    logger_1.logger.info('Received SIGINT, closing database connections...');
    await (0, exports.closeDatabase)();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    logger_1.logger.info('Received SIGTERM, closing database connections...');
    await (0, exports.closeDatabase)();
    process.exit(0);
});
//# sourceMappingURL=connection.js.map