import { Pool } from 'pg';
import { getDb } from '@/database/connection';
import { 
  User, 
  UserRole, 
  BalanceResponse, 
  UserEarningsStats,
  PaginationQuery 
} from '@/types';
import { 
  UserNotFoundError, 
  AuthorizationError, 
  ValidationError,
  InsufficientPointsError 
} from '@/utils/errors';
import { logger, logBusinessOperation, logDatabaseOperation } from '@/utils/logger';

export class UserService {
  private db: Pool;

  constructor() {
    this.db = getDb();
  }

  // 获取用户信息
  async getUserById(id: number): Promise<User> {
    try {
      const query = `
        SELECT id, email, display_name, user_role, points_balance, 
               oauth_provider, oauth_id, is_active, created_at, updated_at
        FROM users
        WHERE id = $1
      `;
      
      const result = await this.db.query(query, [id]);
      
      if (result.rows.length === 0) {
        throw new UserNotFoundError();
      }

      const user = result.rows[0];
      
      return {
        id: user.id,
        email: user.email,
        displayName: user.display_name,
        userRole: user.user_role,
        pointsBalance: user.points_balance,
        oauthProvider: user.oauth_provider,
        oauthId: user.oauth_id,
        isActive: user.is_active,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
      };
    } catch (error) {
      logger.error('Failed to get user by ID:', error);
      throw error;
    }
  }

  // 获取用户余额信息
  async getUserBalance(userId: number): Promise<BalanceResponse> {
    try {
      const query = `
        SELECT 
          u.points_balance,
          COALESCE(SUM(CASE WHEN le.status = 'AVAILABLE' THEN le.amount ELSE 0 END), 0) as available_balance,
          COALESCE(SUM(CASE WHEN le.status = 'PENDING' THEN le.amount ELSE 0 END), 0) as pending_balance
        FROM users u
        LEFT JOIN ledger_entries le ON u.id = le.user_id AND le.entry_type = 'SALE_CREDIT'
        WHERE u.id = $1
        GROUP BY u.id, u.points_balance
      `;
      
      const result = await this.db.query(query, [userId]);
      
      if (result.rows.length === 0) {
        throw new UserNotFoundError();
      }

      const balance = result.rows[0];
      
      return {
        pointsBalance: balance.points_balance,
        availableBalance: parseFloat(balance.available_balance),
        pendingBalance: parseFloat(balance.pending_balance),
      };
    } catch (error) {
      logger.error('Failed to get user balance:', error);
      throw error;
    }
  }

  // 获取用户收益统计
  async getUserEarningsStats(userId: number): Promise<UserEarningsStats> {
    try {
      const query = `
        SELECT 
          u.display_name,
          u.user_role,
          COALESCE(SUM(CASE WHEN le.status = 'AVAILABLE' THEN le.amount ELSE 0 END), 0) as available_balance,
          COALESCE(SUM(CASE WHEN le.status = 'PENDING' THEN le.amount ELSE 0 END), 0) as pending_balance,
          COALESCE(SUM(CASE WHEN le.status = 'WITHDRAWN' THEN le.amount ELSE 0 END), 0) as withdrawn_balance,
          COALESCE(SUM(CASE WHEN le.entry_type = 'SALE_CREDIT' THEN le.amount ELSE 0 END), 0) as total_earnings,
          COUNT(DISTINCT a.id) as total_assets,
          COUNT(DISTINCT t.id) as total_sales
        FROM users u
        LEFT JOIN assets a ON u.id = a.creator_id
        LEFT JOIN transactions t ON a.id = t.asset_id AND t.status = 'COMPLETED'
        LEFT JOIN ledger_entries le ON u.id = le.user_id AND le.entry_type = 'SALE_CREDIT'
        WHERE u.id = $1
        GROUP BY u.id, u.display_name, u.user_role
      `;
      
      const result = await this.db.query(query, [userId]);
      
      if (result.rows.length === 0) {
        throw new UserNotFoundError();
      }

      const stats = result.rows[0];
      
      return {
        totalEarnings: parseFloat(stats.total_earnings),
        availableBalance: parseFloat(stats.available_balance),
        pendingBalance: parseFloat(stats.pending_balance),
        withdrawnBalance: parseFloat(stats.withdrawn_balance),
        totalSales: parseInt(stats.total_sales),
        totalAssets: parseInt(stats.total_assets),
      };
    } catch (error) {
      logger.error('Failed to get user earnings stats:', error);
      throw error;
    }
  }

  // 更新用户资料
  async updateUserProfile(userId: number, updates: Partial<User>): Promise<User> {
    try {
      const allowedUpdates = ['displayName'];
      const updateFields = [];
      const values = [];
      let paramIndex = 1;

      for (const [key, value] of Object.entries(updates)) {
        if (allowedUpdates.includes(key) && value !== undefined) {
          updateFields.push(`${key.replace(/([A-Z])/g, '_$1').toLowerCase()} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      }

      if (updateFields.length === 0) {
        throw new ValidationError('No valid fields to update');
      }

      updateFields.push('updated_at = CURRENT_TIMESTAMP');
      values.push(userId);

      const query = `
        UPDATE users 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, email, display_name, user_role, points_balance, 
                  oauth_provider, oauth_id, is_active, created_at, updated_at
      `;
      
      const result = await this.db.query(query, values);
      
      if (result.rows.length === 0) {
        throw new UserNotFoundError();
      }

      const user = result.rows[0];
      
      logBusinessOperation('USER_PROFILE_UPDATE', userId, updates);
      
      return {
        id: user.id,
        email: user.email,
        displayName: user.display_name,
        userRole: user.user_role,
        pointsBalance: user.points_balance,
        oauthProvider: user.oauth_provider,
        oauthId: user.oauth_id,
        isActive: user.is_active,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
      };
    } catch (error) {
      logger.error('Failed to update user profile:', error);
      throw error;
    }
  }

  // 更新用户积分余额
  async updatePointsBalance(userId: number, amount: number): Promise<void> {
    try {
      const query = `
        UPDATE users 
        SET points_balance = points_balance + $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING points_balance
      `;
      
      const result = await this.db.query(query, [amount, userId]);
      
      if (result.rows.length === 0) {
        throw new UserNotFoundError();
      }

      const newBalance = result.rows[0].points_balance;
      
      if (newBalance < 0) {
        throw new InsufficientPointsError(Math.abs(amount), newBalance - amount);
      }

      logBusinessOperation('POINTS_BALANCE_UPDATE', userId, { amount, newBalance });
    } catch (error) {
      logger.error('Failed to update points balance:', error);
      throw error;
    }
  }

  // 检查用户是否有足够积分
  async checkUserPoints(userId: number, requiredPoints: number): Promise<boolean> {
    try {
      const query = `
        SELECT points_balance
        FROM users
        WHERE id = $1
      `;
      
      const result = await this.db.query(query, [userId]);
      
      if (result.rows.length === 0) {
        throw new UserNotFoundError();
      }

      return result.rows[0].points_balance >= requiredPoints;
    } catch (error) {
      logger.error('Failed to check user points:', error);
      throw error;
    }
  }

  // 获取用户购买历史
  async getUserPurchaseHistory(userId: number, query: PaginationQuery) {
    try {
      const { page = 1, limit = 20 } = query;
      const offset = (page - 1) * limit;

      const purchaseQuery = `
        SELECT 
          t.id as transaction_id,
          t.currency,
          t.amount_usd,
          t.amount_points,
          t.created_at as purchased_at,
          a.id as asset_id,
          a.title as asset_title,
          a.asset_type,
          a.s3_file_key,
          a.cover_image_url,
          u.display_name as creator_name
        FROM transactions t
        JOIN assets a ON t.asset_id = a.id
        JOIN users u ON a.creator_id = u.id
        WHERE t.buyer_id = $1 AND t.status = 'COMPLETED'
        ORDER BY t.created_at DESC
        LIMIT $2 OFFSET $3
      `;
      
      const countQuery = `
        SELECT COUNT(*) as total
        FROM transactions t
        WHERE t.buyer_id = $1 AND t.status = 'COMPLETED'
      `;

      const [purchaseResult, countResult] = await Promise.all([
        this.db.query(purchaseQuery, [userId, limit, offset]),
        this.db.query(countQuery, [userId])
      ]);

      const purchases = purchaseResult.rows.map(row => ({
        transactionId: row.transaction_id,
        currency: row.currency,
        amountUsd: row.amount_usd,
        amountPoints: row.amount_points,
        purchasedAt: row.purchased_at,
        asset: {
          id: row.asset_id,
          title: row.asset_title,
          assetType: row.asset_type,
          coverImageUrl: row.cover_image_url,
          creatorName: row.creator_name,
        },
      }));

      const total = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / limit);

      return {
        purchases,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Failed to get user purchase history:', error);
      throw error;
    }
  }

  // 获取用户销售历史（创作者）
  async getUserSalesHistory(userId: number, query: PaginationQuery) {
    try {
      const { page = 1, limit = 20 } = query;
      const offset = (page - 1) * limit;

      const salesQuery = `
        SELECT 
          t.id as transaction_id,
          t.currency,
          t.amount_usd,
          t.amount_points,
          t.created_at as sold_at,
          a.id as asset_id,
          a.title as asset_title,
          a.asset_type,
          u.display_name as buyer_name,
          le.amount as creator_earnings
        FROM transactions t
        JOIN assets a ON t.asset_id = a.id
        JOIN users u ON t.buyer_id = u.id
        LEFT JOIN ledger_entries le ON t.id = le.transaction_id AND le.entry_type = 'SALE_CREDIT'
        WHERE a.creator_id = $1 AND t.status = 'COMPLETED'
        ORDER BY t.created_at DESC
        LIMIT $2 OFFSET $3
      `;
      
      const countQuery = `
        SELECT COUNT(*) as total
        FROM transactions t
        JOIN assets a ON t.asset_id = a.id
        WHERE a.creator_id = $1 AND t.status = 'COMPLETED'
      `;

      const [salesResult, countResult] = await Promise.all([
        this.db.query(salesQuery, [userId, limit, offset]),
        this.db.query(countQuery, [userId])
      ]);

      const sales = salesResult.rows.map(row => ({
        transactionId: row.transaction_id,
        currency: row.currency,
        amountUsd: row.amount_usd,
        amountPoints: row.amount_points,
        soldAt: row.sold_at,
        creatorEarnings: row.creator_earnings,
        asset: {
          id: row.asset_id,
          title: row.asset_title,
          assetType: row.asset_type,
        },
        buyerName: row.buyer_name,
      }));

      const total = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / limit);

      return {
        sales,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Failed to get user sales history:', error);
      throw error;
    }
  }

  // 验证用户是否拥有资产
  async verifyAssetOwnership(userId: number, assetId: number): Promise<boolean> {
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM assets
        WHERE id = $1 AND creator_id = $2
      `;
      
      const result = await this.db.query(query, [assetId, userId]);
      
      return parseInt(result.rows[0].count) > 0;
    } catch (error) {
      logger.error('Failed to verify asset ownership:', error);
      throw error;
    }
  }

  // 验证用户是否购买了资产
  async verifyAssetPurchase(userId: number, assetId: number): Promise<boolean> {
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM transactions
        WHERE buyer_id = $1 AND asset_id = $2 AND status = 'COMPLETED'
      `;
      
      const result = await this.db.query(query, [userId, assetId]);
      
      return parseInt(result.rows[0].count) > 0;
    } catch (error) {
      logger.error('Failed to verify asset purchase:', error);
      throw error;
    }
  }

  // 获取用户列表（管理员功能）
  async getUserList(query: PaginationQuery & { role?: UserRole; isActive?: boolean }) {
    try {
      const { page = 1, limit = 20, role, isActive } = query;
      const offset = (page - 1) * limit;

      const conditions = [];
      const params = [];
      let paramIndex = 1;

      if (role) {
        conditions.push(`user_role = $${paramIndex}`);
        params.push(role);
        paramIndex++;
      }

      if (isActive !== undefined) {
        conditions.push(`is_active = $${paramIndex}`);
        params.push(isActive);
        paramIndex++;
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      const usersQuery = `
        SELECT 
          id, email, display_name, user_role, points_balance, 
          oauth_provider, is_active, created_at, updated_at
        FROM users
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
      
      const countQuery = `
        SELECT COUNT(*) as total
        FROM users
        ${whereClause}
      `;

      params.push(limit, offset);

      const [usersResult, countResult] = await Promise.all([
        this.db.query(usersQuery, params),
        this.db.query(countQuery, params.slice(0, -2))
      ]);

      const users = usersResult.rows.map(row => ({
        id: row.id,
        email: row.email,
        displayName: row.display_name,
        userRole: row.user_role,
        pointsBalance: row.points_balance,
        oauthProvider: row.oauth_provider,
        isActive: row.is_active,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }));

      const total = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / limit);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Failed to get user list:', error);
      throw error;
    }
  }

  // 更新用户状态（管理员功能）
  async updateUserStatus(userId: number, isActive: boolean, adminId: number): Promise<void> {
    try {
      const query = `
        UPDATE users 
        SET is_active = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `;
      
      const result = await this.db.query(query, [isActive, userId]);
      
      if (result.rowCount === 0) {
        throw new UserNotFoundError();
      }

      logBusinessOperation('USER_STATUS_UPDATE', userId, { isActive, adminId });
    } catch (error) {
      logger.error('Failed to update user status:', error);
      throw error;
    }
  }

  // 删除用户（管理员功能）
  async deleteUser(userId: number, adminId: number): Promise<void> {
    try {
      // 软删除：将用户标记为非活跃
      const query = `
        UPDATE users 
        SET is_active = false, updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `;
      
      const result = await this.db.query(query, [userId]);
      
      if (result.rowCount === 0) {
        throw new UserNotFoundError();
      }

      logBusinessOperation('USER_DELETE', userId, { adminId });
    } catch (error) {
      logger.error('Failed to delete user:', error);
      throw error;
    }
  }
}

export default UserService;