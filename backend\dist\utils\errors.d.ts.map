{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["../../src/utils/errors.ts"], "names": [], "mappings": "AACA,qBAAa,QAAS,SAAQ,KAAK;IACjC,SAAgB,UAAU,EAAE,MAAM,CAAC;IACnC,SAAgB,IAAI,EAAE,MAAM,CAAC;IAC7B,SAAgB,OAAO,CAAC,EAAE,GAAG,CAAC;IAC9B,SAAgB,aAAa,EAAE,OAAO,CAAC;gBAGrC,OAAO,EAAE,MAAM,EACf,UAAU,GAAE,MAAY,EACxB,IAAI,GAAE,MAAgC,EACtC,OAAO,CAAC,EAAE,GAAG,EACb,aAAa,GAAE,OAAc;CAchC;AAGD,qBAAa,eAAgB,SAAQ,QAAQ;gBAC/B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,mBAAoB,SAAQ,QAAQ;gBACnC,OAAO,GAAE,MAAgC;CAGtD;AAED,qBAAa,kBAAmB,SAAQ,QAAQ;gBAClC,OAAO,GAAE,MAAmC;CAGzD;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,GAAE,MAA6B;CAGnD;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,cAAe,SAAQ,QAAQ;gBAC9B,OAAO,GAAE,MAA4B;CAGlD;AAED,qBAAa,mBAAoB,SAAQ,QAAQ;gBACnC,OAAO,GAAE,MAAgC;CAGtD;AAED,qBAAa,uBAAwB,SAAQ,QAAQ;gBACvC,OAAO,GAAE,MAA8B;CAGpD;AAGD,qBAAa,uBAAwB,SAAQ,mBAAmB;;CAI/D;AAED,qBAAa,iBAAkB,SAAQ,mBAAmB;;CAIzD;AAED,qBAAa,iBAAkB,SAAQ,mBAAmB;;CAIzD;AAED,qBAAa,iBAAkB,SAAQ,aAAa;;CAInD;AAED,qBAAa,kBAAmB,SAAQ,aAAa;;CAIpD;AAED,qBAAa,wBAAyB,SAAQ,aAAa;;CAI1D;AAED,qBAAa,uBAAwB,SAAQ,QAAQ;gBACvC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;CAMhD;AAED,qBAAa,wBAAyB,SAAQ,QAAQ;gBACxC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;CAMhD;AAED,qBAAa,sBAAuB,SAAQ,kBAAkB;;CAI7D;AAED,qBAAa,eAAgB,SAAQ,QAAQ;gBAC/B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,sBAAuB,SAAQ,QAAQ;gBACtC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,eAAgB,SAAQ,QAAQ;gBAC/B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,0BAA2B,SAAQ,QAAQ;gBAC1C,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,UAAW,SAAQ,QAAQ;gBAC1B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,OAAQ,SAAQ,QAAQ;gBACvB,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,UAAW,SAAQ,QAAQ;gBAC1B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,WAAY,SAAQ,QAAQ;gBAC3B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAED,qBAAa,UAAW,SAAQ,QAAQ;gBAC1B,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,GAAG;CAG3C;AAGD,eAAO,MAAM,UAAU,GAAI,OAAO,GAAG,KAAG,KAAK,IAAI,QAEhD,CAAC;AAEF,eAAO,MAAM,kBAAkB,GAAI,OAAO,GAAG,KAAG,OAE/C,CAAC;AAGF,eAAO,MAAM,mBAAmB,GAAI,OAAO,QAAQ,GAAG,KAAK;;;;;;;;;;;;;;CAoB1D,CAAC;AAGF,eAAO,MAAM,YAAY,GAAI,OAAO,GAAG,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,EAAE,MAAM,GAAG,QA2CrE,CAAC;AAGF,eAAO,MAAM,YAAY,GAAI,IAAI,QAAQ,MAC/B,KAAK,GAAG,EAAE,KAAK,GAAG,EAAE,MAAM,GAAG,SAGtC,CAAC;AAGF,eAAO,MAAM,aAAa,GAAI,OAAO,GAAG,KAAG,QAM1C,CAAC"}