version: '3.8'

services:
  # PostgreSQL 数据库服务 - 开发环境
  postgres:
    image: postgres:15-alpine
    container_name: aigc-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-aigc_service_hub_dev}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      POSTGRES_INITDB_ARGS: --encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/init.sql:/docker-entrypoint-initdb.d/02-init.sql:ro
      - ./database/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./database/backups:/backups
    command: >
      postgres 
      -c config_file=/etc/postgresql/postgresql.conf
      -c max_connections=50
      -c shared_buffers=128MB
      -c effective_cache_size=512MB
      -c log_statement=all
      -c log_min_duration_statement=0
    networks:
      - aigc-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-aigc_service_hub_dev}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis 缓存服务 - 开发环境
  redis:
    image: redis:7-alpine
    container_name: aigc-redis-dev
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_dev_data:/data
      - ./database/redis.conf:/etc/redis/redis.conf:ro
    command: redis-server /etc/redis/redis.conf --loglevel verbose
    networks:
      - aigc-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # 后端 API 服务 - 开发环境
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: builder
    container_name: aigc-backend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3000
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres123}@postgres:5432/${POSTGRES_DB:-aigc_service_hub_dev}
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET:-dev-jwt-secret-key}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:-dev-access-key}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY:-dev-secret-key}
      AWS_REGION: ${AWS_REGION:-us-west-2}
      S3_PRIVATE_BUCKET: ${S3_PRIVATE_BUCKET:-aigc-private-assets-dev}
      S3_PUBLIC_BUCKET: ${S3_PUBLIC_BUCKET:-aigc-public-assets-dev}
      PAYPAL_CLIENT_ID: ${PAYPAL_CLIENT_ID:-dev-paypal-client-id}
      PAYPAL_CLIENT_SECRET: ${PAYPAL_CLIENT_SECRET:-dev-paypal-client-secret}
      PAYPAL_SANDBOX: true
      FROM_EMAIL: ${FROM_EMAIL:-<EMAIL>}
      SMTP_HOST: ${SMTP_HOST:-localhost}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USER: ${SMTP_USER:-dev-smtp-user}
      SMTP_PASSWORD: ${SMTP_PASSWORD:-dev-smtp-password}
      FRONTEND_URL: http://localhost:3001
      BACKEND_URL: http://localhost:3000
      LOG_LEVEL: debug
      ENABLE_METRICS: true
    ports:
      - "${BACKEND_PORT:-3000}:3000"
    volumes:
      - ./backend/src:/app/src
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
      - ./backend/package.json:/app/package.json
      - ./backend/tsconfig.json:/app/tsconfig.json
      - ./backend/nodemon.json:/app/nodemon.json
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - aigc-dev-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    command: npm run dev

  # 前端应用服务 - 开发环境
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: builder
    container_name: aigc-frontend-dev
    restart: unless-stopped
    environment:
      REACT_APP_API_URL: http://localhost:3000/api/v1
      REACT_APP_WS_URL: ws://localhost:3000
      REACT_APP_PAYPAL_CLIENT_ID: ${PAYPAL_CLIENT_ID:-dev-paypal-client-id}
      REACT_APP_GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID:-dev-google-client-id}
      REACT_APP_GITHUB_CLIENT_ID: ${GITHUB_CLIENT_ID:-dev-github-client-id}
      REACT_APP_AWS_REGION: ${AWS_REGION:-us-west-2}
      REACT_APP_S3_PUBLIC_BUCKET: ${S3_PUBLIC_BUCKET:-aigc-public-assets-dev}
    ports:
      - "${FRONTEND_PORT:-3001}:3000"
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - ./frontend/package.json:/app/package.json
      - ./frontend/tsconfig.json:/app/tsconfig.json
    depends_on:
      - backend
    networks:
      - aigc-dev-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: npm start

  # 数据库管理工具
  adminer:
    image: adminer:4.8.1-standalone
    container_name: aigc-adminer-dev
    restart: unless-stopped
    ports:
      - "${ADMINER_PORT:-8080}:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: pepa-linha
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - aigc-dev-network

  # Redis 管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: aigc-redis-commander-dev
    restart: unless-stopped
    ports:
      - "${REDIS_COMMANDER_PORT:-8081}:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - aigc-dev-network

  # 邮件服务模拟器
  mailhog:
    image: mailhog/mailhog:latest
    container_name: aigc-mailhog-dev
    restart: unless-stopped
    ports:
      - "${MAILHOG_WEB_PORT:-8025}:8025"
      - "${MAILHOG_SMTP_PORT:-1025}:1025"
    networks:
      - aigc-dev-network

  # 文档服务
  docs:
    image: nginx:alpine
    container_name: aigc-docs-dev
    restart: unless-stopped
    ports:
      - "${DOCS_PORT:-8082}:80"
    volumes:
      - ./docs:/usr/share/nginx/html:ro
    networks:
      - aigc-dev-network

  # 热重载代理（如果需要）
  webpack-dev-server:
    image: node:18-alpine
    container_name: aigc-webpack-dev
    restart: unless-stopped
    working_dir: /app
    ports:
      - "${WEBPACK_DEV_PORT:-8083}:8080"
    volumes:
      - ./frontend:/app
    depends_on:
      - backend
    networks:
      - aigc-dev-network
    command: >
      sh -c "
        npm install &&
        npm run start:dev
      "

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  aigc-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 开发环境使用说明:
# 1. 复制 .env.development 到 .env
# 2. 启动开发环境: docker-compose -f docker-compose.dev.yml up -d
# 3. 访问应用:
#    - 前端: http://localhost:3001
#    - 后端 API: http://localhost:3000
#    - 数据库管理: http://localhost:8080
#    - Redis 管理: http://localhost:8081
#    - 邮件测试: http://localhost:8025
#    - 文档: http://localhost:8082
# 4. 查看日志: docker-compose -f docker-compose.dev.yml logs -f
# 5. 停止服务: docker-compose -f docker-compose.dev.yml down