-- 迁移文件：001_initial_schema.sql
-- 描述：创建初始数据库schema
-- 版本：1.0.0
-- 创建时间：2025-01-11
-- 作者：AIGC Service Hub Team

-- 迁移开始标记
-- Migration: 001_initial_schema
-- Description: Initial database schema creation
-- Version: 1.0.0
-- Date: 2025-01-11

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    display_name VARCHAR(255) NOT NULL,
    user_role VARCHAR(20) NOT NULL CHECK (user_role IN ('PERSONAL_CREATOR', 'ENTERPRISE_CREATOR', 'ADMIN')),
    points_balance INTEGER DEFAULT 0,
    oauth_provider VARCHAR(50),
    oauth_id VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(oauth_provider, oauth_id)
);

-- 创建标签表
CREATE TABLE IF NOT EXISTS tags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('CATEGORY', 'STYLE')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建资源表
CREATE TABLE IF NOT EXISTS assets (
    id SERIAL PRIMARY KEY,
    creator_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    asset_type VARCHAR(20) NOT NULL CHECK (asset_type IN ('MODEL', 'LORA', 'WORKFLOW', 'PROMPT', 'TOOL')),
    price_usd DECIMAL(10,2),
    price_points INTEGER,
    s3_file_key VARCHAR(500) NOT NULL,
    cover_image_url VARCHAR(500),
    status VARCHAR(20) DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'PUBLISHED', 'ARCHIVED')),
    download_count INTEGER DEFAULT 0,
    file_size BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    published_at TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT price_check CHECK (price_usd > 0 OR price_points > 0)
);

-- 创建资源标签关联表
CREATE TABLE IF NOT EXISTS asset_tags (
    asset_id INTEGER NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
    tag_id INTEGER NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    PRIMARY KEY (asset_id, tag_id)
);

-- 创建交易表
CREATE TABLE IF NOT EXISTS transactions (
    id SERIAL PRIMARY KEY,
    buyer_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    asset_id INTEGER NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
    currency VARCHAR(10) NOT NULL CHECK (currency IN ('USD', 'POINTS')),
    amount_usd DECIMAL(10,2),
    amount_points INTEGER,
    paypal_transaction_id VARCHAR(255) UNIQUE,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    refunded_at TIMESTAMP,
    CONSTRAINT amount_check CHECK (
        (currency = 'USD' AND amount_usd > 0) OR 
        (currency = 'POINTS' AND amount_points > 0)
    )
);

-- 创建账本条目表
CREATE TABLE IF NOT EXISTS ledger_entries (
    id SERIAL PRIMARY KEY,
    transaction_id INTEGER REFERENCES transactions(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    entry_type VARCHAR(20) NOT NULL CHECK (entry_type IN ('SALE_CREDIT', 'PLATFORM_FEE', 'POINTS_PURCHASE', 'POINTS_DEBIT')),
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'AVAILABLE', 'WITHDRAWN', 'REFUNDED')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cleared_at TIMESTAMP,
    withdrawn_at TIMESTAMP,
    notes TEXT
);

-- 创建提现请求表
CREATE TABLE IF NOT EXISTS withdrawal_requests (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    paypal_email VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED', 'COMPLETED')),
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    processed_by INTEGER REFERENCES users(id),
    paypal_payout_id VARCHAR(255),
    rejection_reason TEXT
);

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(20) DEFAULT 'STRING' CHECK (config_type IN ('STRING', 'NUMBER', 'BOOLEAN', 'JSON')),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建迁移记录表
CREATE TABLE IF NOT EXISTS migration_history (
    id SERIAL PRIMARY KEY,
    version VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    execution_time_ms INTEGER,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    checksum VARCHAR(64)
);

-- 创建性能优化索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_oauth ON users(oauth_provider, oauth_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(user_role);

CREATE INDEX IF NOT EXISTS idx_assets_creator ON assets(creator_id);
CREATE INDEX IF NOT EXISTS idx_assets_status ON assets(status);
CREATE INDEX IF NOT EXISTS idx_assets_type ON assets(asset_type);
CREATE INDEX IF NOT EXISTS idx_assets_published ON assets(published_at DESC) WHERE status = 'PUBLISHED';
CREATE INDEX IF NOT EXISTS idx_assets_created ON assets(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_transactions_buyer ON transactions(buyer_id);
CREATE INDEX IF NOT EXISTS idx_transactions_asset ON transactions(asset_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_created ON transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_completed ON transactions(completed_at DESC) WHERE status = 'COMPLETED';

CREATE INDEX IF NOT EXISTS idx_ledger_user ON ledger_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_ledger_transaction ON ledger_entries(transaction_id);
CREATE INDEX IF NOT EXISTS idx_ledger_status ON ledger_entries(status);
CREATE INDEX IF NOT EXISTS idx_ledger_created ON ledger_entries(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ledger_type ON ledger_entries(entry_type);

CREATE INDEX IF NOT EXISTS idx_withdrawal_user ON withdrawal_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_withdrawal_status ON withdrawal_requests(status);
CREATE INDEX IF NOT EXISTS idx_withdrawal_created ON withdrawal_requests(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_tags_type ON tags(type);
CREATE INDEX IF NOT EXISTS idx_tags_name ON tags(name);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_assets_updated_at ON assets;
CREATE TRIGGER update_assets_updated_at 
    BEFORE UPDATE ON assets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_system_configs_updated_at ON system_configs;
CREATE TRIGGER update_system_configs_updated_at 
    BEFORE UPDATE ON system_configs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建视图：用户收益统计
CREATE OR REPLACE VIEW user_earnings_summary AS
SELECT 
    u.id as user_id,
    u.display_name,
    u.user_role,
    COALESCE(SUM(CASE WHEN le.status = 'AVAILABLE' THEN le.amount ELSE 0 END), 0) as available_balance,
    COALESCE(SUM(CASE WHEN le.status = 'PENDING' THEN le.amount ELSE 0 END), 0) as pending_balance,
    COALESCE(SUM(CASE WHEN le.status = 'WITHDRAWN' THEN le.amount ELSE 0 END), 0) as withdrawn_balance,
    COALESCE(SUM(CASE WHEN le.entry_type = 'SALE_CREDIT' THEN le.amount ELSE 0 END), 0) as total_earnings,
    COUNT(DISTINCT a.id) as total_assets,
    COUNT(DISTINCT t.id) as total_sales
FROM users u
LEFT JOIN assets a ON u.id = a.creator_id
LEFT JOIN transactions t ON a.id = t.asset_id AND t.status = 'COMPLETED'
LEFT JOIN ledger_entries le ON u.id = le.user_id AND le.entry_type = 'SALE_CREDIT'
WHERE u.user_role IN ('PERSONAL_CREATOR', 'ENTERPRISE_CREATOR')
GROUP BY u.id, u.display_name, u.user_role;

-- 创建视图：资产销售统计
CREATE OR REPLACE VIEW asset_sales_summary AS
SELECT 
    a.id as asset_id,
    a.title,
    a.asset_type,
    a.price_usd,
    a.price_points,
    a.download_count,
    COUNT(t.id) as total_sales,
    COALESCE(SUM(CASE WHEN t.currency = 'USD' THEN t.amount_usd ELSE 0 END), 0) as total_usd_revenue,
    COALESCE(SUM(CASE WHEN t.currency = 'POINTS' THEN t.amount_points ELSE 0 END), 0) as total_points_revenue,
    a.created_at,
    a.published_at,
    u.display_name as creator_name
FROM assets a
LEFT JOIN transactions t ON a.id = t.asset_id AND t.status = 'COMPLETED'
LEFT JOIN users u ON a.creator_id = u.id
GROUP BY a.id, a.title, a.asset_type, a.price_usd, a.price_points, a.download_count, a.created_at, a.published_at, u.display_name;

-- 创建分佣计算函数
CREATE OR REPLACE FUNCTION calculate_commission(
    p_asset_id INTEGER,
    p_amount_usd DECIMAL(10,2)
) RETURNS TABLE(
    platform_amount DECIMAL(10,2),
    creator_amount DECIMAL(10,2),
    platform_percentage INTEGER
) AS $$
DECLARE
    v_creator_role VARCHAR(20);
    v_sales_count INTEGER;
    v_platform_percentage INTEGER;
BEGIN
    -- 获取创作者角色
    SELECT u.user_role INTO v_creator_role
    FROM users u
    JOIN assets a ON u.id = a.creator_id
    WHERE a.id = p_asset_id;
    
    -- 计算历史销售次数
    SELECT COUNT(*) INTO v_sales_count
    FROM transactions t
    WHERE t.asset_id = p_asset_id 
    AND t.status = 'COMPLETED' 
    AND t.currency = 'USD';
    
    -- 计算平台分佣比例
    IF v_creator_role = 'PERSONAL_CREATOR' THEN
        v_platform_percentage := LEAST(5 + (v_sales_count * 5), 50);
    ELSIF v_creator_role = 'ENTERPRISE_CREATOR' THEN
        v_platform_percentage := LEAST(8 + (v_sales_count * 8), 56);
    ELSE
        v_platform_percentage := 0;
    END IF;
    
    RETURN QUERY SELECT 
        ROUND(p_amount_usd * v_platform_percentage / 100, 2) as platform_amount,
        ROUND(p_amount_usd * (100 - v_platform_percentage) / 100, 2) as creator_amount,
        v_platform_percentage as platform_percentage;
END;
$$ LANGUAGE plpgsql;

-- 创建交易完成触发器函数
CREATE OR REPLACE FUNCTION process_transaction_completion()
RETURNS TRIGGER AS $$
DECLARE
    v_commission RECORD;
    v_creator_id INTEGER;
BEGIN
    -- 只处理USD交易的状态变更为COMPLETED
    IF OLD.status != 'COMPLETED' AND NEW.status = 'COMPLETED' AND NEW.currency = 'USD' THEN
        -- 获取创作者ID
        SELECT creator_id INTO v_creator_id FROM assets WHERE id = NEW.asset_id;
        
        -- 计算分佣
        SELECT * INTO v_commission FROM calculate_commission(NEW.asset_id, NEW.amount_usd);
        
        -- 创建创作者收益账本条目
        INSERT INTO ledger_entries (
            transaction_id, user_id, amount, entry_type, status
        ) VALUES (
            NEW.id, v_creator_id, v_commission.creator_amount, 'SALE_CREDIT', 'PENDING'
        );
        
        -- 创建平台费用账本条目
        INSERT INTO ledger_entries (
            transaction_id, user_id, amount, entry_type, status
        ) VALUES (
            NEW.id, v_creator_id, v_commission.platform_amount, 'PLATFORM_FEE', 'AVAILABLE'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建积分交易处理函数
CREATE OR REPLACE FUNCTION process_points_transaction()
RETURNS TRIGGER AS $$
DECLARE
    v_creator_id INTEGER;
BEGIN
    -- 处理积分交易完成
    IF OLD.status != 'COMPLETED' AND NEW.status = 'COMPLETED' AND NEW.currency = 'POINTS' THEN
        -- 获取创作者ID
        SELECT creator_id INTO v_creator_id FROM assets WHERE id = NEW.asset_id;
        
        -- 扣除买家积分
        UPDATE users SET points_balance = points_balance - NEW.amount_points 
        WHERE id = NEW.buyer_id;
        
        -- 创建积分扣除记录
        INSERT INTO ledger_entries (
            transaction_id, user_id, amount, entry_type, status
        ) VALUES (
            NEW.id, NEW.buyer_id, NEW.amount_points, 'POINTS_DEBIT', 'AVAILABLE'
        );
        
        -- 增加资产下载次数
        UPDATE assets SET download_count = download_count + 1 WHERE id = NEW.asset_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS trigger_transaction_completion ON transactions;
CREATE TRIGGER trigger_transaction_completion
    AFTER UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION process_transaction_completion();

DROP TRIGGER IF EXISTS trigger_points_transaction ON transactions;
CREATE TRIGGER trigger_points_transaction
    AFTER UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION process_points_transaction();

-- 添加数据完整性约束
ALTER TABLE assets ADD CONSTRAINT IF NOT EXISTS assets_file_size_check CHECK (file_size >= 0);
ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS users_points_balance_check CHECK (points_balance >= 0);
ALTER TABLE transactions ADD CONSTRAINT IF NOT EXISTS transactions_paypal_id_check 
    CHECK (currency != 'USD' OR paypal_transaction_id IS NOT NULL);

-- 记录迁移执行
INSERT INTO migration_history (version, name, execution_time_ms) VALUES 
('001', '001_initial_schema', 0);

-- 迁移完成标记
-- Migration 001_initial_schema completed successfully