import { Request, Response } from 'express';
import { UserService } from '@/services/user.service';
import { User, PaginationQuery, UserRole } from '@/types';
import { ValidationError, UserNotFoundError } from '@/utils/errors';
import { asyncHandler } from '@/utils/errors';
import { logger, logBusinessOperation } from '@/utils/logger';

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  // 获取用户资料
  getProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const user = await this.userService.getUserById(req.user.id);

    res.json({
      success: true,
      data: { user },
      message: 'User profile retrieved successfully',
    });
  });

  // 更新用户资料
  updateProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const { displayName } = req.body;
    const updates: Partial<User> = {};

    if (displayName !== undefined) {
      updates.displayName = displayName;
    }

    const updatedUser = await this.userService.updateUserProfile(req.user.id, updates);

    res.json({
      success: true,
      data: { user: updatedUser },
      message: 'User profile updated successfully',
    });
  });

  // 获取用户余额
  getBalance = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const balance = await this.userService.getUserBalance(req.user.id);

    res.json({
      success: true,
      data: balance,
      message: 'User balance retrieved successfully',
    });
  });

  // 获取用户收益统计
  getEarningsStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const stats = await this.userService.getUserEarningsStats(req.user.id);

    res.json({
      success: true,
      data: stats,
      message: 'User earnings stats retrieved successfully',
    });
  });

  // 获取用户购买历史
  getPurchaseHistory = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const query: PaginationQuery = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
    };

    const result = await this.userService.getUserPurchaseHistory(req.user.id, query);

    res.json({
      success: true,
      data: result,
      message: 'Purchase history retrieved successfully',
    });
  });

  // 获取用户销售历史
  getSalesHistory = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const query: PaginationQuery = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
    };

    const result = await this.userService.getUserSalesHistory(req.user.id, query);

    res.json({
      success: true,
      data: result,
      message: 'Sales history retrieved successfully',
    });
  });

  // 获取用户列表（管理员）
  getUserList = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const query = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      role: req.query.role as UserRole,
      isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
    };

    const result = await this.userService.getUserList(query);

    res.json({
      success: true,
      data: result,
      message: 'User list retrieved successfully',
    });
  });

  // 获取用户详情（管理员）
  getUserById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const userId = parseInt(req.params.id);
    
    if (isNaN(userId)) {
      throw new ValidationError('Invalid user ID');
    }

    const user = await this.userService.getUserById(userId);

    res.json({
      success: true,
      data: { user },
      message: 'User details retrieved successfully',
    });
  });

  // 更新用户状态（管理员）
  updateUserStatus = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const userId = parseInt(req.params.id);
    const { isActive } = req.body;
    
    if (isNaN(userId)) {
      throw new ValidationError('Invalid user ID');
    }

    if (typeof isActive !== 'boolean') {
      throw new ValidationError('isActive must be a boolean');
    }

    await this.userService.updateUserStatus(userId, isActive, req.user.id);

    res.json({
      success: true,
      message: 'User status updated successfully',
    });
  });

  // 删除用户（管理员）
  deleteUser = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const userId = parseInt(req.params.id);
    
    if (isNaN(userId)) {
      throw new ValidationError('Invalid user ID');
    }

    await this.userService.deleteUser(userId, req.user.id);

    res.json({
      success: true,
      message: 'User deleted successfully',
    });
  });
}

export default UserController;