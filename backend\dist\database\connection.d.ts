import { Pool } from 'pg';
import Redis from 'ioredis';
export declare const createPostgresPool: () => Pool;
export declare const createRedisClient: () => Redis;
export declare class DatabaseManager {
    private pgPool;
    private redisClient;
    private isConnected;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    getPostgresPool(): Pool;
    getRedisClient(): Redis;
    healthCheck(): Promise<{
        postgres: boolean;
        redis: boolean;
        timestamp: string;
    }>;
    getConnectionStats(): {
        postgres: {
            totalCount: number;
            idleCount: number;
            waitingCount: number;
        };
        redis: {
            status: string;
        };
    };
}
export declare const dbManager: DatabaseManager;
export declare const getDb: () => Pool;
export declare const getRedis: () => Redis;
export declare const initDatabase: () => Promise<void>;
export declare const closeDatabase: () => Promise<void>;
//# sourceMappingURL=connection.d.ts.map