"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileService = void 0;
const connection_1 = require("../database/connection");
const errors_1 = require("../utils/errors");
const logger_1 = require("../utils/logger");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
class FileService {
    constructor() { }
    async uploadFile(fileData) {
        const db = (0, connection_1.getDb)();
        const client = await db.connect();
        try {
            await client.query('BEGIN');
            const query = `
        INSERT INTO files (
          original_name, file_name, file_path, file_size, 
          mime_type, uploaded_by, description, tags
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;
            const values = [
                fileData.originalName,
                fileData.fileName,
                fileData.filePath,
                fileData.fileSize,
                fileData.mimeType,
                fileData.uploadedBy,
                fileData.description || null,
                fileData.tags || null,
            ];
            const result = await client.query(query, values);
            await client.query('COMMIT');
            const file = result.rows[0];
            if (this.isImage(fileData.mimeType)) {
                this.generateThumbnail(fileData.filePath, file.id).catch(err => {
                    logger_1.logger.error('Failed to generate thumbnail:', err);
                });
            }
            return this.mapFileRecord(file);
        }
        catch (error) {
            await client.query('ROLLBACK');
            logger_1.logger.error('File upload error:', error);
            throw new errors_1.FileUploadError('Failed to upload file');
        }
        finally {
            client.release();
        }
    }
    async uploadMultipleFiles(filesData) {
        const db = (0, connection_1.getDb)();
        const client = await db.connect();
        try {
            await client.query('BEGIN');
            const uploadedFiles = [];
            for (const fileData of filesData) {
                const query = `
          INSERT INTO files (
            original_name, file_name, file_path, file_size, 
            mime_type, uploaded_by, description, tags
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          RETURNING *
        `;
                const values = [
                    fileData.originalName,
                    fileData.fileName,
                    fileData.filePath,
                    fileData.fileSize,
                    fileData.mimeType,
                    fileData.uploadedBy,
                    fileData.description || null,
                    fileData.tags || null,
                ];
                const result = await client.query(query, values);
                const file = result.rows[0];
                uploadedFiles.push(this.mapFileRecord(file));
                if (this.isImage(fileData.mimeType)) {
                    this.generateThumbnail(fileData.filePath, file.id).catch(err => {
                        logger_1.logger.error('Failed to generate thumbnail:', err);
                    });
                }
            }
            await client.query('COMMIT');
            return uploadedFiles;
        }
        catch (error) {
            await client.query('ROLLBACK');
            logger_1.logger.error('Multiple files upload error:', error);
            throw new errors_1.FileUploadError('Failed to upload files');
        }
        finally {
            client.release();
        }
    }
    async getFiles(filters) {
        const db = (0, connection_1.getDb)();
        const client = await db.connect();
        try {
            let whereClause = '';
            const queryParams = [];
            let paramIndex = 1;
            const conditions = [];
            if (filters.userId) {
                conditions.push(`uploaded_by = $${paramIndex++}`);
                queryParams.push(filters.userId);
            }
            if (filters.search) {
                conditions.push(`original_name ILIKE $${paramIndex++}`);
                queryParams.push(`%${filters.search}%`);
            }
            if (filters.fileType) {
                conditions.push(`mime_type LIKE $${paramIndex++}`);
                queryParams.push(`${filters.fileType}%`);
            }
            if (conditions.length > 0) {
                whereClause = `WHERE ${conditions.join(' AND ')}`;
            }
            const countQuery = `SELECT COUNT(*) FROM files ${whereClause}`;
            const countResult = await client.query(countQuery, queryParams);
            const total = parseInt(countResult.rows[0].count);
            const offset = (filters.page - 1) * filters.limit;
            const dataQuery = `
        SELECT * FROM files 
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT $${paramIndex++} OFFSET $${paramIndex++}
      `;
            queryParams.push(filters.limit, offset);
            const dataResult = await client.query(dataQuery, queryParams);
            const files = dataResult.rows.map(file => this.mapFileRecord(file));
            const totalPages = Math.ceil(total / filters.limit);
            return {
                files,
                total,
                page: filters.page,
                limit: filters.limit,
                totalPages,
            };
        }
        catch (error) {
            logger_1.logger.error('Get files error:', error);
            throw error;
        }
        finally {
            client.release();
        }
    }
    async getFileById(fileId) {
        const db = (0, connection_1.getDb)();
        const client = await db.connect();
        try {
            const query = 'SELECT * FROM files WHERE id = $1';
            const result = await client.query(query, [fileId]);
            if (result.rows.length === 0) {
                return null;
            }
            return this.mapFileRecord(result.rows[0]);
        }
        catch (error) {
            logger_1.logger.error('Get file by ID error:', error);
            throw error;
        }
        finally {
            client.release();
        }
    }
    async deleteFile(fileId) {
        const db = (0, connection_1.getDb)();
        const client = await db.connect();
        try {
            await client.query('BEGIN');
            const file = await this.getFileById(fileId);
            if (!file) {
                throw new errors_1.NotFoundError('File not found');
            }
            const deleteQuery = 'DELETE FROM files WHERE id = $1';
            await client.query(deleteQuery, [fileId]);
            if (fs_1.default.existsSync(file.filePath)) {
                fs_1.default.unlinkSync(file.filePath);
            }
            const thumbnailPath = this.getThumbnailPath(fileId);
            if (fs_1.default.existsSync(thumbnailPath)) {
                fs_1.default.unlinkSync(thumbnailPath);
            }
            await client.query('COMMIT');
        }
        catch (error) {
            await client.query('ROLLBACK');
            logger_1.logger.error('Delete file error:', error);
            throw error;
        }
        finally {
            client.release();
        }
    }
    async updateFile(fileId, updates) {
        const db = (0, connection_1.getDb)();
        const client = await db.connect();
        try {
            const updateFields = [];
            const queryParams = [];
            let paramIndex = 1;
            if (updates.description !== undefined) {
                updateFields.push(`description = $${paramIndex++}`);
                queryParams.push(updates.description);
            }
            if (updates.tags !== undefined) {
                updateFields.push(`tags = $${paramIndex++}`);
                queryParams.push(updates.tags);
            }
            if (updateFields.length === 0) {
                throw new errors_1.ValidationError('No fields to update');
            }
            updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
            queryParams.push(fileId);
            const query = `
        UPDATE files 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
      `;
            const result = await client.query(query, queryParams);
            if (result.rows.length === 0) {
                throw new errors_1.NotFoundError('File not found');
            }
            return this.mapFileRecord(result.rows[0]);
        }
        catch (error) {
            logger_1.logger.error('Update file error:', error);
            throw error;
        }
        finally {
            client.release();
        }
    }
    async getFilePath(fileId) {
        const file = await this.getFileById(fileId);
        if (!file) {
            throw new errors_1.NotFoundError('File not found');
        }
        return file.filePath;
    }
    getThumbnailPath(fileId) {
        return path_1.default.join('uploads', 'thumbnails', `${fileId}.jpg`);
    }
    isImage(mimeType) {
        return mimeType.startsWith('image/');
    }
    async generateThumbnail(filePath, fileId) {
        try {
            const thumbnailDir = path_1.default.join('uploads', 'thumbnails');
            if (!fs_1.default.existsSync(thumbnailDir)) {
                fs_1.default.mkdirSync(thumbnailDir, { recursive: true });
            }
            const thumbnailPath = this.getThumbnailPath(fileId);
            fs_1.default.writeFileSync(thumbnailPath, 'placeholder');
            logger_1.logger.info(`Thumbnail placeholder created for file ${fileId}`);
        }
        catch (error) {
            logger_1.logger.error('Thumbnail generation error:', error);
        }
    }
    mapFileRecord(row) {
        return {
            id: row.id,
            originalName: row.original_name,
            fileName: row.file_name,
            filePath: row.file_path,
            fileSize: row.file_size,
            mimeType: row.mime_type,
            uploadedBy: row.uploaded_by,
            description: row.description,
            tags: row.tags,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
        };
    }
}
exports.FileService = FileService;
//# sourceMappingURL=file.service.js.map