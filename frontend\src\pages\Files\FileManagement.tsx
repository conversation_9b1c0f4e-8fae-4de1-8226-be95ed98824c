import React, { useState } from 'react';
import {
  Box,
  <PERSON>rid,
  Card,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  LinearProgress,
  Menu,
  MenuItem,
  Breadcrumbs,
  Link,
  Divider,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  useTheme,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Folder as FolderIcon,
  InsertDriveFile as FileIcon,
  Image as ImageIcon,
  AudioFile as AudioIcon,
  VideoFile as VideoIcon,
  PictureAsPdf as PdfIcon,
  Archive as ArchiveIcon,
  Code as CodeIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Share as ShareIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  ViewList as ViewListIcon,
  ViewModule as ViewModuleIcon,
  CreateNewFolder as CreateFolderIcon,
  Home as HomeIcon,
  ChevronRight as ChevronRightIcon,
  CloudDownload as CloudDownloadIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

interface FileItem {
  id: string;
  name: string;
  type: 'file' | 'folder';
  size?: number;
  mimeType?: string;
  createdAt: string;
  modifiedAt: string;
  path: string;
  isShared?: boolean;
  downloadCount?: number;
  thumbnailUrl?: string;
}

const FileManagement: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [currentPath, setCurrentPath] = useState('/');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [newFolderDialogOpen, setNewFolderDialogOpen] = useState(false);
  const [actionMenuAnchor, setActionMenuAnchor] = useState<null | HTMLElement>(null);
  const [actionMenuFile, setActionMenuFile] = useState<FileItem | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');

  // 模拟文件数据
  const files: FileItem[] = [
    {
      id: '1',
      name: '资产图片',
      type: 'folder',
      createdAt: '2024-01-15',
      modifiedAt: '2024-01-20',
      path: '/assets-images',
    },
    {
      id: '2',
      name: 'AI生成头像.png',
      type: 'file',
      size: 2048576,
      mimeType: 'image/png',
      createdAt: '2024-01-20',
      modifiedAt: '2024-01-20',
      path: '/ai-avatar.png',
      downloadCount: 156,
      thumbnailUrl: '/thumbnails/ai-avatar.png',
    },
    {
      id: '3',
      name: '背景音乐.mp3',
      type: 'file',
      size: 5242880,
      mimeType: 'audio/mpeg',
      createdAt: '2024-01-18',
      modifiedAt: '2024-01-18',
      path: '/background-music.mp3',
      downloadCount: 89,
    },
    {
      id: '4',
      name: '产品介绍.pdf',
      type: 'file',
      size: 1024000,
      mimeType: 'application/pdf',
      createdAt: '2024-01-16',
      modifiedAt: '2024-01-19',
      path: '/product-intro.pdf',
      downloadCount: 234,
    },
    {
      id: '5',
      name: '视频素材',
      type: 'folder',
      createdAt: '2024-01-12',
      modifiedAt: '2024-01-17',
      path: '/video-materials',
    },
  ];

  const storageStats = {
    used: 45.6,
    total: 100,
    usedBytes: 45600000000,
    totalBytes: 100000000000,
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (file: FileItem) => {
    if (file.type === 'folder') {
      return <FolderIcon color="primary" />;
    }
    
    if (file.mimeType) {
      if (file.mimeType.startsWith('image/')) {
        return <ImageIcon color="success" />;
      } else if (file.mimeType.startsWith('audio/')) {
        return <AudioIcon color="secondary" />;
      } else if (file.mimeType.startsWith('video/')) {
        return <VideoIcon color="error" />;
      } else if (file.mimeType === 'application/pdf') {
        return <PdfIcon color="error" />;
      } else if (file.mimeType.includes('zip') || file.mimeType.includes('rar')) {
        return <ArchiveIcon color="warning" />;
      } else if (file.mimeType.includes('text/') || file.mimeType.includes('code')) {
        return <CodeIcon color="info" />;
      }
    }
    
    return <FileIcon />;
  };

  const handleFileClick = (file: FileItem) => {
    if (file.type === 'folder') {
      setCurrentPath(file.path);
    }
  };

  const handleActionMenuOpen = (event: React.MouseEvent<HTMLElement>, file: FileItem) => {
    setActionMenuAnchor(event.currentTarget);
    setActionMenuFile(file);
  };

  const handleActionMenuClose = () => {
    setActionMenuAnchor(null);
    setActionMenuFile(null);
  };

  const handleUpload = () => {
    setUploading(true);
    setUploadProgress(0);
    
    // 模拟上传进度
    const timer = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(timer);
          setUploading(false);
          setUploadDialogOpen(false);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  };

  const handleCreateFolder = () => {
    if (newFolderName.trim()) {
      // 创建文件夹逻辑
      setNewFolderDialogOpen(false);
      setNewFolderName('');
    }
  };

  const pathSegments = currentPath.split('/').filter(Boolean);

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          文件管理
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<CreateFolderIcon />}
            onClick={() => setNewFolderDialogOpen(true)}
          >
            新建文件夹
          </Button>
          <Button
            variant="contained"
            startIcon={<CloudUploadIcon />}
            onClick={() => setUploadDialogOpen(true)}
          >
            上传文件
          </Button>
        </Box>
      </Box>

      {/* 存储统计 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                存储使用情况
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  已使用 {formatFileSize(storageStats.usedBytes)} / {formatFileSize(storageStats.totalBytes)}
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={storageStats.used}
                sx={{ height: 10, borderRadius: 5 }}
              />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                文件统计
              </Typography>
              <Typography variant="body2" color="text.secondary">
                总文件数: {files.filter(f => f.type === 'file').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                文件夹数: {files.filter(f => f.type === 'folder').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 面包屑导航 */}
      <Box sx={{ mb: 2 }}>
        <Breadcrumbs separator={<ChevronRightIcon fontSize="small" />}>
          <Link
            color="inherit"
            href="#"
            onClick={() => setCurrentPath('/')}
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            根目录
          </Link>
          {pathSegments.map((segment, index) => (
            <Link
              key={index}
              color="inherit"
              href="#"
              onClick={() => setCurrentPath('/' + pathSegments.slice(0, index + 1).join('/'))}
            >
              {segment}
            </Link>
          ))}
        </Breadcrumbs>
      </Box>

      {/* 搜索和视图控制 */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, alignItems: 'center' }}>
        <TextField
          placeholder="搜索文件..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ flex: 1 }}
        />
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton
            color={viewMode === 'list' ? 'primary' : 'default'}
            onClick={() => setViewMode('list')}
          >
            <ViewListIcon />
          </IconButton>
          <IconButton
            color={viewMode === 'grid' ? 'primary' : 'default'}
            onClick={() => setViewMode('grid')}
          >
            <ViewModuleIcon />
          </IconButton>
        </Box>
      </Box>

      {/* 文件列表 */}
      <Card>
        <List>
          {files.map((file, index) => (
            <React.Fragment key={file.id}>
              <ListItem
                button
                onClick={() => handleFileClick(file)}
                sx={{
                  '&:hover': {
                    backgroundColor: theme.palette.action.hover,
                  },
                }}
              >
                <ListItemIcon>
                  {getFileIcon(file)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body1">{file.name}</Typography>
                      {file.isShared && (
                        <Chip label="共享" size="small" color="primary" />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box sx={{ display: 'flex', gap: 2, mt: 0.5 }}>
                      <Typography variant="caption" color="text.secondary">
                        {file.type === 'file' ? formatFileSize(file.size || 0) : '文件夹'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        修改时间: {file.modifiedAt}
                      </Typography>
                      {file.downloadCount && (
                        <Typography variant="caption" color="text.secondary">
                          下载: {file.downloadCount}次
                        </Typography>
                      )}
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleActionMenuOpen(e, file);
                    }}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
              {index < files.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      </Card>

      {/* 操作菜单 */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionMenuClose}
      >
        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <DownloadIcon />
          </ListItemIcon>
          <ListItemText>下载</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <EditIcon />
          </ListItemIcon>
          <ListItemText>重命名</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <ShareIcon />
          </ListItemIcon>
          <ListItemText>分享</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <DeleteIcon />
          </ListItemIcon>
          <ListItemText>删除</ListItemText>
        </MenuItem>
      </Menu>

      {/* 上传对话框 */}
      <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>上传文件</DialogTitle>
        <DialogContent>
          <Box
            sx={{
              border: '2px dashed',
              borderColor: 'divider',
              borderRadius: 2,
              p: 4,
              textAlign: 'center',
              my: 2,
            }}
          >
            <CloudUploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              拖拽文件到这里或点击选择
            </Typography>
            <Typography variant="body2" color="text.secondary">
              支持最大 10MB 的文件
            </Typography>
            <Button variant="outlined" sx={{ mt: 2 }}>
              选择文件
            </Button>
          </Box>
          
          {uploading && (
            <Box sx={{ mt: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2" sx={{ mr: 1 }}>
                  上传进度:
                </Typography>
                <Typography variant="body2" color="primary">
                  {uploadProgress}%
                </Typography>
              </Box>
              <LinearProgress variant="determinate" value={uploadProgress} />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)} disabled={uploading}>
            取消
          </Button>
          <Button onClick={handleUpload} variant="contained" disabled={uploading}>
            {uploading ? <CircularProgress size={20} sx={{ mr: 1 }} /> : null}
            上传
          </Button>
        </DialogActions>
      </Dialog>

      {/* 新建文件夹对话框 */}
      <Dialog open={newFolderDialogOpen} onClose={() => setNewFolderDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>新建文件夹</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="文件夹名称"
            fullWidth
            variant="outlined"
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewFolderDialogOpen(false)}>
            取消
          </Button>
          <Button onClick={handleCreateFolder} variant="contained">
            创建
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FileManagement;