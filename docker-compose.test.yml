version: '3.8'

services:
  # PostgreSQL 数据库服务 - 测试环境
  postgres-test:
    image: postgres:15-alpine
    container_name: aigc-postgres-test
    restart: "no"
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-aigc_service_hub_test}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      POSTGRES_INITDB_ARGS: --encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "${POSTGRES_TEST_PORT:-5433}:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/init.sql:/docker-entrypoint-initdb.d/02-init.sql:ro
      - ./database/test-data.sql:/docker-entrypoint-initdb.d/03-test-data.sql:ro
    command: >
      postgres 
      -c fsync=off
      -c synchronous_commit=off
      -c full_page_writes=off
      -c checkpoint_segments=32
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c shared_buffers=256MB
    networks:
      - aigc-test-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-aigc_service_hub_test}"]
      interval: 5s
      timeout: 3s
      retries: 3
      start_period: 10s
    tmpfs:
      - /var/lib/postgresql/data/pg_wal

  # Redis 缓存服务 - 测试环境
  redis-test:
    image: redis:7-alpine
    container_name: aigc-redis-test
    restart: "no"
    ports:
      - "${REDIS_TEST_PORT:-6380}:6379"
    command: redis-server --save "" --appendonly no
    networks:
      - aigc-test-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 3
    tmpfs:
      - /data

  # 后端 API 服务 - 测试环境
  backend-test:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: builder
    container_name: aigc-backend-test
    restart: "no"
    environment:
      NODE_ENV: test
      PORT: 3000
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres123}@postgres-test:5432/${POSTGRES_DB:-aigc_service_hub_test}
      REDIS_URL: redis://redis-test:6379
      JWT_SECRET: test-jwt-secret-key
      AWS_ACCESS_KEY_ID: test-access-key
      AWS_SECRET_ACCESS_KEY: test-secret-key
      AWS_REGION: us-west-2
      S3_PRIVATE_BUCKET: test-private-bucket
      S3_PUBLIC_BUCKET: test-public-bucket
      PAYPAL_CLIENT_ID: test-paypal-client-id
      PAYPAL_CLIENT_SECRET: test-paypal-client-secret
      PAYPAL_SANDBOX: true
      FROM_EMAIL: <EMAIL>
      SMTP_HOST: mailhog-test
      SMTP_PORT: 1025
      SMTP_USER: test-smtp-user
      SMTP_PASSWORD: test-smtp-password
      FRONTEND_URL: http://localhost:3001
      BACKEND_URL: http://localhost:3000
      LOG_LEVEL: error
      ENABLE_METRICS: false
    ports:
      - "${BACKEND_TEST_PORT:-3002}:3000"
    volumes:
      - ./backend/src:/app/src
      - ./backend/test:/app/test
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
      - ./backend/coverage:/app/coverage
    depends_on:
      postgres-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - aigc-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 20s
    command: npm run test:watch

  # 前端应用服务 - 测试环境
  frontend-test:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: builder
    container_name: aigc-frontend-test
    restart: "no"
    environment:
      REACT_APP_API_URL: http://localhost:3002/api/v1
      REACT_APP_WS_URL: ws://localhost:3002
      REACT_APP_PAYPAL_CLIENT_ID: test-paypal-client-id
      REACT_APP_GOOGLE_CLIENT_ID: test-google-client-id
      REACT_APP_GITHUB_CLIENT_ID: test-github-client-id
      REACT_APP_AWS_REGION: us-west-2
      REACT_APP_S3_PUBLIC_BUCKET: test-public-bucket
      CI: true
    ports:
      - "${FRONTEND_TEST_PORT:-3003}:3000"
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/test:/app/test
      - ./frontend/coverage:/app/coverage
    depends_on:
      backend-test:
        condition: service_healthy
    networks:
      - aigc-test-network
    command: npm run test:ci

  # E2E 测试服务
  e2e-test:
    build:
      context: ./frontend
      dockerfile: Dockerfile.e2e
    container_name: aigc-e2e-test
    restart: "no"
    environment:
      BASE_URL: http://frontend-test:3000
      API_URL: http://backend-test:3000/api/v1
    volumes:
      - ./frontend/e2e:/app/e2e
      - ./frontend/test-results:/app/test-results
      - ./frontend/playwright-report:/app/playwright-report
    depends_on:
      backend-test:
        condition: service_healthy
      frontend-test:
        condition: service_started
    networks:
      - aigc-test-network
    command: npm run test:e2e

  # 邮件服务模拟器 - 测试环境
  mailhog-test:
    image: mailhog/mailhog:latest
    container_name: aigc-mailhog-test
    restart: "no"
    ports:
      - "${MAILHOG_TEST_WEB_PORT:-8026}:8025"
      - "${MAILHOG_TEST_SMTP_PORT:-1026}:1025"
    networks:
      - aigc-test-network

  # 数据库种子数据服务
  db-seed:
    image: postgres:15-alpine
    container_name: aigc-db-seed-test
    restart: "no"
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-aigc_service_hub_test}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      POSTGRES_HOST: postgres-test
    volumes:
      - ./database/test-data.sql:/test-data.sql:ro
      - ./database/seed-test.sh:/seed-test.sh:ro
    depends_on:
      postgres-test:
        condition: service_healthy
    networks:
      - aigc-test-network
    command: /seed-test.sh

  # 性能测试服务
  performance-test:
    image: loadimpact/k6:latest
    container_name: aigc-performance-test
    restart: "no"
    volumes:
      - ./performance-tests:/scripts
      - ./performance-tests/results:/results
    depends_on:
      backend-test:
        condition: service_healthy
    networks:
      - aigc-test-network
    command: run /scripts/load-test.js

  # 安全测试服务
  security-test:
    image: owasp/zap2docker-stable:latest
    container_name: aigc-security-test
    restart: "no"
    volumes:
      - ./security-tests:/zap/wrk
      - ./security-tests/results:/zap/results
    depends_on:
      backend-test:
        condition: service_healthy
    networks:
      - aigc-test-network
    command: zap-baseline.py -t http://backend-test:3000 -r security-report.html

volumes:
  postgres_test_data:
    driver: local
  redis_test_data:
    driver: local

networks:
  aigc-test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 测试环境使用说明:
# 1. 运行单元测试: docker-compose -f docker-compose.test.yml up backend-test frontend-test
# 2. 运行E2E测试: docker-compose -f docker-compose.test.yml up e2e-test
# 3. 运行性能测试: docker-compose -f docker-compose.test.yml up performance-test
# 4. 运行安全测试: docker-compose -f docker-compose.test.yml up security-test
# 5. 运行完整测试套件: docker-compose -f docker-compose.test.yml up
# 6. 查看测试结果: docker-compose -f docker-compose.test.yml logs
# 7. 清理测试环境: docker-compose -f docker-compose.test.yml down -v