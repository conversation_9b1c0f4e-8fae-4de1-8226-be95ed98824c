import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import config from '@/config';
import { initDatabase } from '@/database/connection';
import { logger, createRequestLogger, createErrorLogger } from '@/utils/logger';
import { errorHandler } from '@/utils/errors';

// 路由将在数据库连接后动态导入

class Application {
  private app: express.Application;
  private port: number;

  constructor() {
    this.app = express();
    this.port = config.app.port;
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddlewares(): void {
    // 安全中间件
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS配置
    this.app.use(cors({
      origin: config.app.corsOrigins,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // 速率限制
    this.app.use(rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests from this IP, please try again later.',
        },
      },
    }));

    // 请求解析中间件
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 请求日志中间件
    this.app.use(createRequestLogger());

    // 信任代理（用于获取真实IP）
    this.app.set('trust proxy', 1);
  }

  private initializeRoutes(): void {
    // 健康检查
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: config.app.env,
      });
    });

    // API根路径
    this.app.get('/', (req, res) => {
      res.json({
        message: 'AIGC Service Hub API',
        version: '1.0.0',
        documentation: '/api-docs',
        health: '/health',
      });
    });

    // 404处理将在API路由设置后添加
  }

  private async initializeApiRoutes(): Promise<void> {
    try {
      // 动态导入路由模块（在数据库连接后）
      logger.info('Loading route modules...');

      const authRoutes = (await import('./routes/auth.routes')).default;
      logger.info('Auth routes loaded');

      const userRoutes = (await import('./routes/user.routes')).default;
      logger.info('User routes loaded');

      const assetRoutes = (await import('./routes/asset.routes')).default;
      logger.info('Asset routes loaded');

      const transactionRoutes = (await import('./routes/transaction.routes')).default;
      logger.info('Transaction routes loaded');

      const financeRoutes = (await import('./routes/finance.routes')).default;
      logger.info('Finance routes loaded');

      const fileRoutes = (await import('./routes/file.routes')).default;
      logger.info('File routes loaded');

      const adminRoutes = (await import('./routes/admin.routes')).default;
      logger.info('Admin routes loaded');

    // API路由
    const apiRouter = express.Router();
    
    // 认证路由
    apiRouter.use('/auth', authRoutes);
    
    // 用户路由
    apiRouter.use('/users', userRoutes);
    
    // 资产路由
    apiRouter.use('/assets', assetRoutes);
    
    // 交易路由
    apiRouter.use('/transactions', transactionRoutes);
    
    // 财务路由
    apiRouter.use('/finance', financeRoutes);
    
    // 文件路由
    apiRouter.use('/files', fileRoutes);
    
    // 管理员路由
    apiRouter.use('/admin', adminRoutes);

    // 添加测试路由
    apiRouter.get('/test', (req, res) => {
      res.json({ message: 'API routes are working!', timestamp: new Date().toISOString() });
    });

    // 挂载API路由
    this.app.use('/api', apiRouter);
    logger.info('API router mounted to /api');

    // 404处理（在API路由设置后）
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'The requested resource was not found',
        },
      });
    });

      logger.info('API routes initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize API routes:', error);
      logger.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      throw error;
    }
  }

  private initializeErrorHandling(): void {
    // 错误日志中间件
    this.app.use(createErrorLogger());

    // 全局错误处理中间件
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      // 初始化数据库连接
      await initDatabase();
      logger.info('Database connected successfully');

      // 初始化API路由（在数据库连接后）
      logger.info('Starting API routes initialization...');
      await this.initializeApiRoutes();
      logger.info('API routes initialization completed');

      // 启动HTTP服务器
      this.app.listen(this.port, () => {
        logger.info(`Server running on port ${this.port}`);
        logger.info(`Environment: ${config.app.env}`);
        logger.info(`Health check: http://localhost:${this.port}/health`);
        logger.info(`API documentation: http://localhost:${this.port}/api-docs`);
      });

      // 优雅关闭处理
      this.setupGracefulShutdown();
    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  private setupGracefulShutdown(): void {
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}, shutting down gracefully...`);
      
      // 这里可以添加清理逻辑
      // 例如：关闭数据库连接，完成正在进行的请求等
      
      process.exit(0);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }
}

// 创建并启动应用
const app = new Application();

if (require.main === module) {
  app.start().catch((error) => {
    logger.error('Failed to start application:', error);
    process.exit(1);
  });
}

export default app;