import React, { useState } from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>graphy,
  Button,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Box,
  Container,
  Stack,
  Chip,
  useTheme,
  useMediaQuery,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  alpha,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Search,
  ShoppingCart,
  Notifications,
  AccountCircle,
  Login,
  PersonAdd,
  Home,
  Explore,
  Category,
  TrendingUp,
  Upload,
  Settings,
  Help,
  Logout,
  DarkMode,
  LightMode,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

interface ModernNavbarProps {
  onSearch?: (query: string) => void;
  cartItemCount?: number;
  notificationCount?: number;
  user?: {
    id: string;
    username: string;
    avatar?: string;
    isVerified?: boolean;
  } | null;
  onLogin?: () => void;
  onRegister?: () => void;
  onLogout?: () => void;
  darkMode?: boolean;
  onToggleDarkMode?: () => void;
}

const ModernNavbar: React.FC<ModernNavbarProps> = ({
  onSearch,
  cartItemCount = 0,
  notificationCount = 0,
  user = null,
  onLogin,
  onRegister,
  onLogout,
  darkMode = false,
  onToggleDarkMode,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [notificationMenuOpen, setNotificationMenuOpen] = useState<null | HTMLElement>(null);

  const navigationItems = [
    { label: '首页', icon: <Home />, path: '/' },
    { label: '探索', icon: <Explore />, path: '/explore' },
    { label: '分类', icon: <Category />, path: '/categories' },
    { label: '热门', icon: <TrendingUp />, path: '/trending' },
  ];

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationMenuOpen(event.currentTarget);
  };

  const handleNotificationMenuClose = () => {
    setNotificationMenuOpen(null);
  };

  const handleMobileMenuToggle = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    setMobileMenuOpen(false);
  };

  const renderDesktopNavigation = () => (
    <Stack direction="row" spacing={1} alignItems="center">
      {navigationItems.map((item) => (
        <Button
          key={item.path}
          onClick={() => handleNavigation(item.path)}
          startIcon={item.icon}
          sx={{
            color: 'text.primary',
            '&:hover': {
              bgcolor: alpha(theme.palette.primary.main, 0.08),
            },
          }}
        >
          {item.label}
        </Button>
      ))}
    </Stack>
  );

  const renderMobileDrawer = () => (
    <Drawer
      anchor="left"
      open={mobileMenuOpen}
      onClose={handleMobileMenuToggle}
      sx={{
        '& .MuiDrawer-paper': {
          width: 280,
          bgcolor: 'background.paper',
        },
      }}
    >
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" fontWeight="bold" color="primary">
          AIGC Service Hub
        </Typography>
      </Box>
      <Divider />
      <List>
        {navigationItems.map((item) => (
          <ListItem
            key={item.path}
            button
            onClick={() => handleNavigation(item.path)}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText primary={item.label} />
          </ListItem>
        ))}
      </List>
      <Divider />
      {user ? (
        <List>
          <ListItem button onClick={() => handleNavigation('/upload')}>
            <ListItemIcon><Upload /></ListItemIcon>
            <ListItemText primary="上传作品" />
          </ListItem>
          <ListItem button onClick={() => handleNavigation('/profile')}>
            <ListItemIcon><AccountCircle /></ListItemIcon>
            <ListItemText primary="个人中心" />
          </ListItem>
          <ListItem button onClick={() => handleNavigation('/settings')}>
            <ListItemIcon><Settings /></ListItemIcon>
            <ListItemText primary="设置" />
          </ListItem>
          <ListItem button onClick={onLogout}>
            <ListItemIcon><Logout /></ListItemIcon>
            <ListItemText primary="退出登录" />
          </ListItem>
        </List>
      ) : (
        <List>
          <ListItem button onClick={onLogin}>
            <ListItemIcon><Login /></ListItemIcon>
            <ListItemText primary="登录" />
          </ListItem>
          <ListItem button onClick={onRegister}>
            <ListItemIcon><PersonAdd /></ListItemIcon>
            <ListItemText primary="注册" />
          </ListItem>
        </List>
      )}
    </Drawer>
  );

  const renderUserMenu = () => (
    <Menu
      anchorEl={anchorEl}
      open={Boolean(anchorEl)}
      onClose={handleProfileMenuClose}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      PaperProps={{
        sx: {
          mt: 1,
          minWidth: 200,
        },
      }}
    >
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Stack direction="row" spacing={2} alignItems="center">
          <Avatar src={user?.avatar} sx={{ width: 40, height: 40 }}>
            {user?.username?.[0]?.toUpperCase()}
          </Avatar>
          <Box>
            <Typography variant="subtitle1" fontWeight="medium">
              {user?.username}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              创作者
            </Typography>
          </Box>
        </Stack>
      </Box>
      <MenuItem onClick={() => handleNavigation('/profile')}>
        <ListItemIcon><AccountCircle /></ListItemIcon>
        个人中心
      </MenuItem>
      <MenuItem onClick={() => handleNavigation('/upload')}>
        <ListItemIcon><Upload /></ListItemIcon>
        上传作品
      </MenuItem>
      <MenuItem onClick={() => handleNavigation('/settings')}>
        <ListItemIcon><Settings /></ListItemIcon>
        设置
      </MenuItem>
      <MenuItem onClick={onToggleDarkMode}>
        <ListItemIcon>
          {darkMode ? <LightMode /> : <DarkMode />}
        </ListItemIcon>
        {darkMode ? '浅色模式' : '深色模式'}
      </MenuItem>
      <Divider />
      <MenuItem onClick={() => handleNavigation('/help')}>
        <ListItemIcon><Help /></ListItemIcon>
        帮助中心
      </MenuItem>
      <MenuItem onClick={onLogout}>
        <ListItemIcon><Logout /></ListItemIcon>
        退出登录
      </MenuItem>
    </Menu>
  );

  const renderNotificationMenu = () => (
    <Menu
      anchorEl={notificationMenuOpen}
      open={Boolean(notificationMenuOpen)}
      onClose={handleNotificationMenuClose}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      PaperProps={{
        sx: {
          mt: 1,
          minWidth: 320,
          maxHeight: 400,
        },
      }}
    >
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" fontWeight="medium">
          通知
        </Typography>
      </Box>
      {/* 这里可以添加通知列表 */}
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          暂无新通知
        </Typography>
      </Box>
    </Menu>
  );

  return (
    <>
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          bgcolor: 'background.paper',
          borderBottom: 1,
          borderColor: 'divider',
          backdropFilter: 'blur(8px)',
        }}
      >
        <Container maxWidth="xl">
          <Toolbar sx={{ px: { xs: 0, sm: 2 } }}>
            {/* 移动端菜单按钮 */}
            {isMobile && (
              <IconButton
                edge="start"
                onClick={handleMobileMenuToggle}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
            )}

            {/* Logo */}
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontWeight: 'bold',
                color: 'primary.main',
                cursor: 'pointer',
                mr: 4,
              }}
              onClick={() => handleNavigation('/')}
            >
              AIGC Service Hub
            </Typography>

            {/* 桌面端导航 */}
            {!isMobile && renderDesktopNavigation()}

            <Box sx={{ flexGrow: 1 }} />

            {/* 右侧操作区 */}
            <Stack direction="row" spacing={1} alignItems="center">
              {/* 购物车 */}
              <IconButton
                onClick={() => handleNavigation('/cart')}
                sx={{
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.08),
                  },
                }}
              >
                <Badge badgeContent={cartItemCount} color="primary">
                  <ShoppingCart />
                </Badge>
              </IconButton>

              {user ? (
                <>
                  {/* 通知 */}
                  <IconButton
                    onClick={handleNotificationMenuOpen}
                    sx={{
                      '&:hover': {
                        bgcolor: alpha(theme.palette.primary.main, 0.08),
                      },
                    }}
                  >
                    <Badge badgeContent={notificationCount} color="error">
                      <Notifications />
                    </Badge>
                  </IconButton>

                  {/* 用户头像 */}
                  <IconButton onClick={handleProfileMenuOpen}>
                    <Avatar src={user.avatar} sx={{ width: 32, height: 32 }}>
                      {user.username?.[0]?.toUpperCase()}
                    </Avatar>
                  </IconButton>
                </>
              ) : (
                <>
                  {/* 登录/注册按钮 */}
                  <Button
                    variant="outlined"
                    onClick={onLogin}
                    size="small"
                    sx={{ display: { xs: 'none', sm: 'inline-flex' } }}
                  >
                    登录
                  </Button>
                  <Button
                    variant="contained"
                    onClick={onRegister}
                    size="small"
                    sx={{ display: { xs: 'none', sm: 'inline-flex' } }}
                  >
                    注册
                  </Button>
                </>
              )}
            </Stack>
          </Toolbar>
        </Container>
      </AppBar>

      {/* 移动端抽屉菜单 */}
      {renderMobileDrawer()}

      {/* 用户菜单 */}
      {user && renderUserMenu()}

      {/* 通知菜单 */}
      {user && renderNotificationMenu()}
    </>
  );
};

export default ModernNavbar;
