"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Permission = exports.ConfigType = exports.WithdrawalStatus = exports.LedgerStatus = exports.LedgerEntryType = exports.TransactionStatus = exports.Currency = exports.TagType = exports.AssetStatus = exports.AssetType = exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["PERSONAL_CREATOR"] = "PERSONAL_CREATOR";
    UserRole["ENTERPRISE_CREATOR"] = "ENTERPRISE_CREATOR";
    UserRole["ADMIN"] = "ADMIN";
})(UserRole || (exports.UserRole = UserRole = {}));
var AssetType;
(function (AssetType) {
    AssetType["MODEL"] = "MODEL";
    AssetType["LORA"] = "LORA";
    AssetType["WORKFLOW"] = "WORKFLOW";
    AssetType["PROMPT"] = "PROMPT";
    AssetType["TOOL"] = "TOOL";
})(AssetType || (exports.AssetType = AssetType = {}));
var AssetStatus;
(function (AssetStatus) {
    AssetStatus["DRAFT"] = "DRAFT";
    AssetStatus["PUBLISHED"] = "PUBLISHED";
    AssetStatus["ARCHIVED"] = "ARCHIVED";
})(AssetStatus || (exports.AssetStatus = AssetStatus = {}));
var TagType;
(function (TagType) {
    TagType["CATEGORY"] = "CATEGORY";
    TagType["STYLE"] = "STYLE";
})(TagType || (exports.TagType = TagType = {}));
var Currency;
(function (Currency) {
    Currency["USD"] = "USD";
    Currency["POINTS"] = "POINTS";
})(Currency || (exports.Currency = Currency = {}));
var TransactionStatus;
(function (TransactionStatus) {
    TransactionStatus["PENDING"] = "PENDING";
    TransactionStatus["COMPLETED"] = "COMPLETED";
    TransactionStatus["FAILED"] = "FAILED";
    TransactionStatus["REFUNDED"] = "REFUNDED";
})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));
var LedgerEntryType;
(function (LedgerEntryType) {
    LedgerEntryType["SALE_CREDIT"] = "SALE_CREDIT";
    LedgerEntryType["PLATFORM_FEE"] = "PLATFORM_FEE";
    LedgerEntryType["POINTS_PURCHASE"] = "POINTS_PURCHASE";
    LedgerEntryType["POINTS_DEBIT"] = "POINTS_DEBIT";
})(LedgerEntryType || (exports.LedgerEntryType = LedgerEntryType = {}));
var LedgerStatus;
(function (LedgerStatus) {
    LedgerStatus["PENDING"] = "PENDING";
    LedgerStatus["AVAILABLE"] = "AVAILABLE";
    LedgerStatus["WITHDRAWN"] = "WITHDRAWN";
    LedgerStatus["REFUNDED"] = "REFUNDED";
})(LedgerStatus || (exports.LedgerStatus = LedgerStatus = {}));
var WithdrawalStatus;
(function (WithdrawalStatus) {
    WithdrawalStatus["PENDING"] = "PENDING";
    WithdrawalStatus["APPROVED"] = "APPROVED";
    WithdrawalStatus["REJECTED"] = "REJECTED";
    WithdrawalStatus["COMPLETED"] = "COMPLETED";
})(WithdrawalStatus || (exports.WithdrawalStatus = WithdrawalStatus = {}));
var ConfigType;
(function (ConfigType) {
    ConfigType["STRING"] = "STRING";
    ConfigType["NUMBER"] = "NUMBER";
    ConfigType["BOOLEAN"] = "BOOLEAN";
    ConfigType["JSON"] = "JSON";
})(ConfigType || (exports.ConfigType = ConfigType = {}));
var Permission;
(function (Permission) {
    Permission["READ_PUBLIC"] = "read:public";
    Permission["READ_PRIVATE"] = "read:private";
    Permission["WRITE_ASSET"] = "write:asset";
    Permission["ADMIN_PANEL"] = "admin:panel";
    Permission["FINANCE_WITHDRAW"] = "finance:withdraw";
})(Permission || (exports.Permission = Permission = {}));
//# sourceMappingURL=index.js.map