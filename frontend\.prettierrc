{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "jsxSingleQuote": true, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "auto", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxBracketSameLine": false, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "overrides": [{"files": "*.json", "options": {"printWidth": 80, "tabWidth": 2}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always", "tabWidth": 2, "useTabs": false}}, {"files": "*.yaml", "options": {"tabWidth": 2, "useTabs": false}}, {"files": "*.yml", "options": {"tabWidth": 2, "useTabs": false}}]}