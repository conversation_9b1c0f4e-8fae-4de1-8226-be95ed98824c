# 部署指南

本指南详细说明了AIGC Service Hub MVP 1.0的部署过程，包括不同环境的配置和部署选项。

## 目录

- [系统要求](#系统要求)
- [部署选项](#部署选项)
- [环境配置](#环境配置)
- [Docker部署](#docker部署)
- [Kubernetes部署](#kubernetes部署)
- [生产环境部署](#生产环境部署)
- [监控配置](#监控配置)
- [故障排除](#故障排除)

## 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 1Gbps 网络连接

### 软件依赖
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.20+
- **Node.js**: 18+ (仅开发环境)

## 部署选项

### 1. 一键部署（推荐）

适合快速体验和开发环境：

```bash
# 克隆项目
git clone https://github.com/your-org/aigc-service-hub.git
cd aigc-service-hub

# 复制环境配置
cp .env.example .env

# 一键部署
./scripts/deploy.sh
```

### 2. 开发环境部署

适合开发者本地开发：

```bash
# 启动开发环境
./scripts/dev-start.sh

# 查看日志
./scripts/logs.sh --follow

# 停止环境
./scripts/dev-stop.sh
```

### 3. 生产环境部署

适合生产环境部署：

```bash
# 生产环境部署
./scripts/deploy.sh --env production --backup

# 健康检查
./scripts/health-check.sh
```

## 环境配置

### 环境变量配置

创建对应环境的配置文件：

```bash
# 开发环境
cp .env.example .env.development

# 测试环境
cp .env.example .env.test

# 生产环境
cp .env.example .env.production
```

### 核心配置项

#### 数据库配置
```env
# PostgreSQL配置
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=aigc_hub
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}
```

#### 应用配置
```env
# 后端配置
NODE_ENV=production
PORT=3000
API_PREFIX=/api/v1
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# 前端配置
VITE_API_URL=http://localhost:3000/api/v1
VITE_APP_NAME=AIGC Service Hub
VITE_APP_VERSION=1.0.0
```

#### 监控配置
```env
# 监控启用
MONITORING_ENABLED=true
METRICS_ENABLED=true
LOGGING_LEVEL=info

# Prometheus配置
PROMETHEUS_PORT=9090
PROMETHEUS_RETENTION_TIME=30d

# Grafana配置
GRAFANA_PORT=3001
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=secure_password
```

## Docker部署

### 单机部署

使用Docker Compose进行单机部署：

```bash
# 构建和启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 多环境部署

#### 开发环境
```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

#### 测试环境
```bash
docker-compose -f docker-compose.yml -f docker-compose.test.yml up -d
```

#### 生产环境
```bash
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### 监控部署

启动监控服务：

```bash
# 启动监控堆栈
docker-compose -f docker-compose.monitoring.yml up -d

# 访问监控面板
# Grafana: http://localhost:3001
# Prometheus: http://localhost:9090
# Alertmanager: http://localhost:9093
```

## Kubernetes部署

### 前置条件

- Kubernetes 1.20+
- kubectl 配置完成
- Helm 3.0+

### 部署步骤

1. **创建命名空间**
```bash
kubectl create namespace aigc-hub
```

2. **部署PostgreSQL**
```bash
helm repo add bitnami https://charts.bitnami.com/bitnami
helm install postgres bitnami/postgresql -n aigc-hub
```

3. **部署Redis**
```bash
helm install redis bitnami/redis -n aigc-hub
```

4. **部署应用**
```bash
kubectl apply -f k8s/ -n aigc-hub
```

5. **配置Ingress**
```bash
kubectl apply -f k8s/ingress.yml -n aigc-hub
```

### 监控部署

1. **部署Prometheus**
```bash
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install prometheus prometheus-community/kube-prometheus-stack -n aigc-hub
```

2. **配置Grafana**
```bash
kubectl apply -f k8s/monitoring/ -n aigc-hub
```

## 生产环境部署

### 部署前检查

1. **系统准备**
```bash
# 检查系统资源
./scripts/system-check.sh

# 检查Docker
docker --version
docker-compose --version

# 检查网络
ping google.com
```

2. **安全配置**
```bash
# 生成SSL证书
./scripts/generate-ssl.sh

# 配置防火墙
./scripts/setup-firewall.sh

# 创建应用用户
./scripts/create-app-user.sh
```

### 部署步骤

1. **环境准备**
```bash
# 设置环境变量
export ENVIRONMENT=production
export BACKUP_ENABLED=true
export MONITORING_ENABLED=true

# 复制生产配置
cp .env.production .env
```

2. **数据库初始化**
```bash
# 创建数据库
./scripts/setup-database.sh

# 运行迁移
./scripts/migrate.sh
```

3. **应用部署**
```bash
# 部署应用
./scripts/deploy.sh --env production --backup

# 等待服务就绪
./scripts/wait-for-services.sh
```

4. **健康检查**
```bash
# 运行健康检查
./scripts/health-check.sh --verbose

# 运行冒烟测试
./scripts/smoke-test.sh
```

### 生产环境优化

1. **性能优化**
```bash
# 优化数据库
./scripts/optimize-database.sh

# 优化应用配置
./scripts/optimize-app.sh

# 清理无用容器
docker system prune -f
```

2. **安全加固**
```bash
# 应用安全配置
./scripts/apply-security-config.sh

# 启用日志审计
./scripts/enable-audit-logging.sh
```

## 监控配置

### Prometheus配置

编辑`monitoring/prometheus/prometheus.yml`：

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'aigc-hub-backend'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: /metrics
    scrape_interval: 15s
```

### Grafana配置

1. **访问Grafana**
```
URL: http://localhost:3001
用户名: admin
密码: 环境变量中配置的密码
```

2. **导入仪表板**
```bash
# 导入预配置仪表板
curl -X POST \
  ************************************/api/dashboards/import \
  -H 'Content-Type: application/json' \
  -d @monitoring/grafana/dashboards/system.json
```

### 告警配置

编辑`monitoring/alertmanager/alertmanager.yml`：

```yaml
route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '<EMAIL>'
    from: '<EMAIL>'
    smarthost: 'localhost:587'
    subject: 'AIGC Hub Alert'
```

## 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 检查容器日志
docker-compose logs service_name

# 检查容器状态
docker-compose ps

# 重启服务
docker-compose restart service_name
```

#### 2. 数据库连接失败
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready

# 检查数据库日志
docker-compose logs postgres

# 测试连接
docker-compose exec backend npm run db:test
```

#### 3. 内存不足
```bash
# 检查内存使用
docker stats

# 清理无用容器
docker system prune -f

# 增加交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

#### 4. 端口冲突
```bash
# 检查端口占用
netstat -tlnp | grep :3000

# 修改端口配置
vim .env
# 修改 PORT=3001

# 重启服务
docker-compose restart
```

### 日志分析

#### 查看应用日志
```bash
# 查看所有日志
./scripts/logs.sh

# 查看特定服务日志
./scripts/logs.sh backend

# 实时跟踪日志
./scripts/logs.sh --follow backend

# 过滤错误日志
./scripts/logs.sh --grep "ERROR"
```

#### 分析性能问题
```bash
# 查看系统资源
docker stats

# 查看数据库性能
docker-compose exec postgres psql -c "SELECT * FROM pg_stat_activity;"

# 查看应用指标
curl http://localhost:3000/metrics
```

### 恢复操作

#### 从备份恢复
```bash
# 列出可用备份
./scripts/restore.sh --list

# 恢复最新备份
./scripts/restore.sh --latest

# 恢复特定备份
./scripts/restore.sh --backup backup_20240101_120000.tar.gz
```

#### 回滚部署
```bash
# 查看部署历史
./scripts/deploy.sh --history

# 回滚到上一版本
./scripts/deploy.sh --rollback

# 回滚到特定版本
./scripts/deploy.sh --rollback v1.0.0
```

### 性能调优

#### 数据库优化
```bash
# 运行数据库优化脚本
./scripts/optimize-database.sh

# 重建索引
docker-compose exec postgres psql -c "REINDEX DATABASE aigc_hub;"

# 更新统计信息
docker-compose exec postgres psql -c "ANALYZE;"
```

#### 应用优化
```bash
# 启用应用缓存
# 在.env中设置
REDIS_CACHE_ENABLED=true
CACHE_TTL=3600

# 优化Node.js内存
NODE_OPTIONS="--max-old-space-size=4096"

# 启用压缩
COMPRESSION_ENABLED=true
```

## 维护建议

### 定期维护

1. **每日检查**
```bash
# 检查服务状态
./scripts/health-check.sh

# 检查日志错误
./scripts/logs.sh --grep "ERROR" --since "24h"

# 检查磁盘空间
df -h
```

2. **每周维护**
```bash
# 清理旧日志
./scripts/cleanup-logs.sh

# 备份数据库
./scripts/backup.sh --type full

# 更新安全补丁
./scripts/update-security.sh
```

3. **每月维护**
```bash
# 系统更新
./scripts/system-update.sh

# 性能报告
./scripts/performance-report.sh

# 安全审计
./scripts/security-audit.sh
```

### 监控告警

建议配置以下告警规则：

- CPU使用率 > 80%
- 内存使用率 > 85%
- 磁盘使用率 > 90%
- 应用响应时间 > 1秒
- 错误率 > 5%
- 数据库连接数 > 80%

## 联系支持

如果在部署过程中遇到问题，请联系：

- **技术支持**: <EMAIL>
- **GitHub Issues**: https://github.com/your-org/aigc-service-hub/issues
- **文档**: https://docs.aigc-hub.com
- **社区论坛**: https://forum.aigc-hub.com

---

*最后更新: 2024年1月*