export interface FileData {
    originalName: string;
    fileName: string;
    filePath: string;
    fileSize: number;
    mimeType: string;
    uploadedBy: number;
    description?: string;
    tags?: string[];
}
export interface FileFilters {
    page: number;
    limit: number;
    search?: string;
    fileType?: string;
    userId?: number;
}
export interface FileRecord {
    id: number;
    originalName: string;
    fileName: string;
    filePath: string;
    fileSize: number;
    mimeType: string;
    uploadedBy: number;
    description?: string;
    tags?: string[];
    createdAt: Date;
    updatedAt: Date;
}
export declare class FileService {
    constructor();
    uploadFile(fileData: FileData): Promise<FileRecord>;
    uploadMultipleFiles(filesData: FileData[]): Promise<FileRecord[]>;
    getFiles(filters: FileFilters): Promise<{
        files: FileRecord[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }>;
    getFileById(fileId: number): Promise<FileRecord | null>;
    deleteFile(fileId: number): Promise<void>;
    updateFile(fileId: number, updates: Partial<FileData>): Promise<FileRecord>;
    getFilePath(fileId: number): Promise<string>;
    getThumbnailPath(fileId: number): string;
    private isImage;
    private generateThumbnail;
    private mapFileRecord;
}
//# sourceMappingURL=file.service.d.ts.map