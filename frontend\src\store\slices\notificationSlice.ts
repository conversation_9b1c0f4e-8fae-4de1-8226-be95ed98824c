import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 通知类型
type NotificationType = 'success' | 'error' | 'warning' | 'info';

// 通知位置
type NotificationPosition = 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';

// 通知接口
interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  duration?: number; // 持续时间（毫秒），0表示永不自动关闭
  position?: NotificationPosition;
  autoClose?: boolean;
  showCloseButton?: boolean;
  showIcon?: boolean;
  actions?: NotificationAction[];
  metadata?: Record<string, any>;
  createdAt: string;
  readAt?: string;
  dismissedAt?: string;
}

// 通知操作
interface NotificationAction {
  id: string;
  label: string;
  action: () => void;
  type?: 'primary' | 'secondary' | 'danger';
  loading?: boolean;
}

// 通知设置
interface NotificationSettings {
  enabled: boolean;
  position: NotificationPosition;
  duration: number;
  maxVisible: number;
  showIcons: boolean;
  showCloseButton: boolean;
  autoClose: boolean;
  sounds: {
    enabled: boolean;
    volume: number;
    successSound: string;
    errorSound: string;
    warningSound: string;
    infoSound: string;
  };
  desktop: {
    enabled: boolean;
    requirePermission: boolean;
  };
  email: {
    enabled: boolean;
    types: NotificationType[];
    frequency: 'immediate' | 'daily' | 'weekly';
  };
  push: {
    enabled: boolean;
    types: NotificationType[];
  };
}

// 通知历史
interface NotificationHistory {
  id: string;
  notification: Notification;
  action: 'shown' | 'clicked' | 'dismissed' | 'expired';
  timestamp: string;
}

// 通知统计
interface NotificationStats {
  total: number;
  unread: number;
  byType: Record<NotificationType, number>;
  byDay: Array<{
    date: string;
    count: number;
  }>;
}

// 通知状态接口
interface NotificationState {
  // 当前通知
  notifications: Notification[];
  
  // 通知历史
  history: NotificationHistory[];
  
  // 统计信息
  stats: NotificationStats;
  
  // 设置
  settings: NotificationSettings;
  
  // 权限状态
  permissions: {
    desktop: 'default' | 'granted' | 'denied';
    push: 'default' | 'granted' | 'denied';
  };
  
  // 显示状态
  isVisible: boolean;
  isPaused: boolean;
  
  // 过滤器
  filters: {
    type: NotificationType | 'all';
    read: boolean | 'all';
    dateRange: {
      start: string;
      end: string;
    };
  };
  
  // 搜索
  searchQuery: string;
  
  // 批量操作
  selectedNotifications: string[];
  
  // 加载状态
  isLoading: boolean;
  
  // 错误状态
  error: string | null;
}

// 默认设置
const defaultSettings: NotificationSettings = {
  enabled: true,
  position: 'top-right',
  duration: 5000,
  maxVisible: 5,
  showIcons: true,
  showCloseButton: true,
  autoClose: true,
  sounds: {
    enabled: true,
    volume: 0.5,
    successSound: '/sounds/success.mp3',
    errorSound: '/sounds/error.mp3',
    warningSound: '/sounds/warning.mp3',
    infoSound: '/sounds/info.mp3',
  },
  desktop: {
    enabled: false,
    requirePermission: true,
  },
  email: {
    enabled: false,
    types: ['error', 'warning'],
    frequency: 'immediate',
  },
  push: {
    enabled: false,
    types: ['error', 'warning'],
  },
};

// 从localStorage获取设置
const getSavedSettings = (): NotificationSettings => {
  try {
    const saved = localStorage.getItem('notification-settings');
    return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
  } catch (error) {
    console.error('Error loading notification settings:', error);
    return defaultSettings;
  }
};

// 从localStorage获取历史记录
const getSavedHistory = (): NotificationHistory[] => {
  try {
    const saved = localStorage.getItem('notification-history');
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error('Error loading notification history:', error);
    return [];
  }
};

// 计算统计信息
const calculateStats = (notifications: Notification[], history: NotificationHistory[]): NotificationStats => {
  const unread = notifications.filter(n => !n.readAt).length;
  const byType = notifications.reduce((acc, n) => {
    acc[n.type] = (acc[n.type] || 0) + 1;
    return acc;
  }, {} as Record<NotificationType, number>);
  
  // 按天统计
  const byDay = history.reduce((acc, h) => {
    const date = new Date(h.timestamp).toDateString();
    const existing = acc.find(d => d.date === date);
    if (existing) {
      existing.count++;
    } else {
      acc.push({ date, count: 1 });
    }
    return acc;
  }, [] as Array<{ date: string; count: number }>);
  
  return {
    total: notifications.length,
    unread,
    byType,
    byDay: byDay.slice(-30), // 最近30天
  };
};

// 初始状态
const initialState: NotificationState = {
  notifications: [],
  history: getSavedHistory(),
  stats: {
    total: 0,
    unread: 0,
    byType: { success: 0, error: 0, warning: 0, info: 0 },
    byDay: [],
  },
  settings: getSavedSettings(),
  permissions: {
    desktop: 'default',
    push: 'default',
  },
  isVisible: true,
  isPaused: false,
  filters: {
    type: 'all',
    read: 'all',
    dateRange: {
      start: '',
      end: '',
    },
  },
  searchQuery: '',
  selectedNotifications: [],
  isLoading: false,
  error: null,
};

// 生成通知ID
const generateNotificationId = (): string => {
  return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// 播放声音
const playSound = (type: NotificationType, settings: NotificationSettings) => {
  if (!settings.sounds.enabled) return;
  
  try {
    const audio = new Audio(settings.sounds[`${type}Sound`]);
    audio.volume = settings.sounds.volume;
    audio.play().catch(console.error);
  } catch (error) {
    console.error('Error playing notification sound:', error);
  }
};

// 显示桌面通知
const showDesktopNotification = (notification: Notification, settings: NotificationSettings) => {
  if (!settings.desktop.enabled || typeof window === 'undefined') return;
  
  if (Notification.permission === 'granted') {
    const desktopNotification = new Notification(notification.title, {
      body: notification.message,
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      tag: notification.id,
      requireInteraction: notification.duration === 0,
      silent: !settings.sounds.enabled,
    });
    
    desktopNotification.onclick = () => {
      window.focus();
      desktopNotification.close();
    };
    
    if (notification.duration && notification.duration > 0) {
      setTimeout(() => {
        desktopNotification.close();
      }, notification.duration);
    }
  }
};

// 创建slice
const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    // 添加通知
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'createdAt'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: generateNotificationId(),
        createdAt: new Date().toISOString(),
        position: action.payload.position || state.settings.position,
        duration: action.payload.duration ?? state.settings.duration,
        autoClose: action.payload.autoClose ?? state.settings.autoClose,
        showCloseButton: action.payload.showCloseButton ?? state.settings.showCloseButton,
        showIcon: action.payload.showIcon ?? state.settings.showIcons,
      };
      
      state.notifications.unshift(notification);
      
      // 限制最大显示数量
      if (state.notifications.length > state.settings.maxVisible) {
        state.notifications = state.notifications.slice(0, state.settings.maxVisible);
      }
      
      // 添加到历史记录
      const historyItem: NotificationHistory = {
        id: generateNotificationId(),
        notification,
        action: 'shown',
        timestamp: new Date().toISOString(),
      };
      
      state.history.unshift(historyItem);
      
      // 限制历史记录数量
      if (state.history.length > 1000) {
        state.history = state.history.slice(0, 1000);
      }
      
      // 更新统计信息
      state.stats = calculateStats(state.notifications, state.history);
      
      // 保存到localStorage
      localStorage.setItem('notification-history', JSON.stringify(state.history));
      
      // 播放声音
      playSound(notification.type, state.settings);
      
      // 显示桌面通知
      showDesktopNotification(notification, state.settings);
    },
    
    // 移除通知
    removeNotification: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      const notification = state.notifications.find(n => n.id === id);
      
      if (notification) {
        state.notifications = state.notifications.filter(n => n.id !== id);
        
        // 添加到历史记录
        const historyItem: NotificationHistory = {
          id: generateNotificationId(),
          notification,
          action: 'dismissed',
          timestamp: new Date().toISOString(),
        };
        
        state.history.unshift(historyItem);
        state.stats = calculateStats(state.notifications, state.history);
        
        localStorage.setItem('notification-history', JSON.stringify(state.history));
      }
    },
    
    // 标记为已读
    markAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification && !notification.readAt) {
        notification.readAt = new Date().toISOString();
        state.stats = calculateStats(state.notifications, state.history);
      }
    },
    
    // 标记所有为已读
    markAllAsRead: (state) => {
      const now = new Date().toISOString();
      state.notifications.forEach(notification => {
        if (!notification.readAt) {
          notification.readAt = now;
        }
      });
      state.stats = calculateStats(state.notifications, state.history);
    },
    
    // 清除所有通知
    clearAllNotifications: (state) => {
      const now = new Date().toISOString();
      
      // 添加到历史记录
      state.notifications.forEach(notification => {
        const historyItem: NotificationHistory = {
          id: generateNotificationId(),
          notification,
          action: 'dismissed',
          timestamp: now,
        };
        state.history.unshift(historyItem);
      });
      
      state.notifications = [];
      state.stats = calculateStats(state.notifications, state.history);
      
      localStorage.setItem('notification-history', JSON.stringify(state.history));
    },
    
    // 清除已读通知
    clearReadNotifications: (state) => {
      const now = new Date().toISOString();
      const readNotifications = state.notifications.filter(n => n.readAt);
      
      // 添加到历史记录
      readNotifications.forEach(notification => {
        const historyItem: NotificationHistory = {
          id: generateNotificationId(),
          notification,
          action: 'dismissed',
          timestamp: now,
        };
        state.history.unshift(historyItem);
      });
      
      state.notifications = state.notifications.filter(n => !n.readAt);
      state.stats = calculateStats(state.notifications, state.history);
      
      localStorage.setItem('notification-history', JSON.stringify(state.history));
    },
    
    // 更新通知设置
    updateSettings: (state, action: PayloadAction<Partial<NotificationSettings>>) => {
      state.settings = { ...state.settings, ...action.payload };
      localStorage.setItem('notification-settings', JSON.stringify(state.settings));
    },
    
    // 更新权限状态
    updatePermissions: (state, action: PayloadAction<Partial<NotificationState['permissions']>>) => {
      state.permissions = { ...state.permissions, ...action.payload };
    },
    
    // 设置可见性
    setVisible: (state, action: PayloadAction<boolean>) => {
      state.isVisible = action.payload;
    },
    
    // 暂停/恢复通知
    setPaused: (state, action: PayloadAction<boolean>) => {
      state.isPaused = action.payload;
    },
    
    // 设置过滤器
    setFilters: (state, action: PayloadAction<Partial<NotificationState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // 设置搜索查询
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    
    // 选择通知
    selectNotification: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      if (!state.selectedNotifications.includes(id)) {
        state.selectedNotifications.push(id);
      }
    },
    
    // 取消选择通知
    deselectNotification: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      state.selectedNotifications = state.selectedNotifications.filter(selectedId => selectedId !== id);
    },
    
    // 选择所有通知
    selectAllNotifications: (state) => {
      state.selectedNotifications = state.notifications.map(n => n.id);
    },
    
    // 取消选择所有通知
    deselectAllNotifications: (state) => {
      state.selectedNotifications = [];
    },
    
    // 批量删除选中的通知
    deleteSelectedNotifications: (state) => {
      const now = new Date().toISOString();
      
      // 添加到历史记录
      state.selectedNotifications.forEach(id => {
        const notification = state.notifications.find(n => n.id === id);
        if (notification) {
          const historyItem: NotificationHistory = {
            id: generateNotificationId(),
            notification,
            action: 'dismissed',
            timestamp: now,
          };
          state.history.unshift(historyItem);
        }
      });
      
      state.notifications = state.notifications.filter(n => !state.selectedNotifications.includes(n.id));
      state.selectedNotifications = [];
      state.stats = calculateStats(state.notifications, state.history);
      
      localStorage.setItem('notification-history', JSON.stringify(state.history));
    },
    
    // 批量标记为已读
    markSelectedAsRead: (state) => {
      const now = new Date().toISOString();
      state.selectedNotifications.forEach(id => {
        const notification = state.notifications.find(n => n.id === id);
        if (notification && !notification.readAt) {
          notification.readAt = now;
        }
      });
      state.selectedNotifications = [];
      state.stats = calculateStats(state.notifications, state.history);
    },
    
    // 清除历史记录
    clearHistory: (state) => {
      state.history = [];
      state.stats = calculateStats(state.notifications, state.history);
      localStorage.removeItem('notification-history');
    },
    
    // 设置加载状态
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
  },
});

// 导出actions
export const {
  addNotification,
  removeNotification,
  markAsRead,
  markAllAsRead,
  clearAllNotifications,
  clearReadNotifications,
  updateSettings,
  updatePermissions,
  setVisible,
  setPaused,
  setFilters,
  setSearchQuery,
  selectNotification,
  deselectNotification,
  selectAllNotifications,
  deselectAllNotifications,
  deleteSelectedNotifications,
  markSelectedAsRead,
  clearHistory,
  setLoading,
  setError,
  clearError,
} = notificationSlice.actions;

// 选择器
export const selectNotifications = (state: { notification: NotificationState }) => state.notification;
export const selectNotificationsList = (state: { notification: NotificationState }) => state.notification.notifications;
export const selectUnreadNotifications = (state: { notification: NotificationState }) => 
  state.notification.notifications.filter(n => !n.readAt);
export const selectNotificationHistory = (state: { notification: NotificationState }) => state.notification.history;
export const selectNotificationStats = (state: { notification: NotificationState }) => state.notification.stats;
export const selectNotificationSettings = (state: { notification: NotificationState }) => state.notification.settings;
export const selectNotificationPermissions = (state: { notification: NotificationState }) => state.notification.permissions;
export const selectNotificationFilters = (state: { notification: NotificationState }) => state.notification.filters;
export const selectNotificationSearchQuery = (state: { notification: NotificationState }) => state.notification.searchQuery;
export const selectSelectedNotifications = (state: { notification: NotificationState }) => state.notification.selectedNotifications;
export const selectIsNotificationVisible = (state: { notification: NotificationState }) => state.notification.isVisible;
export const selectIsNotificationPaused = (state: { notification: NotificationState }) => state.notification.isPaused;
export const selectNotificationError = (state: { notification: NotificationState }) => state.notification.error;

// 过滤后的通知列表
export const selectFilteredNotifications = (state: { notification: NotificationState }) => {
  const { notifications, filters, searchQuery } = state.notification;
  
  let filtered = notifications;
  
  // 按类型过滤
  if (filters.type !== 'all') {
    filtered = filtered.filter(n => n.type === filters.type);
  }
  
  // 按已读状态过滤
  if (filters.read !== 'all') {
    filtered = filtered.filter(n => filters.read ? !!n.readAt : !n.readAt);
  }
  
  // 按日期范围过滤
  if (filters.dateRange.start || filters.dateRange.end) {
    filtered = filtered.filter(n => {
      const date = new Date(n.createdAt);
      const start = filters.dateRange.start ? new Date(filters.dateRange.start) : null;
      const end = filters.dateRange.end ? new Date(filters.dateRange.end) : null;
      
      if (start && date < start) return false;
      if (end && date > end) return false;
      return true;
    });
  }
  
  // 按搜索查询过滤
  if (searchQuery) {
    const query = searchQuery.toLowerCase();
    filtered = filtered.filter(n =>
      n.title.toLowerCase().includes(query) ||
      n.message.toLowerCase().includes(query)
    );
  }
  
  return filtered;
};

// 导出reducer
export default notificationSlice.reducer;