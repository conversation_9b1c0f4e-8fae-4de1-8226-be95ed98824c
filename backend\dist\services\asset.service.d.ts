import { Asset, CreateAssetRequest, AssetQuery, AssetStats, Tag } from '../types';
export declare class AssetService {
    private db;
    constructor();
    createAsset(creatorId: number, assetData: CreateAssetRequest): Promise<Asset>;
    updateAsset(assetId: number, creatorId: number, updates: Partial<CreateAssetRequest>): Promise<Asset>;
    updateAssetFile(assetId: number, creatorId: number, s3FileKey: string, fileSize: number): Promise<void>;
    publishAsset(assetId: number, creatorId: number): Promise<Asset>;
    getAssetById(assetId: number, includePrivate?: boolean): Promise<Asset & {
        tags?: Tag[];
        creator?: any;
    }>;
    getAssetList(query: AssetQuery): Promise<{
        assets: {
            id: any;
            creatorId: any;
            title: any;
            description: any;
            assetType: any;
            priceUsd: any;
            pricePoints: any;
            coverImageUrl: any;
            downloadCount: any;
            createdAt: any;
            publishedAt: any;
            creatorName: any;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getCreatorAssets(creatorId: number, query: AssetQuery): Promise<{
        assets: {
            id: any;
            creatorId: any;
            title: any;
            description: any;
            assetType: any;
            priceUsd: any;
            pricePoints: any;
            coverImageUrl: any;
            status: any;
            downloadCount: any;
            fileSize: any;
            createdAt: any;
            publishedAt: any;
            updatedAt: any;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getAssetStats(assetId: number): Promise<AssetStats>;
    incrementDownloadCount(assetId: number): Promise<void>;
    deleteAsset(assetId: number, creatorId: number): Promise<void>;
    getAllTags(): Promise<Tag[]>;
    private createOrGetTags;
    private linkAssetTags;
    private updateAssetTags;
}
export default AssetService;
//# sourceMappingURL=asset.service.d.ts.map