import { Request, Response, NextFunction } from 'express';
import { AdminService } from '@/services/admin.service';
import { 
  ValidationError, 
  NotFoundError, 
  AuthorizationError 
} from '@/utils/errors';
import { logger } from '@/utils/logger';
import { User } from '@/types';

export class AdminController {
  private adminService: AdminService;

  constructor() {
    this.adminService = new AdminService();
  }

  // 用户管理
  getUsers = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { page = 1, limit = 20, search, role, status } = req.query;
      
      const filters = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        search: search as string,
        role: role as string,
        status: status as string,
      };

      const result = await this.adminService.getUsers(filters);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  getUserById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { userId } = req.params;
      const user = await this.adminService.getUserById(parseInt(userId));

      if (!user) {
        throw new NotFoundError('User not found');
      }

      res.json({
        success: true,
        data: user,
      });
    } catch (error) {
      next(error);
    }
  };

  updateUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { userId } = req.params;
      const updateData = req.body;

      const user = await this.adminService.updateUser(parseInt(userId), updateData);

      res.json({
        success: true,
        data: user,
        message: 'User updated successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  deleteUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { userId } = req.params;
      await this.adminService.deleteUser(parseInt(userId));

      res.json({
        success: true,
        message: 'User deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  activateUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { userId } = req.params;
      await this.adminService.activateUser(parseInt(userId));

      res.json({
        success: true,
        message: 'User activated successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  deactivateUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { userId } = req.params;
      await this.adminService.deactivateUser(parseInt(userId));

      res.json({
        success: true,
        message: 'User deactivated successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  // 资产管理
  getAssets = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { page = 1, limit = 20, search, category, status } = req.query;
      
      const filters = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        search: search as string,
        category: category as string,
        status: status as string,
      };

      const result = await this.adminService.getAssets(filters);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  getAssetById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { assetId } = req.params;
      const asset = await this.adminService.getAssetById(parseInt(assetId));

      if (!asset) {
        throw new NotFoundError('Asset not found');
      }

      res.json({
        success: true,
        data: asset,
      });
    } catch (error) {
      next(error);
    }
  };

  updateAsset = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { assetId } = req.params;
      const updateData = req.body;

      const asset = await this.adminService.updateAsset(parseInt(assetId), updateData);

      res.json({
        success: true,
        data: asset,
        message: 'Asset updated successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  deleteAsset = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { assetId } = req.params;
      await this.adminService.deleteAsset(parseInt(assetId));

      res.json({
        success: true,
        message: 'Asset deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  approveAsset = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { assetId } = req.params;
      await this.adminService.approveAsset(parseInt(assetId));

      res.json({
        success: true,
        message: 'Asset approved successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  rejectAsset = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { assetId } = req.params;
      const { reason } = req.body;
      await this.adminService.rejectAsset(parseInt(assetId), reason);

      res.json({
        success: true,
        message: 'Asset rejected successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  // 交易管理
  getTransactions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { page = 1, limit = 20, search, status, type } = req.query;
      
      const filters = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        search: search as string,
        status: status as string,
        type: type as string,
      };

      const result = await this.adminService.getTransactions(filters);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  getTransactionById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { transactionId } = req.params;
      const transaction = await this.adminService.getTransactionById(parseInt(transactionId));

      if (!transaction) {
        throw new NotFoundError('Transaction not found');
      }

      res.json({
        success: true,
        data: transaction,
      });
    } catch (error) {
      next(error);
    }
  };

  refundTransaction = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { transactionId } = req.params;
      const { reason } = req.body;
      
      await this.adminService.refundTransaction(parseInt(transactionId), reason);

      res.json({
        success: true,
        message: 'Transaction refunded successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  // 财务管理
  getWithdrawals = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { page = 1, limit = 20, status } = req.query;
      
      const filters = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        status: status as string,
      };

      const result = await this.adminService.getWithdrawals(filters);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  getWithdrawalById = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { withdrawalId } = req.params;
      const withdrawal = await this.adminService.getWithdrawalById(parseInt(withdrawalId));

      if (!withdrawal) {
        throw new NotFoundError('Withdrawal not found');
      }

      res.json({
        success: true,
        data: withdrawal,
      });
    } catch (error) {
      next(error);
    }
  };

  approveWithdrawal = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { withdrawalId } = req.params;
      await this.adminService.approveWithdrawal(parseInt(withdrawalId));

      res.json({
        success: true,
        message: 'Withdrawal approved successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  rejectWithdrawal = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { withdrawalId } = req.params;
      const { reason } = req.body;
      await this.adminService.rejectWithdrawal(parseInt(withdrawalId), reason);

      res.json({
        success: true,
        message: 'Withdrawal rejected successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  // 系统统计
  getOverviewStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const stats = await this.adminService.getOverviewStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      next(error);
    }
  };

  getUserStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const stats = await this.adminService.getUserStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      next(error);
    }
  };

  getAssetStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const stats = await this.adminService.getAssetStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      next(error);
    }
  };

  getTransactionStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const stats = await this.adminService.getTransactionStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      next(error);
    }
  };

  getRevenueStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const stats = await this.adminService.getRevenueStats();

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      next(error);
    }
  };

  // 系统设置
  getSettings = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const settings = await this.adminService.getSettings();

      res.json({
        success: true,
        data: settings,
      });
    } catch (error) {
      next(error);
    }
  };

  updateSettings = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const settings = await this.adminService.updateSettings(req.body);

      res.json({
        success: true,
        data: settings,
        message: 'Settings updated successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  // 日志查看
  getSecurityLogs = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { page = 1, limit = 50 } = req.query;
      
      const filters = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
      };

      const result = await this.adminService.getSecurityLogs(filters);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  getErrorLogs = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { page = 1, limit = 50 } = req.query;
      
      const filters = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
      };

      const result = await this.adminService.getErrorLogs(filters);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  getAuditLogs = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { page = 1, limit = 50 } = req.query;
      
      const filters = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
      };

      const result = await this.adminService.getAuditLogs(filters);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  // 系统监控
  getSystemHealth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const health = await this.adminService.getSystemHealth();

      res.json({
        success: true,
        data: health,
      });
    } catch (error) {
      next(error);
    }
  };

  getSystemMetrics = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const metrics = await this.adminService.getSystemMetrics();

      res.json({
        success: true,
        data: metrics,
      });
    } catch (error) {
      next(error);
    }
  };

  // 内容管理
  getReports = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { page = 1, limit = 20, status } = req.query;
      
      const filters = {
        page: parseInt(page as string),
        limit: parseInt(limit as string),
        status: status as string,
      };

      const result = await this.adminService.getReports(filters);

      res.json({
        success: true,
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  resolveReport = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { reportId } = req.params;
      const { action, reason } = req.body;
      
      await this.adminService.resolveReport(parseInt(reportId), action, reason);

      res.json({
        success: true,
        message: 'Report resolved successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  // 批量操作
  bulkActivateUsers = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { userIds } = req.body;
      await this.adminService.bulkActivateUsers(userIds);

      res.json({
        success: true,
        message: 'Users activated successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  bulkDeactivateUsers = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { userIds } = req.body;
      await this.adminService.bulkDeactivateUsers(userIds);

      res.json({
        success: true,
        message: 'Users deactivated successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  bulkApproveAssets = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { assetIds } = req.body;
      await this.adminService.bulkApproveAssets(assetIds);

      res.json({
        success: true,
        message: 'Assets approved successfully',
      });
    } catch (error) {
      next(error);
    }
  };

  bulkRejectAssets = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { assetIds, reason } = req.body;
      await this.adminService.bulkRejectAssets(assetIds, reason);

      res.json({
        success: true,
        message: 'Assets rejected successfully',
      });
    } catch (error) {
      next(error);
    }
  };
}