"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.logThirdPartyService = exports.logCacheOperation = exports.logFileOperation = exports.logFinancialOperation = exports.logPerformance = exports.logSecurityEvent = exports.logBusinessOperation = exports.logDatabaseOperation = exports.createErrorLogger = exports.createRequestLogger = exports.logDebug = exports.logInfo = exports.logWarn = exports.logError = void 0;
const winston_1 = __importDefault(require("winston"));
const config_1 = __importDefault(require("../config"));
const logger = winston_1.default.createLogger({
    level: config_1.default.logging.level,
    format: winston_1.default.format.combine(winston_1.default.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
    }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json()),
    defaultMeta: { service: 'aigc-service-hub' },
    transports: [
        new winston_1.default.transports.File({
            filename: 'logs/error.log',
            level: 'error',
            maxsize: 5242880,
            maxFiles: 5,
            format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.json())
        }),
        new winston_1.default.transports.File({
            filename: config_1.default.logging.file,
            maxsize: 5242880,
            maxFiles: config_1.default.logging.maxFiles,
            format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.json())
        }),
    ],
});
exports.logger = logger;
if (config_1.default.logging.enableConsole) {
    logger.add(new winston_1.default.transports.Console({
        format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple(), winston_1.default.format.printf(({ level, message, timestamp, ...meta }) => {
            return `${timestamp} [${level}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`;
        }))
    }));
}
const logError = (message, error, meta) => {
    logger.error(message, { error: error?.stack || error, ...meta });
};
exports.logError = logError;
const logWarn = (message, meta) => {
    logger.warn(message, meta);
};
exports.logWarn = logWarn;
const logInfo = (message, meta) => {
    logger.info(message, meta);
};
exports.logInfo = logInfo;
const logDebug = (message, meta) => {
    logger.debug(message, meta);
};
exports.logDebug = logDebug;
const createRequestLogger = () => {
    return (req, res, next) => {
        const start = Date.now();
        res.on('finish', () => {
            const duration = Date.now() - start;
            const logData = {
                method: req.method,
                url: req.url,
                statusCode: res.statusCode,
                duration: `${duration}ms`,
                userAgent: req.get('User-Agent'),
                ip: req.ip,
                userId: req.user?.id || 'anonymous',
            };
            if (res.statusCode >= 400) {
                logger.warn('HTTP Request', logData);
            }
            else {
                logger.info('HTTP Request', logData);
            }
        });
        next();
    };
};
exports.createRequestLogger = createRequestLogger;
const createErrorLogger = () => {
    return (error, req, res, next) => {
        const logData = {
            error: error.stack || error,
            method: req.method,
            url: req.url,
            statusCode: error.statusCode || 500,
            userAgent: req.get('User-Agent'),
            ip: req.ip,
            userId: req.user?.id || 'anonymous',
            body: req.body,
            query: req.query,
            params: req.params,
        };
        logger.error('HTTP Error', logData);
        next(error);
    };
};
exports.createErrorLogger = createErrorLogger;
const logDatabaseOperation = (operation, table, meta) => {
    logger.debug('Database Operation', {
        operation,
        table,
        ...meta,
    });
};
exports.logDatabaseOperation = logDatabaseOperation;
const logBusinessOperation = (operation, userId, meta) => {
    logger.info('Business Operation', {
        operation,
        userId,
        ...meta,
    });
};
exports.logBusinessOperation = logBusinessOperation;
const logSecurityEvent = (event, userId, meta) => {
    logger.warn('Security Event', {
        event,
        userId,
        timestamp: new Date().toISOString(),
        ...meta,
    });
};
exports.logSecurityEvent = logSecurityEvent;
const logPerformance = (operation, duration, meta) => {
    const level = duration > 5000 ? 'warn' : duration > 1000 ? 'info' : 'debug';
    logger.log(level, 'Performance', {
        operation,
        duration: `${duration}ms`,
        ...meta,
    });
};
exports.logPerformance = logPerformance;
const logFinancialOperation = (operation, amount, userId, meta) => {
    logger.info('Financial Operation', {
        operation,
        amount,
        userId,
        timestamp: new Date().toISOString(),
        ...meta,
    });
};
exports.logFinancialOperation = logFinancialOperation;
const logFileOperation = (operation, fileKey, userId, meta) => {
    logger.info('File Operation', {
        operation,
        fileKey,
        userId,
        timestamp: new Date().toISOString(),
        ...meta,
    });
};
exports.logFileOperation = logFileOperation;
const logCacheOperation = (operation, key, hit, meta) => {
    logger.debug('Cache Operation', {
        operation,
        key,
        hit,
        ...meta,
    });
};
exports.logCacheOperation = logCacheOperation;
const logThirdPartyService = (service, operation, success, meta) => {
    const level = success ? 'info' : 'error';
    logger.log(level, 'Third Party Service', {
        service,
        operation,
        success,
        timestamp: new Date().toISOString(),
        ...meta,
    });
};
exports.logThirdPartyService = logThirdPartyService;
exports.default = logger;
//# sourceMappingURL=logger.js.map