version: '3.8'

services:
  # PostgreSQL 数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: aigc-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-aigc_service_hub}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      POSTGRES_INITDB_ARGS: --encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/init.sql:/docker-entrypoint-initdb.d/02-init.sql:ro
      - ./database/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./database/backups:/backups
    command: >
      postgres 
      -c config_file=/etc/postgresql/postgresql.conf
      -c max_connections=100
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-aigc_service_hub}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: aigc-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./database/redis.conf:/etc/redis/redis.conf:ro
    command: redis-server /etc/redis/redis.conf
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: aigc-backend
    restart: unless-stopped
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: ${BACKEND_PORT:-3000}
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-postgres123}@postgres:5432/${POSTGRES_DB:-aigc_service_hub}
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: ${AWS_REGION:-us-west-2}
      S3_PRIVATE_BUCKET: ${S3_PRIVATE_BUCKET}
      S3_PUBLIC_BUCKET: ${S3_PUBLIC_BUCKET}
      PAYPAL_CLIENT_ID: ${PAYPAL_CLIENT_ID}
      PAYPAL_CLIENT_SECRET: ${PAYPAL_CLIENT_SECRET}
      PAYPAL_SANDBOX: ${PAYPAL_SANDBOX:-true}
      FROM_EMAIL: ${FROM_EMAIL}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost}
      BACKEND_URL: ${BACKEND_URL:-http://localhost:3000}
    ports:
      - "${BACKEND_PORT:-3000}:3000"
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端应用服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
      args:
        REACT_APP_API_URL: ${REACT_APP_API_URL:-http://localhost:3000/api/v1}
        REACT_APP_WS_URL: ${REACT_APP_WS_URL:-ws://localhost:3000}
        REACT_APP_PAYPAL_CLIENT_ID: ${PAYPAL_CLIENT_ID}
        REACT_APP_GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
        REACT_APP_GITHUB_CLIENT_ID: ${GITHUB_CLIENT_ID}
        REACT_APP_AWS_REGION: ${AWS_REGION:-us-west-2}
        REACT_APP_S3_PUBLIC_BUCKET: ${S3_PUBLIC_BUCKET}
    container_name: aigc-frontend
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT:-3001}:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: aigc-nginx
    restart: unless-stopped
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
      - ./frontend/build:/usr/share/nginx/html:ro
    depends_on:
      - backend
      - frontend
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  aigc-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 使用说明:
# 1. 复制 .env.example 到 .env 并配置环境变量
# 2. 启动所有服务: docker-compose up -d
# 3. 查看日志: docker-compose logs -f
# 4. 停止服务: docker-compose down
# 5. 重启服务: docker-compose restart
# 6. 查看状态: docker-compose ps