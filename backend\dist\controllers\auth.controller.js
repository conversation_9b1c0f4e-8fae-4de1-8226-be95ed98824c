"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const auth_service_1 = require("../services/auth.service");
const errors_1 = require("../utils/errors");
const errors_2 = require("../utils/errors");
const logger_1 = require("../utils/logger");
class AuthController {
    constructor() {
        this.register = (0, errors_2.asyncHandler)(async (req, res) => {
            const registerData = req.body;
            try {
                const result = await this.authService.register(registerData);
                res.status(201).json({
                    success: true,
                    data: result,
                    message: 'User registered successfully',
                });
                (0, logger_1.logBusinessOperation)('USER_REGISTER_SUCCESS', result.user.id, {
                    email: result.user.email,
                    userRole: result.user.userRole,
                });
            }
            catch (error) {
                logger_1.logger.error('Registration failed:', error);
                if (error instanceof errors_1.ConflictError) {
                    res.status(409).json({
                        success: false,
                        error: {
                            code: 'EMAIL_ALREADY_EXISTS',
                            message: 'An account with this email already exists',
                        },
                    });
                }
                else {
                    throw error;
                }
            }
        });
        this.login = (0, errors_2.asyncHandler)(async (req, res) => {
            const loginData = req.body;
            try {
                const result = await this.authService.login(loginData);
                res.json({
                    success: true,
                    data: result,
                    message: 'Login successful',
                });
                (0, logger_1.logBusinessOperation)('USER_LOGIN_SUCCESS', result.user.id, {
                    email: result.user.email,
                    ip: req.ip,
                    userAgent: req.get('User-Agent'),
                });
            }
            catch (error) {
                if (error instanceof errors_1.InvalidCredentialsError) {
                    (0, logger_1.logSecurityEvent)('LOGIN_FAILED', undefined, {
                        email: loginData.email,
                        ip: req.ip,
                        userAgent: req.get('User-Agent'),
                    });
                    res.status(401).json({
                        success: false,
                        error: {
                            code: 'INVALID_CREDENTIALS',
                            message: 'Invalid email or password',
                        },
                    });
                }
                else {
                    logger_1.logger.error('Login failed:', error);
                    throw error;
                }
            }
        });
        this.refreshToken = (0, errors_2.asyncHandler)(async (req, res) => {
            const { refreshToken } = req.body;
            try {
                const result = await this.authService.refreshToken(refreshToken);
                res.json({
                    success: true,
                    data: result,
                    message: 'Token refreshed successfully',
                });
            }
            catch (error) {
                logger_1.logger.error('Token refresh failed:', error);
                res.status(401).json({
                    success: false,
                    error: {
                        code: 'INVALID_REFRESH_TOKEN',
                        message: 'Invalid or expired refresh token',
                    },
                });
            }
        });
        this.getProfile = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            res.json({
                success: true,
                data: {
                    user: req.user,
                },
                message: 'User profile retrieved successfully',
            });
        });
        this.changePassword = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const { currentPassword, newPassword } = req.body;
            if (!currentPassword || !newPassword) {
                throw new errors_1.ValidationError('Current password and new password are required');
            }
            try {
                await this.authService.changePassword(req.user.id, currentPassword, newPassword);
                res.json({
                    success: true,
                    message: 'Password changed successfully',
                });
                (0, logger_1.logBusinessOperation)('PASSWORD_CHANGE_SUCCESS', req.user.id, {
                    ip: req.ip,
                    userAgent: req.get('User-Agent'),
                });
            }
            catch (error) {
                if (error instanceof errors_1.InvalidCredentialsError) {
                    (0, logger_1.logSecurityEvent)('PASSWORD_CHANGE_FAILED', req.user.id, {
                        reason: 'Invalid current password',
                        ip: req.ip,
                    });
                    res.status(400).json({
                        success: false,
                        error: {
                            code: 'INVALID_CURRENT_PASSWORD',
                            message: 'Current password is incorrect',
                        },
                    });
                }
                else {
                    logger_1.logger.error('Password change failed:', error);
                    throw error;
                }
            }
        });
        this.requestPasswordReset = (0, errors_2.asyncHandler)(async (req, res) => {
            const { email } = req.body;
            if (!email) {
                throw new errors_1.ValidationError('Email is required');
            }
            try {
                await this.authService.requestPasswordReset(email);
                res.json({
                    success: true,
                    message: 'If an account with this email exists, a password reset link has been sent',
                });
                (0, logger_1.logBusinessOperation)('PASSWORD_RESET_REQUESTED', 0, {
                    email,
                    ip: req.ip,
                });
            }
            catch (error) {
                logger_1.logger.error('Password reset request failed:', error);
                res.json({
                    success: true,
                    message: 'If an account with this email exists, a password reset link has been sent',
                });
            }
        });
        this.resetPassword = (0, errors_2.asyncHandler)(async (req, res) => {
            const { resetToken, newPassword } = req.body;
            if (!resetToken || !newPassword) {
                throw new errors_1.ValidationError('Reset token and new password are required');
            }
            try {
                await this.authService.resetPassword(resetToken, newPassword);
                res.json({
                    success: true,
                    message: 'Password reset successfully',
                });
                (0, logger_1.logBusinessOperation)('PASSWORD_RESET_SUCCESS', 0, {
                    ip: req.ip,
                    userAgent: req.get('User-Agent'),
                });
            }
            catch (error) {
                logger_1.logger.error('Password reset failed:', error);
                res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_RESET_TOKEN',
                        message: 'Invalid or expired reset token',
                    },
                });
            }
        });
        this.logout = (0, errors_2.asyncHandler)(async (req, res) => {
            if (req.user) {
                (0, logger_1.logBusinessOperation)('USER_LOGOUT', req.user.id, {
                    ip: req.ip,
                    userAgent: req.get('User-Agent'),
                });
            }
            res.json({
                success: true,
                message: 'Logout successful',
            });
        });
        this.googleCallback = (0, errors_2.asyncHandler)(async (req, res) => {
            res.status(501).json({
                success: false,
                error: {
                    code: 'NOT_IMPLEMENTED',
                    message: 'Google OAuth not implemented yet',
                },
            });
        });
        this.githubCallback = (0, errors_2.asyncHandler)(async (req, res) => {
            res.status(501).json({
                success: false,
                error: {
                    code: 'NOT_IMPLEMENTED',
                    message: 'GitHub OAuth not implemented yet',
                },
            });
        });
        this.verifyEmail = (0, errors_2.asyncHandler)(async (req, res) => {
            const { token } = req.query;
            if (!token) {
                throw new errors_1.ValidationError('Verification token is required');
            }
            try {
                res.status(501).json({
                    success: false,
                    error: {
                        code: 'NOT_IMPLEMENTED',
                        message: 'Email verification not implemented yet',
                    },
                });
            }
            catch (error) {
                logger_1.logger.error('Email verification failed:', error);
                throw error;
            }
        });
        this.resendVerification = (0, errors_2.asyncHandler)(async (req, res) => {
            const { email } = req.body;
            if (!email) {
                throw new errors_1.ValidationError('Email is required');
            }
            try {
                res.status(501).json({
                    success: false,
                    error: {
                        code: 'NOT_IMPLEMENTED',
                        message: 'Email verification not implemented yet',
                    },
                });
            }
            catch (error) {
                logger_1.logger.error('Resend verification failed:', error);
                throw error;
            }
        });
        this.checkEmailAvailability = (0, errors_2.asyncHandler)(async (req, res) => {
            const { email } = req.query;
            if (!email) {
                throw new errors_1.ValidationError('Email is required');
            }
            try {
                res.json({
                    success: true,
                    data: {
                        available: true,
                    },
                    message: 'Email availability checked',
                });
            }
            catch (error) {
                logger_1.logger.error('Email availability check failed:', error);
                throw error;
            }
        });
        this.getUserPermissions = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const permissions = {
                canCreateAssets: req.user.userRole !== 'ADMIN',
                canManageAssets: req.user.userRole !== 'ADMIN',
                canWithdrawFunds: req.user.userRole !== 'ADMIN',
                canAccessAdminPanel: req.user.userRole === 'ADMIN',
            };
            res.json({
                success: true,
                data: {
                    permissions,
                },
                message: 'User permissions retrieved successfully',
            });
        });
        this.authService = new auth_service_1.AuthService();
    }
}
exports.AuthController = AuthController;
exports.default = AuthController;
//# sourceMappingURL=auth.controller.js.map