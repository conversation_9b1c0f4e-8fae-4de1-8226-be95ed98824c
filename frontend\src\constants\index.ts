// API配置
export const API_CONFIG = {
  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:3000',
  VERSION: process.env.REACT_APP_API_VERSION || 'v1',
  TIMEOUT: 10000,
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000,
} as const;

// 文件上传配置
export const FILE_UPLOAD_CONFIG = {
  MAX_FILE_SIZE: Number(process.env.REACT_APP_MAX_FILE_SIZE) || 32212254720, // 30GB
  SUPPORTED_FILE_TYPES: (process.env.REACT_APP_SUPPORTED_FILE_TYPES || '.zip,.rar,.7z,.tar,.gz,.model,.lora,.workflow,.prompt,.tool').split(','),
  CHUNK_SIZE: 5 * 1024 * 1024, // 5MB chunks
  MAX_CONCURRENT_UPLOADS: 3,
  UPLOAD_TIMEOUT: 30000, // 30 seconds per chunk
} as const;

// PayPal配置
export const PAYPAL_CONFIG = {
  CLIENT_ID: process.env.REACT_APP_PAYPAL_CLIENT_ID || '',
  ENVIRONMENT: process.env.REACT_APP_PAYPAL_ENVIRONMENT || 'sandbox',
  CURRENCY: 'USD',
  INTENT: 'capture',
} as const;

// OAuth配置
export const OAUTH_CONFIG = {
  GOOGLE_CLIENT_ID: process.env.REACT_APP_GOOGLE_CLIENT_ID || '',
  GITHUB_CLIENT_ID: process.env.REACT_APP_GITHUB_CLIENT_ID || '',
  REDIRECT_URI: `${window.location.origin}/auth/callback`,
} as const;

// 路由路径
export const ROUTES = {
  HOME: '/',
  LOGIN: '/auth/login',
  REGISTER: '/auth/register',
  OAUTH_CALLBACK: '/auth/callback',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  
  // 用户相关
  PROFILE: '/profile',
  DASHBOARD: '/dashboard',
  PURCHASES: '/dashboard/purchases',
  SETTINGS: '/dashboard/settings',
  
  // 创作者相关
  CREATOR_DASHBOARD: '/creator',
  CREATOR_ASSETS: '/creator/assets',
  CREATOR_UPLOAD: '/creator/upload',
  CREATOR_ANALYTICS: '/creator/analytics',
  CREATOR_EARNINGS: '/creator/earnings',
  CREATOR_WITHDRAW: '/creator/withdraw',
  
  // 资源相关
  ASSET_DETAIL: '/asset/:id',
  ASSET_SEARCH: '/search',
  ASSET_CATEGORY: '/category/:category',
  ASSET_STYLE: '/style/:style',
  ASSET_TYPE: '/type/:type',
  
  // 交易相关
  PURCHASE: '/purchase/:id',
  TRANSACTION_SUCCESS: '/transaction/success',
  TRANSACTION_FAILED: '/transaction/failed',
  
  // 管理员相关
  ADMIN: '/admin',
  ADMIN_USERS: '/admin/users',
  ADMIN_ASSETS: '/admin/assets',
  ADMIN_TRANSACTIONS: '/admin/transactions',
  ADMIN_FINANCE: '/admin/finance',
  ADMIN_SETTINGS: '/admin/settings',
  
  // 其他
  ABOUT: '/about',
  TERMS: '/terms',
  PRIVACY: '/privacy',
  CONTACT: '/contact',
  HELP: '/help',
  FAQ: '/faq',
} as const;

// 资源类型映射
export const ASSET_TYPE_MAP = {
  MODEL: {
    label: '微调模型',
    labelEn: 'Fine-tuned Model',
    icon: 'model',
    color: '#1976d2',
  },
  LORA: {
    label: 'LoRA',
    labelEn: 'LoRA',
    icon: 'lora',
    color: '#dc004e',
  },
  WORKFLOW: {
    label: '工作流',
    labelEn: 'Workflow',
    icon: 'workflow',
    color: '#2e7d32',
  },
  PROMPT: {
    label: '提示词',
    labelEn: 'Prompt',
    icon: 'prompt',
    color: '#ed6c02',
  },
  TOOL: {
    label: '工具',
    labelEn: 'Tool',
    icon: 'tool',
    color: '#0288d1',
  },
} as const;

// 用户角色映射
export const USER_ROLE_MAP = {
  PERSONAL_CREATOR: {
    label: '个人创作者',
    labelEn: 'Personal Creator',
    color: '#1976d2',
  },
  ENTERPRISE_CREATOR: {
    label: '企业创作者',
    labelEn: 'Enterprise Creator',
    color: '#dc004e',
  },
  ADMIN: {
    label: '管理员',
    labelEn: 'Administrator',
    color: '#2e7d32',
  },
} as const;

// 交易状态映射
export const TRANSACTION_STATUS_MAP = {
  PENDING: {
    label: '等待中',
    labelEn: 'Pending',
    color: '#ed6c02',
  },
  COMPLETED: {
    label: '已完成',
    labelEn: 'Completed',
    color: '#2e7d32',
  },
  FAILED: {
    label: '失败',
    labelEn: 'Failed',
    color: '#d32f2f',
  },
  REFUNDED: {
    label: '已退款',
    labelEn: 'Refunded',
    color: '#9e9e9e',
  },
} as const;

// 提现状态映射
export const WITHDRAWAL_STATUS_MAP = {
  PENDING: {
    label: '等待审核',
    labelEn: 'Pending Review',
    color: '#ed6c02',
  },
  APPROVED: {
    label: '已批准',
    labelEn: 'Approved',
    color: '#2e7d32',
  },
  REJECTED: {
    label: '已拒绝',
    labelEn: 'Rejected',
    color: '#d32f2f',
  },
  COMPLETED: {
    label: '已完成',
    labelEn: 'Completed',
    color: '#1976d2',
  },
} as const;

// 默认分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: Number(process.env.REACT_APP_PAGINATION_SIZE) || 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100,
} as const;

// 瀑布流配置
export const WATERFALL_CONFIG = {
  COLUMNS: Number(process.env.REACT_APP_WATERFALL_COLUMNS) || 4,
  GAP: 16,
  MIN_COLUMN_WIDTH: 280,
  LOAD_MORE_THRESHOLD: 200,
} as const;

// 缓存配置
export const CACHE_CONFIG = {
  DEFAULT_DURATION: Number(process.env.REACT_APP_CACHE_DURATION) || 300000, // 5分钟
  ASSETS_DURATION: Number(process.env.REACT_APP_ASSETS_CACHE_DURATION) || 86400000, // 24小时
  USER_DURATION: 600000, // 10分钟
  TAGS_DURATION: 3600000, // 1小时
} as const;

// 国际化配置
export const I18N_CONFIG = {
  DEFAULT_LANGUAGE: 'en',
  SUPPORTED_LANGUAGES: ['en', 'zh'],
  FALLBACK_LANGUAGE: 'en',
  NAMESPACE: 'common',
} as const;

// 主题配置
export const THEME_CONFIG = {
  DEFAULT_MODE: 'light',
  DEFAULT_LOCALE: 'en',
  STORAGE_KEY: 'theme-settings',
} as const;

// 布局配置（基于PRD第7章）
export const LAYOUT_CONFIG = {
  HEADER_HEIGHT: 100, // H1.1
  NAVIGATION_HEIGHT: 30, // H1.2
  CATEGORY_BAR_HEIGHT: 26, // H1.3
  STYLE_BAR_HEIGHT: 20, // H1.4
  CAROUSEL_HEIGHT: 350, // H2.1
  HERO_BOARD_HEIGHT: 350, // H2.2
  FEATURED_HEIGHT: 220, // H3
  FOOTER_HEIGHT: 260, // H5
  CAROUSEL_WIDTH_RATIO: 0.8, // 4/5
  HERO_BOARD_WIDTH_RATIO: 0.2, // 1/5
} as const;

// 默认标签
export const DEFAULT_TAGS = {
  CATEGORIES: [
    { name: '视频', nameEn: 'Video' },
    { name: '音频', nameEn: 'Audio' },
    { name: '图片', nameEn: 'Image' },
    { name: '文本', nameEn: 'Text' },
  ],
  STYLES: [
    { name: '电商', nameEn: 'E-commerce' },
    { name: '动漫', nameEn: 'Anime' },
    { name: '建筑', nameEn: 'Architecture' },
    { name: '摄影', nameEn: 'Photography' },
    { name: '网页', nameEn: 'Web' },
    { name: '写真', nameEn: 'Portrait' },
    { name: '节日', nameEn: 'Holiday' },
    { name: '国画', nameEn: 'Chinese Painting' },
    { name: '园林', nameEn: 'Garden' },
    { name: '卡通', nameEn: 'Cartoon' },
    { name: '人像', nameEn: 'Human' },
    { name: '老照片', nameEn: 'Vintage' },
    { name: '美女', nameEn: 'Beauty' },
    { name: '男人', nameEn: 'Male' },
    { name: '女人', nameEn: 'Female' },
    { name: '素材', nameEn: 'Material' },
  ],
} as const;

// 验证规则
export const VALIDATION_RULES = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_MAX_LENGTH: 128,
  PASSWORD_REGEX: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  DISPLAY_NAME_MIN_LENGTH: 2,
  DISPLAY_NAME_MAX_LENGTH: 50,
  TITLE_MIN_LENGTH: 5,
  TITLE_MAX_LENGTH: 100,
  DESCRIPTION_MAX_LENGTH: 2000,
  PAYPAL_EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  AMOUNT_MIN: 0.01,
  AMOUNT_MAX: 99999.99,
} as const;

// 错误代码映射
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  UNSUPPORTED_FILE_TYPE: 'UNSUPPORTED_FILE_TYPE',
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  DOWNLOAD_FAILED: 'DOWNLOAD_FAILED',
} as const;

// 通知类型
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
} as const;

// 本地存储键
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  THEME_SETTINGS: 'theme_settings',
  LANGUAGE: 'language',
  SEARCH_HISTORY: 'search_history',
  RECENT_ASSETS: 'recent_assets',
  UPLOAD_PROGRESS: 'upload_progress',
} as const;

// 应用元数据
export const APP_META = {
  NAME: process.env.REACT_APP_NAME || 'AIGC Service Hub',
  VERSION: process.env.REACT_APP_VERSION || '1.0.0',
  DESCRIPTION: process.env.REACT_APP_DESCRIPTION || 'AI创作者服务平台',
  KEYWORDS: process.env.REACT_APP_KEYWORDS || 'AIGC,AI,创作者,交易平台,数字资产',
  AUTHOR: 'AIGC Service Hub Team',
  SITE_URL: process.env.REACT_APP_SITE_URL || 'https://aigcservicehub.com',
} as const;

// 社交媒体配置
export const SOCIAL_CONFIG = {
  FACEBOOK_APP_ID: process.env.REACT_APP_FACEBOOK_APP_ID || '',
  TWITTER_HANDLE: process.env.REACT_APP_TWITTER_HANDLE || '@aigcservicehub',
  LINKEDIN_COMPANY_ID: process.env.REACT_APP_LINKEDIN_COMPANY_ID || '',
} as const;

// 监控配置
export const MONITORING_CONFIG = {
  SENTRY_DSN: process.env.REACT_APP_SENTRY_DSN || '',
  SENTRY_ENVIRONMENT: process.env.REACT_APP_SENTRY_ENVIRONMENT || 'development',
  GOOGLE_ANALYTICS_ID: process.env.REACT_APP_GOOGLE_ANALYTICS_ID || '',
  HOTJAR_ID: process.env.REACT_APP_HOTJAR_ID || '',
} as const;

// 功能开关
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: process.env.REACT_APP_ENABLE_ANALYTICS === 'true',
  ENABLE_SENTRY: process.env.REACT_APP_ENABLE_SENTRY === 'true',
  ENABLE_PWA: process.env.REACT_APP_ENABLE_PWA === 'true',
  ENABLE_DARK_MODE: true,
  ENABLE_MULTI_LANGUAGE: true,
  ENABLE_OAUTH: true,
  ENABLE_PAYPAL: true,
} as const;

// 调试配置
export const DEBUG_CONFIG = {
  ENABLED: process.env.REACT_APP_DEBUG === 'true',
  LOG_LEVEL: process.env.REACT_APP_LOG_LEVEL || 'info',
  SHOW_REDUX_DEVTOOLS: process.env.NODE_ENV === 'development',
} as const;

// 默认值
export const DEFAULT_VALUES = {
  AVATAR_URL: '/assets/default-avatar.png',
  ASSET_COVER_URL: '/assets/default-asset-cover.png',
  LOADING_TIMEOUT: 30000,
  DEBOUNCE_DELAY: 300,
  THROTTLE_DELAY: 100,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// 数学常量
export const MATH_CONSTANTS = {
  BYTES_PER_KB: 1024,
  BYTES_PER_MB: 1024 * 1024,
  BYTES_PER_GB: 1024 * 1024 * 1024,
  MILLISECONDS_PER_SECOND: 1000,
  SECONDS_PER_MINUTE: 60,
  MINUTES_PER_HOUR: 60,
  HOURS_PER_DAY: 24,
  DAYS_PER_WEEK: 7,
  WEEKS_PER_MONTH: 4,
  MONTHS_PER_YEAR: 12,
} as const;

// 正则表达式
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  URL: /^https?:\/\/[^\s/$.?#].[^\s]*$/,
  CHINESE: /[\u4e00-\u9fa5]/,
  ENGLISH: /^[A-Za-z\s]+$/,
  NUMBER: /^\d+$/,
  DECIMAL: /^\d+(\.\d+)?$/,
  ALPHANUMERIC: /^[A-Za-z0-9]+$/,
  SLUG: /^[a-z0-9\-_]+$/,
} as const;

export default {
  API_CONFIG,
  FILE_UPLOAD_CONFIG,
  PAYPAL_CONFIG,
  OAUTH_CONFIG,
  ROUTES,
  ASSET_TYPE_MAP,
  USER_ROLE_MAP,
  TRANSACTION_STATUS_MAP,
  WITHDRAWAL_STATUS_MAP,
  PAGINATION_CONFIG,
  WATERFALL_CONFIG,
  CACHE_CONFIG,
  I18N_CONFIG,
  THEME_CONFIG,
  LAYOUT_CONFIG,
  DEFAULT_TAGS,
  VALIDATION_RULES,
  ERROR_CODES,
  NOTIFICATION_TYPES,
  STORAGE_KEYS,
  APP_META,
  SOCIAL_CONFIG,
  MONITORING_CONFIG,
  FEATURE_FLAGS,
  DEBUG_CONFIG,
  DEFAULT_VALUES,
  MATH_CONSTANTS,
  REGEX_PATTERNS,
};