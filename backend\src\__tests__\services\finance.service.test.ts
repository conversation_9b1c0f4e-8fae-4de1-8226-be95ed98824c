import { FinanceService } from '../../services/finance.service';

describe('FinanceService', () => {
  let financeService: FinanceService;

  beforeEach(() => {
    financeService = new FinanceService();
  });

  describe('calculateCommission', () => {
    describe('Individual Creator Commission Tests', () => {
      test('should calculate 95% creator share for first sale (individual)', () => {
        const transaction = {
          asset: {
            creator: { type: 'INDIVIDUAL' },
            id: 'asset-1'
          },
          amount: 100
        };
        const salesCount = 0; // First sale

        const result = financeService.calculateCommission(transaction, salesCount);

        expect(result.creatorSharePercent).toBe(95);
        expect(result.platformSharePercent).toBe(5);
        expect(result.creatorAmount).toBe(95);
        expect(result.platformAmount).toBe(5);
      });

      test('should calculate 90% creator share for second sale (individual)', () => {
        const transaction = {
          asset: {
            creator: { type: 'INDIVIDUAL' },
            id: 'asset-1'
          },
          amount: 100
        };
        const salesCount = 1; // Second sale

        const result = financeService.calculateCommission(transaction, salesCount);

        expect(result.creatorSharePercent).toBe(90);
        expect(result.platformSharePercent).toBe(10);
        expect(result.creatorAmount).toBe(90);
        expect(result.platformAmount).toBe(10);
      });

      test('should calculate 85% creator share for third sale (individual)', () => {
        const transaction = {
          asset: {
            creator: { type: 'INDIVIDUAL' },
            id: 'asset-1'
          },
          amount: 100
        };
        const salesCount = 2; // Third sale

        const result = financeService.calculateCommission(transaction, salesCount);

        expect(result.creatorSharePercent).toBe(85);
        expect(result.platformSharePercent).toBe(15);
        expect(result.creatorAmount).toBe(85);
        expect(result.platformAmount).toBe(15);
      });

      test('should cap at 50% creator share for 10+ sales (individual)', () => {
        const transaction = {
          asset: {
            creator: { type: 'INDIVIDUAL' },
            id: 'asset-1'
          },
          amount: 100
        };
        const salesCount = 9; // 10th sale

        const result = financeService.calculateCommission(transaction, salesCount);

        expect(result.creatorSharePercent).toBe(50);
        expect(result.platformSharePercent).toBe(50);
        expect(result.creatorAmount).toBe(50);
        expect(result.platformAmount).toBe(50);
      });

      test('should maintain 50% creator share for sales beyond 10 (individual)', () => {
        const transaction = {
          asset: {
            creator: { type: 'INDIVIDUAL' },
            id: 'asset-1'
          },
          amount: 100
        };
        const salesCount = 15; // 16th sale

        const result = financeService.calculateCommission(transaction, salesCount);

        expect(result.creatorSharePercent).toBe(50);
        expect(result.platformSharePercent).toBe(50);
        expect(result.creatorAmount).toBe(50);
        expect(result.platformAmount).toBe(50);
      });
    });

    describe('Enterprise Creator Commission Tests', () => {
      test('should calculate 92% creator share for first sale (enterprise)', () => {
        const transaction = {
          asset: {
            creator: { type: 'ENTERPRISE' },
            id: 'asset-1'
          },
          amount: 100
        };
        const salesCount = 0; // First sale

        const result = financeService.calculateCommission(transaction, salesCount);

        expect(result.creatorSharePercent).toBe(92);
        expect(result.platformSharePercent).toBe(8);
        expect(result.creatorAmount).toBe(92);
        expect(result.platformAmount).toBe(8);
      });

      test('should calculate 84% creator share for second sale (enterprise)', () => {
        const transaction = {
          asset: {
            creator: { type: 'ENTERPRISE' },
            id: 'asset-1'
          },
          amount: 100
        };
        const salesCount = 1; // Second sale

        const result = financeService.calculateCommission(transaction, salesCount);

        expect(result.creatorSharePercent).toBe(84);
        expect(result.platformSharePercent).toBe(16);
        expect(result.creatorAmount).toBe(84);
        expect(result.platformAmount).toBe(16);
      });

      test('should cap at 44% creator share for 7+ sales (enterprise)', () => {
        const transaction = {
          asset: {
            creator: { type: 'ENTERPRISE' },
            id: 'asset-1'
          },
          amount: 100
        };
        const salesCount = 6; // 7th sale

        const result = financeService.calculateCommission(transaction, salesCount);

        expect(result.creatorSharePercent).toBe(44);
        expect(result.platformSharePercent).toBe(56);
        expect(result.creatorAmount).toBe(44);
        expect(result.platformAmount).toBe(56);
      });

      test('should maintain 44% creator share for sales beyond 7 (enterprise)', () => {
        const transaction = {
          asset: {
            creator: { type: 'ENTERPRISE' },
            id: 'asset-1'
          },
          amount: 100
        };
        const salesCount = 10; // 11th sale

        const result = financeService.calculateCommission(transaction, salesCount);

        expect(result.creatorSharePercent).toBe(44);
        expect(result.platformSharePercent).toBe(56);
        expect(result.creatorAmount).toBe(44);
        expect(result.platformAmount).toBe(56);
      });
    });

    describe('Edge Cases and Validation', () => {
      test('should handle decimal amounts correctly', () => {
        const transaction = {
          asset: {
            creator: { type: 'INDIVIDUAL' },
            id: 'asset-1'
          },
          amount: 99.99
        };
        const salesCount = 0;

        const result = financeService.calculateCommission(transaction, salesCount);

        expect(result.creatorAmount).toBeCloseTo(94.99, 2);
        expect(result.platformAmount).toBeCloseTo(5.00, 2);
      });

      test('should throw error for invalid creator type', () => {
        const transaction = {
          asset: {
            creator: { type: 'INVALID' },
            id: 'asset-1'
          },
          amount: 100
        };
        const salesCount = 0;

        expect(() => {
          financeService.calculateCommission(transaction, salesCount);
        }).toThrow('Invalid creator type');
      });

      test('should handle zero amount', () => {
        const transaction = {
          asset: {
            creator: { type: 'INDIVIDUAL' },
            id: 'asset-1'
          },
          amount: 0
        };
        const salesCount = 0;

        const result = financeService.calculateCommission(transaction, salesCount);

        expect(result.creatorAmount).toBe(0);
        expect(result.platformAmount).toBe(0);
      });
    });
  });

  describe('calculateWithdrawalEligibility', () => {
    test('should return eligible funds after 7 days', () => {
      const ledgerEntries = [
        {
          id: '1',
          amount: 100,
          status: 'PENDING',
          created_at: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000) // 8 days ago
        },
        {
          id: '2',
          amount: 50,
          status: 'PENDING',
          created_at: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000) // 6 days ago
        }
      ];

      const result = financeService.calculateWithdrawalEligibility(ledgerEntries);

      expect(result.eligibleAmount).toBe(100);
      expect(result.pendingAmount).toBe(50);
      expect(result.eligibleEntries).toHaveLength(1);
    });
  });
});
