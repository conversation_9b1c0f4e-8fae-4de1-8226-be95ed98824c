export declare class FinanceController {
    private financeService;
    constructor();
    createWithdrawalRequest: (req: any, res: any, next: any) => void;
    getWithdrawalRequests: (req: any, res: any, next: any) => void;
    getWithdrawalById: (req: any, res: any, next: any) => void;
    getEarnings: (req: any, res: any, next: any) => void;
    getBalance: (req: any, res: any, next: any) => void;
    getAllWithdrawals: (req: any, res: any, next: any) => void;
    approveWithdrawal: (req: any, res: any, next: any) => void;
    rejectWithdrawal: (req: any, res: any, next: any) => void;
    getPlatformStats: (req: any, res: any, next: any) => void;
}
export default FinanceController;
//# sourceMappingURL=finance.controller.d.ts.map