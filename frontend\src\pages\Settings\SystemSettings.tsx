import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Divider,
  Tab,
  Tabs,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  useTheme,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Payment as PaymentIcon,
  Language as LanguageIcon,
  Palette as PaletteIcon,
  Email as EmailIcon,
  Sms as SmsIcon,
  CloudUpload as CloudUploadIcon,
  Save as SaveIcon,
  Restore as RestoreIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div role="tabpanel" hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
};

const SystemSettings: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [selectedTab, setSelectedTab] = useState(0);
  const [showApiKey, setShowApiKey] = useState(false);
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);

  // 系统设置状态
  const [settings, setSettings] = useState({
    general: {
      siteName: 'AIGC Service Hub',
      siteDescription: '专业的AI生成内容服务平台',
      supportEmail: '<EMAIL>',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      theme: 'light',
      enableRegistration: true,
      enableGuestAccess: false,
      maintenanceMode: false,
    },
    security: {
      requireEmailVerification: true,
      enableTwoFactor: true,
      sessionTimeout: 30,
      maxLoginAttempts: 5,
      passwordMinLength: 8,
      requireStrongPassword: true,
      enableCaptcha: true,
      apiRateLimit: 100,
    },
    notifications: {
      emailNotifications: true,
      smsNotifications: false,
      pushNotifications: true,
      notifyNewUser: true,
      notifyNewAsset: true,
      notifyNewTransaction: true,
      notifySystemUpdate: false,
    },
    payment: {
      currency: 'CNY',
      commissionRate: 10,
      minimumWithdrawal: 100,
      paymentMethods: ['alipay', 'wechat', 'stripe'],
      autoPayoutEnabled: true,
      payoutSchedule: 'weekly',
    },
    storage: {
      maxFileSize: 100,
      allowedFileTypes: ['jpg', 'png', 'gif', 'pdf', 'mp3', 'mp4', 'zip'],
      storageLimit: 10000,
      enableCdn: true,
      cdnUrl: 'https://cdn.aigcservicehub.com',
    },
    api: {
      apiKey: 'sk-1234567890abcdef1234567890abcdef',
      enableApiAccess: true,
      apiRateLimit: 1000,
      enableWebhooks: true,
      webhookUrl: 'https://webhook.aigcservicehub.com',
    },
  });

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value,
      },
    }));
  };

  const handleSave = () => {
    // 保存设置逻辑
    setSaveDialogOpen(true);
  };

  const handleRestore = () => {
    // 恢复默认设置逻辑
    console.log('恢复默认设置');
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
        系统设置
      </Typography>

      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={selectedTab} onChange={(e, v) => setSelectedTab(v)}>
            <Tab label="基本设置" icon={<SettingsIcon />} />
            <Tab label="安全设置" icon={<SecurityIcon />} />
            <Tab label="通知设置" icon={<NotificationsIcon />} />
            <Tab label="支付设置" icon={<PaymentIcon />} />
            <Tab label="存储设置" icon={<CloudUploadIcon />} />
            <Tab label="API设置" icon={<EditIcon />} />
          </Tabs>
        </Box>

        <TabPanel value={selectedTab} index={0}>
          {/* 基本设置 */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="站点名称"
                value={settings.general.siteName}
                onChange={(e) => handleSettingChange('general', 'siteName', e.target.value)}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="站点描述"
                multiline
                rows={3}
                value={settings.general.siteDescription}
                onChange={(e) => handleSettingChange('general', 'siteDescription', e.target.value)}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="支持邮箱"
                type="email"
                value={settings.general.supportEmail}
                onChange={(e) => handleSettingChange('general', 'supportEmail', e.target.value)}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>语言</InputLabel>
                <Select
                  value={settings.general.language}
                  onChange={(e) => handleSettingChange('general', 'language', e.target.value)}
                  label="语言"
                >
                  <MenuItem value="zh-CN">简体中文</MenuItem>
                  <MenuItem value="en-US">English</MenuItem>
                  <MenuItem value="ja-JP">日本語</MenuItem>
                </Select>
              </FormControl>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>时区</InputLabel>
                <Select
                  value={settings.general.timezone}
                  onChange={(e) => handleSettingChange('general', 'timezone', e.target.value)}
                  label="时区"
                >
                  <MenuItem value="Asia/Shanghai">Asia/Shanghai</MenuItem>
                  <MenuItem value="Asia/Tokyo">Asia/Tokyo</MenuItem>
                  <MenuItem value="America/New_York">America/New_York</MenuItem>
                  <MenuItem value="Europe/London">Europe/London</MenuItem>
                </Select>
              </FormControl>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>主题</InputLabel>
                <Select
                  value={settings.general.theme}
                  onChange={(e) => handleSettingChange('general', 'theme', e.target.value)}
                  label="主题"
                >
                  <MenuItem value="light">浅色</MenuItem>
                  <MenuItem value="dark">深色</MenuItem>
                  <MenuItem value="auto">自动</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                访问控制
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.general.enableRegistration}
                    onChange={(e) => handleSettingChange('general', 'enableRegistration', e.target.checked)}
                  />
                }
                label="允许用户注册"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.general.enableGuestAccess}
                    onChange={(e) => handleSettingChange('general', 'enableGuestAccess', e.target.checked)}
                  />
                }
                label="允许访客访问"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.general.maintenanceMode}
                    onChange={(e) => handleSettingChange('general', 'maintenanceMode', e.target.checked)}
                  />
                }
                label="维护模式"
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={selectedTab} index={1}>
          {/* 安全设置 */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                账户安全
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.security.requireEmailVerification}
                    onChange={(e) => handleSettingChange('security', 'requireEmailVerification', e.target.checked)}
                  />
                }
                label="要求邮箱验证"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.security.enableTwoFactor}
                    onChange={(e) => handleSettingChange('security', 'enableTwoFactor', e.target.checked)}
                  />
                }
                label="启用双因素认证"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.security.enableCaptcha}
                    onChange={(e) => handleSettingChange('security', 'enableCaptcha', e.target.checked)}
                  />
                }
                label="启用验证码"
              />
              <TextField
                fullWidth
                label="会话超时时间（分钟）"
                type="number"
                value={settings.security.sessionTimeout}
                onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
                sx={{ mt: 2 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                密码策略
              </Typography>
              <TextField
                fullWidth
                label="最小密码长度"
                type="number"
                value={settings.security.passwordMinLength}
                onChange={(e) => handleSettingChange('security', 'passwordMinLength', parseInt(e.target.value))}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="最大登录尝试次数"
                type="number"
                value={settings.security.maxLoginAttempts}
                onChange={(e) => handleSettingChange('security', 'maxLoginAttempts', parseInt(e.target.value))}
                sx={{ mb: 2 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.security.requireStrongPassword}
                    onChange={(e) => handleSettingChange('security', 'requireStrongPassword', e.target.checked)}
                  />
                }
                label="要求强密码"
              />
              <TextField
                fullWidth
                label="API速率限制（请求/分钟）"
                type="number"
                value={settings.security.apiRateLimit}
                onChange={(e) => handleSettingChange('security', 'apiRateLimit', parseInt(e.target.value))}
                sx={{ mt: 2 }}
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={selectedTab} index={2}>
          {/* 通知设置 */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                通知渠道
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications.emailNotifications}
                    onChange={(e) => handleSettingChange('notifications', 'emailNotifications', e.target.checked)}
                  />
                }
                label="邮件通知"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications.smsNotifications}
                    onChange={(e) => handleSettingChange('notifications', 'smsNotifications', e.target.checked)}
                  />
                }
                label="短信通知"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications.pushNotifications}
                    onChange={(e) => handleSettingChange('notifications', 'pushNotifications', e.target.checked)}
                  />
                }
                label="推送通知"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                通知类型
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications.notifyNewUser}
                    onChange={(e) => handleSettingChange('notifications', 'notifyNewUser', e.target.checked)}
                  />
                }
                label="新用户注册"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications.notifyNewAsset}
                    onChange={(e) => handleSettingChange('notifications', 'notifyNewAsset', e.target.checked)}
                  />
                }
                label="新资产上传"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications.notifyNewTransaction}
                    onChange={(e) => handleSettingChange('notifications', 'notifyNewTransaction', e.target.checked)}
                  />
                }
                label="新交易"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.notifications.notifySystemUpdate}
                    onChange={(e) => handleSettingChange('notifications', 'notifySystemUpdate', e.target.checked)}
                  />
                }
                label="系统更新"
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={selectedTab} index={3}>
          {/* 支付设置 */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>默认货币</InputLabel>
                <Select
                  value={settings.payment.currency}
                  onChange={(e) => handleSettingChange('payment', 'currency', e.target.value)}
                  label="默认货币"
                >
                  <MenuItem value="CNY">人民币 (CNY)</MenuItem>
                  <MenuItem value="USD">美元 (USD)</MenuItem>
                  <MenuItem value="EUR">欧元 (EUR)</MenuItem>
                </Select>
              </FormControl>
              <TextField
                fullWidth
                label="平台佣金率 (%)"
                type="number"
                value={settings.payment.commissionRate}
                onChange={(e) => handleSettingChange('payment', 'commissionRate', parseFloat(e.target.value))}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="最低提现金额"
                type="number"
                value={settings.payment.minimumWithdrawal}
                onChange={(e) => handleSettingChange('payment', 'minimumWithdrawal', parseFloat(e.target.value))}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                支付方式
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.payment.paymentMethods.includes('alipay')}
                    onChange={(e) => {
                      const methods = [...settings.payment.paymentMethods];
                      if (e.target.checked) {
                        methods.push('alipay');
                      } else {
                        methods.splice(methods.indexOf('alipay'), 1);
                      }
                      handleSettingChange('payment', 'paymentMethods', methods);
                    }}
                  />
                }
                label="支付宝"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.payment.paymentMethods.includes('wechat')}
                    onChange={(e) => {
                      const methods = [...settings.payment.paymentMethods];
                      if (e.target.checked) {
                        methods.push('wechat');
                      } else {
                        methods.splice(methods.indexOf('wechat'), 1);
                      }
                      handleSettingChange('payment', 'paymentMethods', methods);
                    }}
                  />
                }
                label="微信支付"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.payment.paymentMethods.includes('stripe')}
                    onChange={(e) => {
                      const methods = [...settings.payment.paymentMethods];
                      if (e.target.checked) {
                        methods.push('stripe');
                      } else {
                        methods.splice(methods.indexOf('stripe'), 1);
                      }
                      handleSettingChange('payment', 'paymentMethods', methods);
                    }}
                  />
                }
                label="Stripe"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.payment.autoPayoutEnabled}
                    onChange={(e) => handleSettingChange('payment', 'autoPayoutEnabled', e.target.checked)}
                  />
                }
                label="自动结算"
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={selectedTab} index={4}>
          {/* 存储设置 */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="最大文件大小 (MB)"
                type="number"
                value={settings.storage.maxFileSize}
                onChange={(e) => handleSettingChange('storage', 'maxFileSize', parseInt(e.target.value))}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="存储限制 (GB)"
                type="number"
                value={settings.storage.storageLimit}
                onChange={(e) => handleSettingChange('storage', 'storageLimit', parseInt(e.target.value))}
                sx={{ mb: 2 }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.storage.enableCdn}
                    onChange={(e) => handleSettingChange('storage', 'enableCdn', e.target.checked)}
                  />
                }
                label="启用CDN"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                允许的文件类型
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {['jpg', 'png', 'gif', 'pdf', 'mp3', 'mp4', 'zip', 'rar', 'doc', 'docx'].map((type) => (
                  <Chip
                    key={type}
                    label={type.toUpperCase()}
                    color={settings.storage.allowedFileTypes.includes(type) ? 'primary' : 'default'}
                    onClick={() => {
                      const types = [...settings.storage.allowedFileTypes];
                      if (types.includes(type)) {
                        types.splice(types.indexOf(type), 1);
                      } else {
                        types.push(type);
                      }
                      handleSettingChange('storage', 'allowedFileTypes', types);
                    }}
                  />
                ))}
              </Box>
              {settings.storage.enableCdn && (
                <TextField
                  fullWidth
                  label="CDN URL"
                  value={settings.storage.cdnUrl}
                  onChange={(e) => handleSettingChange('storage', 'cdnUrl', e.target.value)}
                  sx={{ mt: 2 }}
                />
              )}
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={selectedTab} index={5}>
          {/* API设置 */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.api.enableApiAccess}
                    onChange={(e) => handleSettingChange('api', 'enableApiAccess', e.target.checked)}
                  />
                }
                label="启用API访问"
              />
              <TextField
                fullWidth
                label="API密钥"
                type={showApiKey ? 'text' : 'password'}
                value={settings.api.apiKey}
                onChange={(e) => handleSettingChange('api', 'apiKey', e.target.value)}
                sx={{ mb: 2 }}
                InputProps={{
                  endAdornment: (
                    <IconButton onClick={() => setShowApiKey(!showApiKey)}>
                      {showApiKey ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  ),
                }}
              />
              <TextField
                fullWidth
                label="API速率限制（请求/小时）"
                type="number"
                value={settings.api.apiRateLimit}
                onChange={(e) => handleSettingChange('api', 'apiRateLimit', parseInt(e.target.value))}
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.api.enableWebhooks}
                    onChange={(e) => handleSettingChange('api', 'enableWebhooks', e.target.checked)}
                  />
                }
                label="启用Webhook"
              />
              {settings.api.enableWebhooks && (
                <TextField
                  fullWidth
                  label="Webhook URL"
                  value={settings.api.webhookUrl}
                  onChange={(e) => handleSettingChange('api', 'webhookUrl', e.target.value)}
                  sx={{ mt: 2 }}
                />
              )}
            </Grid>
          </Grid>
        </TabPanel>

        <Divider sx={{ my: 3 }} />

        {/* 操作按钮 */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, p: 3 }}>
          <Button
            variant="outlined"
            startIcon={<RestoreIcon />}
            onClick={handleRestore}
          >
            恢复默认
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
          >
            保存设置
          </Button>
        </Box>
      </Card>

      {/* 保存确认对话框 */}
      <Dialog open={saveDialogOpen} onClose={() => setSaveDialogOpen(false)}>
        <DialogTitle>保存设置</DialogTitle>
        <DialogContent>
          <Alert severity="success">
            设置已成功保存！
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveDialogOpen(false)} variant="contained">
            确定
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SystemSettings;