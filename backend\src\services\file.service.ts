import { getDb } from '@/database/connection';
import {
  ValidationError,
  NotFoundError,
  FileUploadError
} from '@/utils/errors';
import { logger } from '@/utils/logger';
import fs from 'fs';
import path from 'path';

export interface FileData {
  originalName: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadedBy: number;
  description?: string;
  tags?: string[];
}

export interface FileFilters {
  page: number;
  limit: number;
  search?: string;
  fileType?: string;
  userId?: number;
}

export interface FileRecord {
  id: number;
  originalName: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  uploadedBy: number;
  description?: string;
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export class FileService {
  constructor() {}

  // 上传单个文件
  async uploadFile(fileData: FileData): Promise<FileRecord> {
    const db = getDb();
    const client = await db.connect();
    
    try {
      await client.query('BEGIN');

      const query = `
        INSERT INTO files (
          original_name, file_name, file_path, file_size, 
          mime_type, uploaded_by, description, tags
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;

      const values = [
        fileData.originalName,
        fileData.fileName,
        fileData.filePath,
        fileData.fileSize,
        fileData.mimeType,
        fileData.uploadedBy,
        fileData.description || null,
        fileData.tags || null,
      ];

      const result = await client.query(query, values);
      
      await client.query('COMMIT');

      const file = result.rows[0];
      
      // 如果是图片，生成缩略图
      if (this.isImage(fileData.mimeType)) {
        this.generateThumbnail(fileData.filePath, file.id).catch(err => {
          logger.error('Failed to generate thumbnail:', err);
        });
      }

      return this.mapFileRecord(file);
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('File upload error:', error);
      throw new FileUploadError('Failed to upload file');
    } finally {
      client.release();
    }
  }

  // 上传多个文件
  async uploadMultipleFiles(filesData: FileData[]): Promise<FileRecord[]> {
    const db = getDb();
    const client = await db.connect();
    
    try {
      await client.query('BEGIN');

      const uploadedFiles: FileRecord[] = [];

      for (const fileData of filesData) {
        const query = `
          INSERT INTO files (
            original_name, file_name, file_path, file_size, 
            mime_type, uploaded_by, description, tags
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          RETURNING *
        `;

        const values = [
          fileData.originalName,
          fileData.fileName,
          fileData.filePath,
          fileData.fileSize,
          fileData.mimeType,
          fileData.uploadedBy,
          fileData.description || null,
          fileData.tags || null,
        ];

        const result = await client.query(query, values);
        const file = result.rows[0];
        
        uploadedFiles.push(this.mapFileRecord(file));

        // 如果是图片，生成缩略图
        if (this.isImage(fileData.mimeType)) {
          this.generateThumbnail(fileData.filePath, file.id).catch(err => {
            logger.error('Failed to generate thumbnail:', err);
          });
        }
      }

      await client.query('COMMIT');
      return uploadedFiles;
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Multiple files upload error:', error);
      throw new FileUploadError('Failed to upload files');
    } finally {
      client.release();
    }
  }

  // 获取文件列表
  async getFiles(filters: FileFilters): Promise<{
    files: FileRecord[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const db = getDb();
    const client = await db.connect();
    
    try {
      let whereClause = '';
      const queryParams: any[] = [];
      let paramIndex = 1;

      // 构建查询条件
      const conditions: string[] = [];

      if (filters.userId) {
        conditions.push(`uploaded_by = $${paramIndex++}`);
        queryParams.push(filters.userId);
      }

      if (filters.search) {
        conditions.push(`original_name ILIKE $${paramIndex++}`);
        queryParams.push(`%${filters.search}%`);
      }

      if (filters.fileType) {
        conditions.push(`mime_type LIKE $${paramIndex++}`);
        queryParams.push(`${filters.fileType}%`);
      }

      if (conditions.length > 0) {
        whereClause = `WHERE ${conditions.join(' AND ')}`;
      }

      // 获取总数
      const countQuery = `SELECT COUNT(*) FROM files ${whereClause}`;
      const countResult = await client.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].count);

      // 获取分页数据
      const offset = (filters.page - 1) * filters.limit;
      const dataQuery = `
        SELECT * FROM files 
        ${whereClause}
        ORDER BY created_at DESC
        LIMIT $${paramIndex++} OFFSET $${paramIndex++}
      `;

      queryParams.push(filters.limit, offset);
      const dataResult = await client.query(dataQuery, queryParams);

      const files = dataResult.rows.map(file => this.mapFileRecord(file));
      const totalPages = Math.ceil(total / filters.limit);

      return {
        files,
        total,
        page: filters.page,
        limit: filters.limit,
        totalPages,
      };
    } catch (error) {
      logger.error('Get files error:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // 根据ID获取文件
  async getFileById(fileId: number): Promise<FileRecord | null> {
    const db = getDb();
    const client = await db.connect();
    
    try {
      const query = 'SELECT * FROM files WHERE id = $1';
      const result = await client.query(query, [fileId]);

      if (result.rows.length === 0) {
        return null;
      }

      return this.mapFileRecord(result.rows[0]);
    } catch (error) {
      logger.error('Get file by ID error:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // 删除文件
  async deleteFile(fileId: number): Promise<void> {
    const db = getDb();
    const client = await db.connect();
    
    try {
      await client.query('BEGIN');

      // 获取文件信息
      const file = await this.getFileById(fileId);
      if (!file) {
        throw new NotFoundError('File not found');
      }

      // 删除数据库记录
      const deleteQuery = 'DELETE FROM files WHERE id = $1';
      await client.query(deleteQuery, [fileId]);

      // 删除文件系统中的文件
      if (fs.existsSync(file.filePath)) {
        fs.unlinkSync(file.filePath);
      }

      // 删除缩略图
      const thumbnailPath = this.getThumbnailPath(fileId);
      if (fs.existsSync(thumbnailPath)) {
        fs.unlinkSync(thumbnailPath);
      }

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Delete file error:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // 更新文件信息
  async updateFile(fileId: number, updates: Partial<FileData>): Promise<FileRecord> {
    const db = getDb();
    const client = await db.connect();
    
    try {
      const updateFields: string[] = [];
      const queryParams: any[] = [];
      let paramIndex = 1;

      if (updates.description !== undefined) {
        updateFields.push(`description = $${paramIndex++}`);
        queryParams.push(updates.description);
      }

      if (updates.tags !== undefined) {
        updateFields.push(`tags = $${paramIndex++}`);
        queryParams.push(updates.tags);
      }

      if (updateFields.length === 0) {
        throw new ValidationError('No fields to update');
      }

      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
      queryParams.push(fileId);

      const query = `
        UPDATE files 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
      `;

      const result = await client.query(query, queryParams);

      if (result.rows.length === 0) {
        throw new NotFoundError('File not found');
      }

      return this.mapFileRecord(result.rows[0]);
    } catch (error) {
      logger.error('Update file error:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // 获取文件路径
  async getFilePath(fileId: number): Promise<string> {
    const file = await this.getFileById(fileId);
    if (!file) {
      throw new NotFoundError('File not found');
    }
    return file.filePath;
  }

  // 获取缩略图路径
  getThumbnailPath(fileId: number): string {
    return path.join('uploads', 'thumbnails', `${fileId}.jpg`);
  }

  // 检查是否是图片
  private isImage(mimeType: string): boolean {
    return mimeType.startsWith('image/');
  }

  // 生成缩略图（简化版本）
  private async generateThumbnail(filePath: string, fileId: number): Promise<void> {
    try {
      const thumbnailDir = path.join('uploads', 'thumbnails');
      if (!fs.existsSync(thumbnailDir)) {
        fs.mkdirSync(thumbnailDir, { recursive: true });
      }

      // 简化版本：仅创建缩略图占位符
      const thumbnailPath = this.getThumbnailPath(fileId);
      fs.writeFileSync(thumbnailPath, 'placeholder');
        
      logger.info(`Thumbnail placeholder created for file ${fileId}`);
    } catch (error) {
      logger.error('Thumbnail generation error:', error);
    }
  }

  // 映射数据库记录到FileRecord
  private mapFileRecord(row: any): FileRecord {
    return {
      id: row.id,
      originalName: row.original_name,
      fileName: row.file_name,
      filePath: row.file_path,
      fileSize: row.file_size,
      mimeType: row.mime_type,
      uploadedBy: row.uploaded_by,
      description: row.description,
      tags: row.tags,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }
}