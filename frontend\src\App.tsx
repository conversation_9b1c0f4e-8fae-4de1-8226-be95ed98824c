import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, Typography, CssBaseline } from '@mui/material';
import { useTranslation } from 'react-i18next';
import MainLayout from './components/Layout/MainLayout';
import HomePage from './pages/HomePage';
import Dashboard from './pages/Dashboard/Dashboard';
import ModernNavbar from './pages/HomePage/components/ModernNavbar';
import ModernHeroSection from './pages/HomePage/components/ModernHeroSection';
import ModernAssetGrid from './pages/HomePage/components/ModernAssetGrid';
import AssetList from './pages/Assets/AssetList';
import TradingCenter from './pages/Trading/TradingCenter';
import UserManagement from './pages/Users/<USER>';
import FileManagement from './pages/Files/FileManagement';
import SystemSettings from './pages/Settings/SystemSettings';

// 临时的登录页面组件
const LoginPage: React.FC = () => {
  const { t } = useTranslation('auth');

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'background.default',
      }}
    >
      <Box
        sx={{
          p: 4,
          backgroundColor: 'background.paper',
          borderRadius: 2,
          boxShadow: 3,
          maxWidth: 400,
          width: '100%',
        }}
      >
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <img
            src="/logo.png"
            alt="AIGC Service Hub"
            style={{ height: 60, marginBottom: 16 }}
            onError={(e) => {
              // 如果logo不存在，显示文字
              e.currentTarget.style.display = 'none';
            }}
          />
          <Box
            sx={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              color: 'primary.main',
              mb: 1,
            }}
          >
            AIGC Service Hub
          </Box>
          <Box sx={{ color: 'text.secondary' }}>
            专业的AI生成内容服务平台
          </Box>
        </Box>
        
        <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
          <p>请联系管理员获取登录凭据</p>
          <p>或访问以下页面体验系统功能：</p>
          <Box sx={{ mt: 2 }}>
            <a href="/dashboard" style={{ color: 'inherit', textDecoration: 'none' }}>
              → 进入仪表板
            </a>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

// 临时的上传页面组件
const AssetUpload: React.FC = () => {
  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          上传资产
        </Typography>
      </Box>
      <Box sx={{ p: 3, backgroundColor: 'background.paper', borderRadius: 2 }}>
        <Typography sx={{ color: 'text.secondary', textAlign: 'center' }}>
          资产上传功能开发中...
        </Typography>
      </Box>
    </Box>
  );
};

// 临时的分类管理页面
const AssetCategories: React.FC = () => {
  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          分类管理
        </Typography>
      </Box>
      <Box sx={{ p: 3, backgroundColor: 'background.paper', borderRadius: 2 }}>
        <Typography sx={{ color: 'text.secondary', textAlign: 'center' }}>
          分类管理功能开发中...
        </Typography>
      </Box>
    </Box>
  );
};

// 临时的订单管理页面
const OrderManagement: React.FC = () => {
  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          订单管理
        </Typography>
      </Box>
      <Box sx={{ p: 3, backgroundColor: 'background.paper', borderRadius: 2 }}>
        <Typography sx={{ color: 'text.secondary', textAlign: 'center' }}>
          订单管理功能开发中...
        </Typography>
      </Box>
    </Box>
  );
};

// 临时的支付记录页面
const PaymentRecords: React.FC = () => {
  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          支付记录
        </Typography>
      </Box>
      <Box sx={{ p: 3, backgroundColor: 'background.paper', borderRadius: 2 }}>
        <Typography sx={{ color: 'text.secondary', textAlign: 'center' }}>
          支付记录功能开发中...
        </Typography>
      </Box>
    </Box>
  );
};

// 临时的报表统计页面
const ReportsPage: React.FC = () => {
  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          报表统计
        </Typography>
      </Box>
      <Box sx={{ p: 3, backgroundColor: 'background.paper', borderRadius: 2 }}>
        <Typography sx={{ color: 'text.secondary', textAlign: 'center' }}>
          报表统计功能开发中...
        </Typography>
      </Box>
    </Box>
  );
};

// 临时的通知管理页面
const NotificationManagement: React.FC = () => {
  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          通知管理
        </Typography>
      </Box>
      <Box sx={{ p: 3, backgroundColor: 'background.paper', borderRadius: 2 }}>
        <Typography sx={{ color: 'text.secondary', textAlign: 'center' }}>
          通知管理功能开发中...
        </Typography>
      </Box>
    </Box>
  );
};

// 临时的安全设置页面
const SecuritySettings: React.FC = () => {
  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          安全设置
        </Typography>
      </Box>
      <Box sx={{ p: 3, backgroundColor: 'background.paper', borderRadius: 2 }}>
        <Typography sx={{ color: 'text.secondary', textAlign: 'center' }}>
          安全设置功能开发中...
        </Typography>
      </Box>
    </Box>
  );
};

// 临时的个人资料页面
const ProfilePage: React.FC = () => {
  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold' }}>
          个人资料
        </Typography>
      </Box>
      <Box sx={{ p: 3, backgroundColor: 'background.paper', borderRadius: 2 }}>
        <Typography sx={{ color: 'text.secondary', textAlign: 'center' }}>
          个人资料功能开发中...
        </Typography>
      </Box>
    </Box>
  );
};

// 主应用组件
const App: React.FC = () => {
  return (
    <Routes>
      {/* 主页面 - 公开访问的HomePage */}
      <Route path="/" element={<HomePage />} />

      {/* 现代化首页路由 */}
      <Route path="/modern" element={
        <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
          <CssBaseline />
          <ModernNavbar
            onSearch={() => {}}
            cartItemCount={0}
            notificationCount={3}
            user={null}
            onLogin={() => console.log('Login')}
            onRegister={() => console.log('Register')}
            onLogout={() => console.log('Logout')}
            darkMode={false}
            onToggleDarkMode={() => console.log('Toggle dark mode')}
          />
          <ModernHeroSection />
          <ModernAssetGrid
            assets={[]}
            loading={false}
            hasMore={false}
            onLoadMore={() => {}}
            searchQuery=""
            currentFilters={{}}
            onFiltersChange={() => {}}
            onSearch={() => {}}
          />
        </Box>
      } />

      {/* 登录页面 */}
      <Route path="/login" element={<LoginPage />} />

      {/* 管理后台路由 */}
      <Route path="/admin/*" element={
        <MainLayout>
          <Routes>
            {/* 默认重定向到仪表板 */}
            <Route path="/" element={<Navigate to="dashboard" replace />} />

            {/* 仪表板 */}
            <Route path="dashboard" element={<Dashboard />} />

            {/* 资产管理 */}
            <Route path="assets" element={<AssetList />} />
            <Route path="assets/upload" element={<AssetUpload />} />
            <Route path="assets/categories" element={<AssetCategories />} />

            {/* 交易中心 */}
            <Route path="trading" element={<TradingCenter />} />
            <Route path="trading/orders" element={<OrderManagement />} />
            <Route path="trading/payments" element={<PaymentRecords />} />

            {/* 用户管理 */}
            <Route path="users" element={<UserManagement />} />

            {/* 文件管理 */}
            <Route path="files" element={<FileManagement />} />

            {/* 报表统计 */}
            <Route path="reports" element={<ReportsPage />} />

            {/* 通知管理 */}
            <Route path="notifications" element={<NotificationManagement />} />

            {/* 安全设置 */}
            <Route path="security" element={<SecuritySettings />} />

            {/* 系统设置 */}
            <Route path="settings" element={<SystemSettings />} />

            {/* 个人资料 */}
            <Route path="profile" element={<ProfilePage />} />

            {/* 404 重定向 */}
            <Route path="*" element={<Navigate to="dashboard" replace />} />
          </Routes>
        </MainLayout>
      } />

      {/* 其他公开页面路由 */}
      <Route path="/assets/:id" element={<HomePage />} />
      <Route path="/creators/:id" element={<HomePage />} />
      <Route path="/challenges" element={<HomePage />} />
      <Route path="/bounties" element={<HomePage />} />

      {/* 兼容旧的dashboard路由，重定向到管理后台 */}
      <Route path="/dashboard" element={<Navigate to="/admin/dashboard" replace />} />

      {/* 404 重定向到主页 */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default App;