import { Request, Response, NextFunction } from 'express';
import { User, UserRole, Permission } from '../types';
declare global {
    namespace Express {
        interface Request {
            user?: User;
        }
    }
}
declare class AuthMiddleware {
    private authService;
    constructor();
    authenticate: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    optionalAuthenticate: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    authorize: (requiredPermissions: Permission[]) => ((req: Request, res: Response, next: NextFunction) => void);
    requireRole: (allowedRoles: UserRole[]) => ((req: Request, res: Response, next: NextFunction) => void);
    requireAdmin: (req: Request, res: Response, next: NextFunction) => void;
    requireCreator: (req: Request, res: Response, next: NextFunction) => void;
    requireResourceOwner: (getResourceId: (req: Request) => number, getResourceOwnerId: (req: Request) => Promise<number>) => ((req: Request, res: Response, next: NextFunction) => Promise<void>);
    requireActiveUser: (req: Request, res: Response, next: NextFunction) => void;
    sensitiveOperationRateLimit: (req: Request, res: Response, next: NextFunction) => void;
    verifyAssetAccess: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    verifyAssetPurchase: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    verifyApiKey: (req: Request, res: Response, next: NextFunction) => void;
    hasPermission: (user: User, permission: Permission) => boolean;
    hasRole: (user: User, role: UserRole) => boolean;
    isAdmin: (user: User) => boolean;
    isCreator: (user: User) => boolean;
}
declare const authMiddleware: AuthMiddleware;
export default authMiddleware;
export { AuthMiddleware };
//# sourceMappingURL=auth.middleware.d.ts.map