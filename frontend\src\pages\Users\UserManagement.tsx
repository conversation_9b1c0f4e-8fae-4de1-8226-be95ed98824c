import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tab,
  Tabs,
  FormControl,
  InputLabel,
  Select,
  InputAdornment,
  Pagination,
  useTheme,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  MoreVert as MoreVertIcon,
  Person as PersonIcon,
  AdminPanelSettings as AdminIcon,
  Business as BusinessIcon,
  Star as StarIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'user' | 'creator' | 'admin';
  status: 'active' | 'inactive' | 'suspended';
  avatar?: string;
  createdAt: string;
  lastLogin?: string;
  totalAssets: number;
  totalSales: number;
  totalEarnings: number;
  phone?: string;
  location?: string;
  isVerified: boolean;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div role="tabpanel" hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const UserManagement: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [selectedTab, setSelectedTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [actionMenuAnchor, setActionMenuAnchor] = useState<null | HTMLElement>(null);
  const [actionMenuUser, setActionMenuUser] = useState<User | null>(null);
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);

  // 模拟用户数据
  const users: User[] = [
    {
      id: '1',
      username: 'zhangsan',
      email: '<EMAIL>',
      firstName: '张',
      lastName: '三',
      role: 'creator',
      status: 'active',
      createdAt: '2024-01-15',
      lastLogin: '2024-01-20 10:30:00',
      totalAssets: 12,
      totalSales: 89,
      totalEarnings: 5678,
      phone: '13800138000',
      location: '北京市',
      isVerified: true,
    },
    {
      id: '2',
      username: 'lisi',
      email: '<EMAIL>',
      firstName: '李',
      lastName: '四',
      role: 'user',
      status: 'active',
      createdAt: '2024-01-10',
      lastLogin: '2024-01-19 15:45:00',
      totalAssets: 0,
      totalSales: 0,
      totalEarnings: 0,
      phone: '13900139000',
      location: '上海市',
      isVerified: false,
    },
    {
      id: '3',
      username: 'wangwu',
      email: '<EMAIL>',
      firstName: '王',
      lastName: '五',
      role: 'admin',
      status: 'active',
      createdAt: '2024-01-05',
      lastLogin: '2024-01-20 09:15:00',
      totalAssets: 25,
      totalSales: 156,
      totalEarnings: 12345,
      phone: '13700137000',
      location: '深圳市',
      isVerified: true,
    },
  ];

  const userStats = {
    total: 1248,
    active: 1089,
    inactive: 159,
    creators: 234,
    admins: 12,
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'creator':
        return 'primary';
      case 'user':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'suspended':
        return 'error';
      default:
        return 'default';
    }
  };

  const getRoleText = (role: string) => {
    switch (role) {
      case 'admin':
        return '管理员';
      case 'creator':
        return '创作者';
      case 'user':
        return '用户';
      default:
        return role;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '活跃';
      case 'inactive':
        return '非活跃';
      case 'suspended':
        return '已暂停';
      default:
        return status;
    }
  };

  const handleActionMenuOpen = (event: React.MouseEvent<HTMLElement>, user: User) => {
    setActionMenuAnchor(event.currentTarget);
    setActionMenuUser(user);
  };

  const handleActionMenuClose = () => {
    setActionMenuAnchor(null);
    setActionMenuUser(null);
  };

  const handleUserClick = (user: User) => {
    setSelectedUser(user);
  };

  const handleAddUser = () => {
    setAddUserDialogOpen(true);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          用户管理
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddUser}
        >
          添加用户
        </Button>
      </Box>

      {/* 用户统计卡片 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PersonIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">总用户</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {userStats.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">活跃用户</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {userStats.active}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <CancelIcon color="warning" sx={{ mr: 1 }} />
                <Typography variant="h6">非活跃</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {userStats.inactive}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <StarIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">创作者</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {userStats.creators}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <AdminIcon color="error" sx={{ mr: 1 }} />
                <Typography variant="h6">管理员</Typography>
              </Box>
              <Typography variant="h4" fontWeight="bold">
                {userStats.admins}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 搜索和筛选 */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, alignItems: 'center' }}>
        <TextField
          placeholder="搜索用户..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ flex: 1 }}
        />
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>角色</InputLabel>
          <Select
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
            label="角色"
          >
            <MenuItem value="all">全部</MenuItem>
            <MenuItem value="admin">管理员</MenuItem>
            <MenuItem value="creator">创作者</MenuItem>
            <MenuItem value="user">用户</MenuItem>
          </Select>
        </FormControl>
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>状态</InputLabel>
          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            label="状态"
          >
            <MenuItem value="all">全部</MenuItem>
            <MenuItem value="active">活跃</MenuItem>
            <MenuItem value="inactive">非活跃</MenuItem>
            <MenuItem value="suspended">已暂停</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* 用户列表 */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>用户</TableCell>
                <TableCell>邮箱</TableCell>
                <TableCell>角色</TableCell>
                <TableCell>状态</TableCell>
                <TableCell>资产数</TableCell>
                <TableCell>销售额</TableCell>
                <TableCell>注册时间</TableCell>
                <TableCell>最后登录</TableCell>
                <TableCell>操作</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar sx={{ mr: 2 }}>
                        {user.firstName[0]}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" fontWeight="medium">
                          {user.firstName} {user.lastName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          @{user.username}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body2">{user.email}</Typography>
                      {user.isVerified && (
                        <CheckCircleIcon color="success" sx={{ ml: 1, fontSize: 16 }} />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getRoleText(user.role)}
                      color={getRoleColor(user.role) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={getStatusText(user.status)}
                      color={getStatusColor(user.status) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{user.totalAssets}</TableCell>
                  <TableCell>¥{user.totalEarnings}</TableCell>
                  <TableCell>{user.createdAt}</TableCell>
                  <TableCell>{user.lastLogin || '-'}</TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={(e) => handleActionMenuOpen(e, user)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Card>

      {/* 分页 */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
        <Pagination
          count={10}
          page={page}
          onChange={(event, value) => setPage(value)}
          color="primary"
        />
      </Box>

      {/* 操作菜单 */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionMenuClose}
      >
        <MenuItem onClick={() => {
          handleActionMenuClose();
          actionMenuUser && handleUserClick(actionMenuUser);
        }}>
          <ListItemIcon>
            <PersonIcon />
          </ListItemIcon>
          <ListItemText>查看详情</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <EditIcon />
          </ListItemIcon>
          <ListItemText>编辑用户</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <BlockIcon />
          </ListItemIcon>
          <ListItemText>暂停用户</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <DeleteIcon />
          </ListItemIcon>
          <ListItemText>删除用户</ListItemText>
        </MenuItem>
      </Menu>

      {/* 用户详情对话框 */}
      <Dialog
        open={Boolean(selectedUser)}
        onClose={() => setSelectedUser(null)}
        maxWidth="md"
        fullWidth
      >
        {selectedUser && (
          <>
            <DialogTitle>
              用户详情 - {selectedUser.firstName} {selectedUser.lastName}
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                    <Avatar sx={{ width: 100, height: 100, mb: 2 }}>
                      {selectedUser.firstName[0]}
                    </Avatar>
                    <Typography variant="h6" gutterBottom>
                      {selectedUser.firstName} {selectedUser.lastName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      @{selectedUser.username}
                    </Typography>
                    <Chip
                      label={getRoleText(selectedUser.role)}
                      color={getRoleColor(selectedUser.role) as any}
                      size="small"
                    />
                  </Box>
                </Grid>
                <Grid item xs={12} md={8}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        邮箱地址
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <EmailIcon sx={{ mr: 1, fontSize: 16 }} />
                        <Typography variant="body1">{selectedUser.email}</Typography>
                        {selectedUser.isVerified && (
                          <CheckCircleIcon color="success" sx={{ ml: 1, fontSize: 16 }} />
                        )}
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        联系电话
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <PhoneIcon sx={{ mr: 1, fontSize: 16 }} />
                        <Typography variant="body1">{selectedUser.phone || '-'}</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        所在地区
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <LocationIcon sx={{ mr: 1, fontSize: 16 }} />
                        <Typography variant="body1">{selectedUser.location || '-'}</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        注册时间
                      </Typography>
                      <Typography variant="body1" sx={{ mb: 2 }}>
                        {selectedUser.createdAt}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        最后登录
                      </Typography>
                      <Typography variant="body1" sx={{ mb: 2 }}>
                        {selectedUser.lastLogin || '-'}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="text.secondary">
                        账户状态
                      </Typography>
                      <Box sx={{ mb: 2 }}>
                        <Chip
                          label={getStatusText(selectedUser.status)}
                          color={getStatusColor(selectedUser.status) as any}
                          size="small"
                        />
                      </Box>
                    </Grid>
                  </Grid>
                  
                  <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
                    统计信息
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">
                        总资产
                      </Typography>
                      <Typography variant="h6">{selectedUser.totalAssets}</Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">
                        总销售
                      </Typography>
                      <Typography variant="h6">{selectedUser.totalSales}</Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">
                        总收入
                      </Typography>
                      <Typography variant="h6">¥{selectedUser.totalEarnings}</Typography>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setSelectedUser(null)}>
                关闭
              </Button>
              <Button variant="outlined">
                编辑
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* 添加用户对话框 */}
      <Dialog
        open={addUserDialogOpen}
        onClose={() => setAddUserDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>添加新用户</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              label="用户名"
              variant="outlined"
              fullWidth
            />
            <TextField
              label="邮箱"
              type="email"
              variant="outlined"
              fullWidth
            />
            <TextField
              label="姓"
              variant="outlined"
              fullWidth
            />
            <TextField
              label="名"
              variant="outlined"
              fullWidth
            />
            <FormControl fullWidth>
              <InputLabel>角色</InputLabel>
              <Select label="角色">
                <MenuItem value="user">用户</MenuItem>
                <MenuItem value="creator">创作者</MenuItem>
                <MenuItem value="admin">管理员</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="初始密码"
              type="password"
              variant="outlined"
              fullWidth
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddUserDialogOpen(false)}>
            取消
          </Button>
          <Button variant="contained" onClick={() => setAddUserDialogOpen(false)}>
            添加
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserManagement;