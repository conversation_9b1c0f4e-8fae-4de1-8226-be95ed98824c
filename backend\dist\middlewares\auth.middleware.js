"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthMiddleware = void 0;
const auth_service_1 = require("../services/auth.service");
const errors_1 = require("../utils/errors");
const types_1 = require("../types");
const logger_1 = require("../utils/logger");
const ROLE_PERMISSIONS = {
    [types_1.UserRole.PERSONAL_CREATOR]: [
        types_1.Permission.READ_PUBLIC,
        types_1.Permission.READ_PRIVATE,
        types_1.Permission.WRITE_ASSET,
        types_1.Permission.FINANCE_WITHDRAW,
    ],
    [types_1.UserRole.ENTERPRISE_CREATOR]: [
        types_1.Permission.READ_PUBLIC,
        types_1.Permission.READ_PRIVATE,
        types_1.Permission.WRITE_ASSET,
        types_1.Permission.FINANCE_WITHDRAW,
    ],
    [types_1.UserRole.ADMIN]: [
        types_1.Permission.READ_PUBLIC,
        types_1.Permission.READ_PRIVATE,
        types_1.Permission.WRITE_ASSET,
        types_1.Permission.ADMIN_PANEL,
    ],
};
class AuthMiddleware {
    constructor() {
        this.authenticate = async (req, res, next) => {
            try {
                const authHeader = req.headers.authorization;
                if (!authHeader || !authHeader.startsWith('Bearer ')) {
                    throw new errors_1.AuthenticationError('No token provided');
                }
                const token = authHeader.substring(7);
                if (!token) {
                    throw new errors_1.AuthenticationError('No token provided');
                }
                const user = await this.authService.verifyAccessToken(token);
                if (!user) {
                    throw new errors_1.AuthenticationError('Invalid token');
                }
                req.user = user;
                next();
            }
            catch (error) {
                (0, logger_1.logSecurityEvent)('AUTH_FAILED', undefined, {
                    ip: req.ip,
                    userAgent: req.get('User-Agent'),
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
                if (error instanceof errors_1.TokenExpiredError || error instanceof errors_1.InvalidTokenError) {
                    next(error);
                }
                else {
                    next(new errors_1.AuthenticationError('Authentication failed'));
                }
            }
        };
        this.optionalAuthenticate = async (req, res, next) => {
            try {
                const authHeader = req.headers.authorization;
                if (authHeader && authHeader.startsWith('Bearer ')) {
                    const token = authHeader.substring(7);
                    if (token) {
                        const user = await this.authService.verifyAccessToken(token);
                        if (user) {
                            req.user = user;
                        }
                    }
                }
                next();
            }
            catch (error) {
                next();
            }
        };
        this.authorize = (requiredPermissions) => {
            return (req, res, next) => {
                try {
                    if (!req.user) {
                        throw new errors_1.AuthenticationError('User not authenticated');
                    }
                    const userPermissions = ROLE_PERMISSIONS[req.user.userRole] || [];
                    const hasPermission = requiredPermissions.every(permission => userPermissions.includes(permission));
                    if (!hasPermission) {
                        (0, logger_1.logSecurityEvent)('AUTHORIZATION_FAILED', req.user.id, {
                            requiredPermissions,
                            userRole: req.user.userRole,
                            ip: req.ip,
                        });
                        throw new errors_1.AuthorizationError('Insufficient permissions');
                    }
                    next();
                }
                catch (error) {
                    next(error);
                }
            };
        };
        this.requireRole = (allowedRoles) => {
            return (req, res, next) => {
                try {
                    if (!req.user) {
                        throw new errors_1.AuthenticationError('User not authenticated');
                    }
                    if (!allowedRoles.includes(req.user.userRole)) {
                        (0, logger_1.logSecurityEvent)('ROLE_ACCESS_DENIED', req.user.id, {
                            userRole: req.user.userRole,
                            allowedRoles,
                            ip: req.ip,
                        });
                        throw new errors_1.AuthorizationError(`Access denied. Required roles: ${allowedRoles.join(', ')}`);
                    }
                    next();
                }
                catch (error) {
                    next(error);
                }
            };
        };
        this.requireAdmin = (req, res, next) => {
            this.requireRole([types_1.UserRole.ADMIN])(req, res, next);
        };
        this.requireCreator = (req, res, next) => {
            this.requireRole([types_1.UserRole.PERSONAL_CREATOR, types_1.UserRole.ENTERPRISE_CREATOR])(req, res, next);
        };
        this.requireResourceOwner = (getResourceId, getResourceOwnerId) => {
            return async (req, res, next) => {
                try {
                    if (!req.user) {
                        throw new errors_1.AuthenticationError('User not authenticated');
                    }
                    if (req.user.userRole === types_1.UserRole.ADMIN) {
                        return next();
                    }
                    const resourceId = getResourceId(req);
                    const resourceOwnerId = await getResourceOwnerId(req);
                    if (req.user.id !== resourceOwnerId) {
                        (0, logger_1.logSecurityEvent)('RESOURCE_ACCESS_DENIED', req.user.id, {
                            resourceId,
                            resourceOwnerId,
                            ip: req.ip,
                        });
                        throw new errors_1.AuthorizationError('You can only access your own resources');
                    }
                    next();
                }
                catch (error) {
                    next(error);
                }
            };
        };
        this.requireActiveUser = (req, res, next) => {
            try {
                if (!req.user) {
                    throw new errors_1.AuthenticationError('User not authenticated');
                }
                if (!req.user.isActive) {
                    (0, logger_1.logSecurityEvent)('INACTIVE_USER_ACCESS', req.user.id, { ip: req.ip });
                    throw new errors_1.AuthorizationError('Account is deactivated');
                }
                next();
            }
            catch (error) {
                next(error);
            }
        };
        this.sensitiveOperationRateLimit = (req, res, next) => {
            next();
        };
        this.verifyAssetAccess = async (req, res, next) => {
            try {
                if (!req.user) {
                    throw new errors_1.AuthenticationError('User not authenticated');
                }
                const assetId = parseInt(req.params.id);
                if (isNaN(assetId)) {
                    throw new errors_1.ValidationError('Invalid asset ID');
                }
                next();
            }
            catch (error) {
                next(error);
            }
        };
        this.verifyAssetPurchase = async (req, res, next) => {
            try {
                if (!req.user) {
                    throw new errors_1.AuthenticationError('User not authenticated');
                }
                const assetId = parseInt(req.params.id);
                if (isNaN(assetId)) {
                    throw new errors_1.ValidationError('Invalid asset ID');
                }
                next();
            }
            catch (error) {
                next(error);
            }
        };
        this.verifyApiKey = (req, res, next) => {
            try {
                const apiKey = req.headers['x-api-key'];
                if (!apiKey) {
                    throw new errors_1.AuthenticationError('API key required');
                }
                next();
            }
            catch (error) {
                next(error);
            }
        };
        this.hasPermission = (user, permission) => {
            const userPermissions = ROLE_PERMISSIONS[user.userRole] || [];
            return userPermissions.includes(permission);
        };
        this.hasRole = (user, role) => {
            return user.userRole === role;
        };
        this.isAdmin = (user) => {
            return user.userRole === types_1.UserRole.ADMIN;
        };
        this.isCreator = (user) => {
            return user.userRole === types_1.UserRole.PERSONAL_CREATOR || user.userRole === types_1.UserRole.ENTERPRISE_CREATOR;
        };
        this.authService = new auth_service_1.AuthService();
    }
}
exports.AuthMiddleware = AuthMiddleware;
const authMiddleware = new AuthMiddleware();
exports.default = authMiddleware;
//# sourceMappingURL=auth.middleware.js.map