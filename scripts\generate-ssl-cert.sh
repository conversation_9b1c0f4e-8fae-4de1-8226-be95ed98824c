#!/bin/bash

# ===========================================
# SSL证书生成脚本 - AIGC Service Hub
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SSL_DIR="./nginx/ssl"
DOMAIN="${DOMAIN:-localhost}"
COUNTRY="${SSL_COUNTRY:-US}"
STATE="${SSL_STATE:-California}"
CITY="${SSL_CITY:-San Francisco}"
ORG="${SSL_ORG:-AIGC Service Hub}"
OU="${SSL_OU:-IT Department}"
EMAIL="${SSL_EMAIL:-<EMAIL>}"

echo -e "${BLUE}🔐 AIGC Service Hub SSL证书生成工具${NC}"
echo -e "${BLUE}======================================${NC}"

# 创建SSL目录
echo -e "${YELLOW}📁 创建SSL目录...${NC}"
mkdir -p "$SSL_DIR"

# 检查是否已存在证书
if [[ -f "$SSL_DIR/server.crt" && -f "$SSL_DIR/server.key" ]]; then
    echo -e "${YELLOW}⚠️  SSL证书已存在${NC}"
    read -p "是否要重新生成? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${GREEN}✅ 保持现有证书${NC}"
        exit 0
    fi
fi

echo -e "${BLUE}🔧 证书配置信息:${NC}"
echo -e "   域名: ${GREEN}$DOMAIN${NC}"
echo -e "   国家: ${GREEN}$COUNTRY${NC}"
echo -e "   州/省: ${GREEN}$STATE${NC}"
echo -e "   城市: ${GREEN}$CITY${NC}"
echo -e "   组织: ${GREEN}$ORG${NC}"
echo -e "   部门: ${GREEN}$OU${NC}"
echo -e "   邮箱: ${GREEN}$EMAIL${NC}"
echo

# 生成私钥
echo -e "${YELLOW}🔑 生成私钥...${NC}"
openssl genrsa -out "$SSL_DIR/server.key" 2048

# 生成证书签名请求 (CSR)
echo -e "${YELLOW}📝 生成证书签名请求...${NC}"
openssl req -new -key "$SSL_DIR/server.key" -out "$SSL_DIR/server.csr" -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORG/OU=$OU/CN=$DOMAIN/emailAddress=$EMAIL"

# 生成自签名证书
echo -e "${YELLOW}📜 生成自签名证书...${NC}"
openssl x509 -req -days 365 -in "$SSL_DIR/server.csr" -signkey "$SSL_DIR/server.key" -out "$SSL_DIR/server.crt" -extensions v3_req -extfile <(
cat <<EOF
[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = www.$DOMAIN
DNS.3 = api.$DOMAIN
DNS.4 = localhost
IP.1 = 127.0.0.1
IP.2 = ::1
EOF
)

# 生成DH参数（用于增强安全性）
echo -e "${YELLOW}🔐 生成DH参数...${NC}"
openssl dhparam -out "$SSL_DIR/dhparam.pem" 2048

# 设置文件权限
echo -e "${YELLOW}🔒 设置文件权限...${NC}"
chmod 600 "$SSL_DIR/server.key"
chmod 644 "$SSL_DIR/server.crt"
chmod 644 "$SSL_DIR/dhparam.pem"

# 清理临时文件
rm -f "$SSL_DIR/server.csr"

# 验证证书
echo -e "${YELLOW}✅ 验证证书...${NC}"
openssl x509 -in "$SSL_DIR/server.crt" -text -noout | grep -E "(Subject:|Issuer:|Not Before:|Not After:|DNS:|IP Address:)"

echo -e "${GREEN}🎉 SSL证书生成完成!${NC}"
echo -e "${GREEN}=================${NC}"
echo -e "证书文件位置:"
echo -e "  私钥: ${BLUE}$SSL_DIR/server.key${NC}"
echo -e "  证书: ${BLUE}$SSL_DIR/server.crt${NC}"
echo -e "  DH参数: ${BLUE}$SSL_DIR/dhparam.pem${NC}"
echo
echo -e "${YELLOW}📋 下一步操作:${NC}"
echo -e "1. 更新 .env.production 中的域名配置"
echo -e "2. 确保 nginx/conf.d/default.conf 中的SSL配置正确"
echo -e "3. 运行: ${BLUE}docker-compose -f docker-compose.prod.yml up -d${NC}"
echo
echo -e "${RED}⚠️  注意: 这是自签名证书，仅用于开发和测试${NC}"
echo -e "${RED}   生产环境请使用 Let's Encrypt 或购买商业证书${NC}"

# 生成Let's Encrypt脚本提示
cat > "$SSL_DIR/letsencrypt-setup.sh" << 'EOF'
#!/bin/bash
# Let's Encrypt证书获取脚本
# 使用前请确保:
# 1. 域名已正确解析到服务器
# 2. 80端口可访问
# 3. 已安装certbot

DOMAIN="your-domain.com"
EMAIL="<EMAIL>"

# 停止nginx
docker-compose -f docker-compose.prod.yml stop nginx

# 获取证书
certbot certonly --standalone \
  --email $EMAIL \
  --agree-tos \
  --no-eff-email \
  -d $DOMAIN \
  -d www.$DOMAIN \
  -d api.$DOMAIN

# 复制证书到nginx目录
cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem ./nginx/ssl/server.crt
cp /etc/letsencrypt/live/$DOMAIN/privkey.pem ./nginx/ssl/server.key

# 重启nginx
docker-compose -f docker-compose.prod.yml start nginx

echo "Let's Encrypt证书配置完成!"
EOF

chmod +x "$SSL_DIR/letsencrypt-setup.sh"

echo -e "${BLUE}💡 已创建 Let's Encrypt 设置脚本: $SSL_DIR/letsencrypt-setup.sh${NC}"
echo -e "${BLUE}   编辑该脚本并运行以获取真实的SSL证书${NC}"
