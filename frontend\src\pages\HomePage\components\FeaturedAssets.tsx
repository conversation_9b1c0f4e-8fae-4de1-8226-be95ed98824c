import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  Avatar,
  Chip,
  IconButton,
  Skeleton,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Favorite,
  FavoriteBorder,
  Download,
  Visibility,
  Star,
  AttachMoney
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Asset } from '../../../types';

interface FeaturedAssetsProps {
  assets: Asset[];
  loading: boolean;
}

const FeaturedAssets: React.FC<FeaturedAssetsProps> = ({ assets, loading }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleAssetClick = (assetId: string) => {
    navigate(`/assets/${assetId}`);
  };

  const handleCreatorClick = (creatorId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    navigate(`/creators/${creatorId}`);
  };

  const handleLikeToggle = (assetId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    // 实现点赞功能
    console.log('Toggle like for asset:', assetId);
  };

  const formatPrice = (price: number, currency: string) => {
    if (price === 0) {
      return t('common.free');
    }
    return currency === 'USD' ? `$${price}` : `${price} ${currency}`;
  };

  const formatNumber = (num: number): string => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}万`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  const AssetCard: React.FC<{ asset: Asset }> = ({ asset }) => (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: theme.shadows[8]
        }
      }}
      onClick={() => handleAssetClick(asset.id)}
    >
      {/* 缩略图区域 */}
      <Box sx={{ position: 'relative' }}>
        <CardMedia
          component="img"
          height="120"
          image={asset.thumbnailUrl || '/images/placeholder-asset.jpg'}
          alt={asset.title}
          sx={{
            objectFit: 'cover',
            backgroundColor: 'grey.100'
          }}
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/images/placeholder-asset.jpg';
          }}
        />
        
        {/* 价格标签 */}
        <Chip
          label={formatPrice(asset.price, asset.currency)}
          size="small"
          color={asset.isFree ? 'success' : 'primary'}
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            fontWeight: 'bold',
            backgroundColor: asset.isFree ? 'success.main' : 'primary.main',
            color: 'white'
          }}
        />

        {/* 类型标签 */}
        <Chip
          label={asset.type}
          size="small"
          variant="outlined"
          sx={{
            position: 'absolute',
            top: 8,
            left: 8,
            backgroundColor: 'rgba(255,255,255,0.9)',
            fontSize: '0.7rem'
          }}
        />

        {/* 点赞按钮 */}
        <IconButton
          size="small"
          sx={{
            position: 'absolute',
            bottom: 8,
            right: 8,
            backgroundColor: 'rgba(255,255,255,0.9)',
            '&:hover': {
              backgroundColor: 'rgba(255,255,255,1)'
            }
          }}
          onClick={(e) => handleLikeToggle(asset.id, e)}
        >
          {asset.stats?.likes > 0 ? (
            <Favorite sx={{ fontSize: 16, color: 'error.main' }} />
          ) : (
            <FavoriteBorder sx={{ fontSize: 16 }} />
          )}
        </IconButton>
      </Box>

      {/* 内容区域 */}
      <CardContent sx={{ flexGrow: 1, p: 2 }}>
        <Typography
          variant="subtitle2"
          component="h3"
          sx={{
            fontWeight: 'bold',
            mb: 1,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}
        >
          {asset.title}
        </Typography>

        {/* 创作者信息 */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            mb: 1,
            cursor: 'pointer'
          }}
          onClick={(e) => handleCreatorClick(asset.creatorId, e)}
        >
          <Avatar
            src={asset.creator?.avatar}
            sx={{ width: 20, height: 20, mr: 1 }}
          >
            {asset.creator?.firstName?.charAt(0)}
          </Avatar>
          <Typography variant="caption" color="text.secondary" noWrap>
            {asset.creator?.firstName} {asset.creator?.lastName}
          </Typography>
        </Box>

        {/* 统计信息 */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Download sx={{ fontSize: 12, color: 'text.secondary' }} />
            <Typography variant="caption" color="text.secondary">
              {formatNumber(asset.stats?.downloads || 0)}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Visibility sx={{ fontSize: 12, color: 'text.secondary' }} />
            <Typography variant="caption" color="text.secondary">
              {formatNumber(asset.stats?.views || 0)}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Star sx={{ fontSize: 12, color: 'warning.main' }} />
            <Typography variant="caption" color="text.secondary">
              {asset.stats?.avgRating?.toFixed(1) || '0.0'}
            </Typography>
          </Box>
        </Box>

        {/* 标签 */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {asset.tags?.slice(0, 2).map((tag, index) => (
            <Chip
              key={index}
              label={tag}
              size="small"
              variant="outlined"
              sx={{
                fontSize: '0.65rem',
                height: 20
              }}
            />
          ))}
          {asset.tags?.length > 2 && (
            <Typography variant="caption" color="text.secondary">
              +{asset.tags.length - 2}
            </Typography>
          )}
        </Box>
      </CardContent>

      {/* 操作区域 */}
      <CardActions sx={{ p: 2, pt: 0 }}>
        <Button
          size="small"
          variant="contained"
          fullWidth
          startIcon={asset.isFree ? <Download /> : <AttachMoney />}
          onClick={(e) => {
            e.stopPropagation();
            // 处理下载或购买
            console.log('Download/Purchase asset:', asset.id);
          }}
        >
          {asset.isFree ? t('common.download') : t('common.purchase')}
        </Button>
      </CardActions>
    </Card>
  );

  const SkeletonCard: React.FC = () => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Skeleton variant="rectangular" height={120} />
      <CardContent sx={{ flexGrow: 1 }}>
        <Skeleton variant="text" height={24} sx={{ mb: 1 }} />
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Skeleton variant="circular" width={20} height={20} sx={{ mr: 1 }} />
          <Skeleton variant="text" width="60%" />
        </Box>
        <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
          <Skeleton variant="text" width="30%" />
          <Skeleton variant="text" width="30%" />
          <Skeleton variant="text" width="30%" />
        </Box>
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <Skeleton variant="rounded" width={40} height={20} />
          <Skeleton variant="rounded" width={50} height={20} />
        </Box>
      </CardContent>
      <CardActions sx={{ p: 2, pt: 0 }}>
        <Skeleton variant="rounded" width="100%" height={32} />
      </CardActions>
    </Card>
  );

  return (
    <Box
      sx={{
        height: 180,
        p: 2,
        backgroundColor: 'background.default'
      }}
    >
      {/* 标题 */}
      <Box sx={{ mb: 2 }}>
        <Typography
          variant="h5"
          component="h2"
          sx={{
            fontWeight: 'bold',
            color: 'text.primary',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
        >
          <Star sx={{ color: 'warning.main' }} />
          {t('home.featured.title')}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {t('home.featured.subtitle')}
        </Typography>
      </Box>

      {/* 精选作品网格 */}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            sm: 'repeat(2, 1fr)',
            md: 'repeat(3, 1fr)'
          },
          gap: 2,
          height: 'calc(100% - 60px)'
        }}
      >
        {loading ? (
          Array.from({ length: 3 }).map((_, index) => (
            <SkeletonCard key={index} />
          ))
        ) : assets.length > 0 ? (
          assets.slice(0, 3).map((asset) => (
            <AssetCard key={asset.id} asset={asset} />
          ))
        ) : (
          <Box
            sx={{
              gridColumn: '1 / -1',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'text.secondary'
            }}
          >
            <Typography variant="body2">
              {t('home.featured.empty')}
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default FeaturedAssets;
