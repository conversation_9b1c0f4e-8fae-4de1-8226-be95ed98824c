import React, { useState } from 'react';
import {
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Typography,
  Avatar,
  Chip,
  IconButton,
  Button,
  Box,
  Stack,
  Rating,
  Tooltip,
  Fade,
  Zoom,
  useTheme,
} from '@mui/material';
import {
  Favorite,
  FavoriteBorder,
  Download,
  Visibility,
  Share,
  ShoppingCart,
  PlayArrow,
  MoreVert,
  Verified,
} from '@mui/icons-material';
import { Asset } from '../../../types';

interface ModernAssetCardProps {
  asset: Asset;
  onLike?: (assetId: string) => void;
  onDownload?: (assetId: string) => void;
  onAddToCart?: (assetId: string) => void;
  onPreview?: (assetId: string) => void;
  loading?: boolean;
}

const ModernAssetCard: React.FC<ModernAssetCardProps> = ({
  asset,
  onLike,
  onDownload,
  onAddToCart,
  onPreview,
  loading = false,
}) => {
  const theme = useTheme();
  const [isLiked, setIsLiked] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const handleLike = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsLiked(!isLiked);
    onLike?.(asset.id);
  };

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDownload?.(asset.id);
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAddToCart?.(asset.id);
  };

  const handlePreview = (e: React.MouseEvent) => {
    e.stopPropagation();
    onPreview?.(asset.id);
  };

  const formatPrice = (price: number) => {
    return price === 0 ? 'Free' : `$${price.toFixed(2)}`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const getCategoryColor = (category: any) => {
    const categoryName = typeof category === 'string' ? category : category?.name || 'other';
    const colors: Record<string, string> = {
      'image': '#FF6B6B',
      'video': '#4ECDC4',
      'audio': '#45B7D1',
      'model': '#96CEB4',
      'tool': '#FFEAA7',
      'template': '#DDA0DD',
    };
    return colors[categoryName.toLowerCase()] || theme.palette.primary.main;
  };

  return (
    <Fade in timeout={300}>
      <Card
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          cursor: 'pointer',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          transform: isHovered ? 'translateY(-8px)' : 'translateY(0)',
          boxShadow: isHovered ? 8 : 1,
          '&:hover': {
            '& .asset-overlay': {
              opacity: 1,
            },
            '& .asset-media': {
              transform: 'scale(1.05)',
            },
          },
        }}
      >
        {/* 图片区域 */}
        <Box sx={{ position: 'relative', overflow: 'hidden' }}>
          <CardMedia
            component="img"
            height="200"
            image={asset.thumbnailUrl || 'https://via.placeholder.com/300x200?text=No+Image'}
            alt={asset.title}
            className="asset-media"
            onLoad={() => setImageLoaded(true)}
            sx={{
              transition: 'transform 0.3s ease',
              bgcolor: 'grey.100',
            }}
          />
          
          {/* 悬浮覆盖层 */}
          <Box
            className="asset-overlay"
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'rgba(0, 0, 0, 0.6)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              opacity: 0,
              transition: 'opacity 0.3s ease',
            }}
          >
            <Stack direction="row" spacing={1}>
              <Tooltip title="预览">
                <IconButton
                  onClick={handlePreview}
                  sx={{
                    bgcolor: 'white',
                    color: 'primary.main',
                    '&:hover': { bgcolor: 'grey.100' },
                  }}
                >
                  <PlayArrow />
                </IconButton>
              </Tooltip>
              <Tooltip title="快速查看">
                <IconButton
                  sx={{
                    bgcolor: 'white',
                    color: 'primary.main',
                    '&:hover': { bgcolor: 'grey.100' },
                  }}
                >
                  <Visibility />
                </IconButton>
              </Tooltip>
              <Tooltip title="分享">
                <IconButton
                  sx={{
                    bgcolor: 'white',
                    color: 'primary.main',
                    '&:hover': { bgcolor: 'grey.100' },
                  }}
                >
                  <Share />
                </IconButton>
              </Tooltip>
            </Stack>
          </Box>

          {/* 价格标签 */}
          <Chip
            label={formatPrice(asset.price)}
            size="small"
            sx={{
              position: 'absolute',
              top: 12,
              right: 12,
              bgcolor: asset.price === 0 ? 'success.main' : 'primary.main',
              color: 'white',
              fontWeight: 'bold',
            }}
          />

          {/* 分类标签 */}
          <Chip
            label={typeof asset.category === 'string' ? asset.category : asset.category?.name || '未分类'}
            size="small"
            sx={{
              position: 'absolute',
              top: 12,
              left: 12,
              bgcolor: getCategoryColor(asset.category),
              color: 'white',
              fontSize: '0.75rem',
            }}
          />

          {/* 喜欢按钮 */}
          <IconButton
            onClick={handleLike}
            sx={{
              position: 'absolute',
              bottom: 8,
              right: 8,
              bgcolor: 'rgba(255, 255, 255, 0.9)',
              '&:hover': { bgcolor: 'white' },
            }}
          >
            {isLiked ? (
              <Favorite sx={{ color: 'red' }} />
            ) : (
              <FavoriteBorder />
            )}
          </IconButton>
        </Box>

        {/* 内容区域 */}
        <CardContent sx={{ flexGrow: 1, pb: 1 }}>
          <Typography
            variant="h6"
            component="h3"
            gutterBottom
            sx={{
              fontWeight: 'bold',
              fontSize: '1rem',
              lineHeight: 1.3,
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
            }}
          >
            {asset.title}
          </Typography>

          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              mb: 2,
            }}
          >
            {asset.description}
          </Typography>

          {/* 创作者信息 */}
          <Stack direction="row" spacing={1} alignItems="center" mb={2}>
            <Avatar
              src={asset.creator?.avatar}
              sx={{ width: 24, height: 24 }}
            >
              {asset.creator?.username?.[0]?.toUpperCase()}
            </Avatar>
            <Typography variant="body2" color="text.secondary" noWrap>
              {asset.creator?.username}
            </Typography>
            {asset.creator?.isVerified && (
              <Verified sx={{ fontSize: 16, color: 'primary.main' }} />
            )}
          </Stack>

          {/* 统计信息 */}
          <Stack direction="row" spacing={2} alignItems="center" mb={2}>
            <Stack direction="row" spacing={0.5} alignItems="center">
              <Download sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                {formatNumber(asset.stats?.downloads || 0)}
              </Typography>
            </Stack>
            <Stack direction="row" spacing={0.5} alignItems="center">
              <Visibility sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                {formatNumber(asset.stats?.views || 0)}
              </Typography>
            </Stack>
            <Rating
              value={asset.stats?.avgRating || 0}
              size="small"
              readOnly
              sx={{ ml: 'auto' }}
            />
          </Stack>

          {/* 标签 */}
          <Stack direction="row" spacing={0.5} flexWrap="wrap" gap={0.5}>
            {asset.tags?.slice(0, 3).map((tag) => (
              <Chip
                key={tag}
                label={tag}
                size="small"
                variant="outlined"
                sx={{
                  fontSize: '0.7rem',
                  height: 20,
                }}
              />
            ))}
            {asset.tags && asset.tags.length > 3 && (
              <Chip
                label={`+${asset.tags.length - 3}`}
                size="small"
                variant="outlined"
                sx={{
                  fontSize: '0.7rem',
                  height: 20,
                }}
              />
            )}
          </Stack>
        </CardContent>

        {/* 操作按钮 */}
        <CardActions sx={{ p: 2, pt: 0 }}>
          <Stack direction="row" spacing={1} width="100%">
            {asset.price === 0 ? (
              <Button
                variant="contained"
                startIcon={<Download />}
                onClick={handleDownload}
                fullWidth
                size="small"
              >
                免费下载
              </Button>
            ) : (
              <>
                <Button
                  variant="outlined"
                  startIcon={<ShoppingCart />}
                  onClick={handleAddToCart}
                  size="small"
                  sx={{ flex: 1 }}
                >
                  加入购物车
                </Button>
                <Button
                  variant="contained"
                  onClick={handleDownload}
                  size="small"
                  sx={{ flex: 1 }}
                >
                  立即购买
                </Button>
              </>
            )}
          </Stack>
        </CardActions>
      </Card>
    </Fade>
  );
};

export default ModernAssetCard;
