import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  IconButton,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  useTheme,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  People as PeopleIcon,
  ShoppingCart as ShoppingCartIcon,
  AttachMoney as MoneyIcon,
  Assessment as AssessmentIcon,
  FileUpload as FileUploadIcon,
  Download as DownloadIcon,
  Visibility as VisibilityIcon,
  Star as StarIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

interface StatCardProps {
  title: string;
  value: string | number;
  change: number;
  changeText: string;
  icon: React.ReactNode;
  color: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
}

const StatCard: React.FC<StatCardProps> = ({ title, value, change, changeText, icon, color }) => {
  const theme = useTheme();
  const isPositive = change >= 0;

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{
              backgroundColor: theme.palette[color].main,
              width: 56,
              height: 56,
              mr: 2,
            }}
          >
            {icon}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" component="div" fontWeight="bold">
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {isPositive ? (
            <TrendingUpIcon color="success" sx={{ mr: 1 }} />
          ) : (
            <TrendingDownIcon color="error" sx={{ mr: 1 }} />
          )}
          <Typography
            variant="body2"
            color={isPositive ? 'success.main' : 'error.main'}
            fontWeight="medium"
          >
            {isPositive ? '+' : ''}{change}%
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
            {changeText}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();

  const stats = [
    {
      title: '总用户数',
      value: '12,438',
      change: 12.5,
      changeText: '较上月',
      icon: <PeopleIcon />,
      color: 'primary' as const,
    },
    {
      title: '总交易额',
      value: '¥89,432',
      change: 8.3,
      changeText: '较上月',
      icon: <MoneyIcon />,
      color: 'success' as const,
    },
    {
      title: '资产数量',
      value: '3,847',
      change: 15.2,
      changeText: '较上月',
      icon: <AssessmentIcon />,
      color: 'info' as const,
    },
    {
      title: '订单数量',
      value: '2,156',
      change: -2.4,
      changeText: '较上月',
      icon: <ShoppingCartIcon />,
      color: 'warning' as const,
    },
  ];

  const recentActivities = [
    {
      id: 1,
      user: '张三',
      action: '上传了新资产',
      asset: 'AI生成的风景画',
      time: '2分钟前',
      avatar: '/avatars/user1.jpg',
    },
    {
      id: 2,
      user: '李四',
      action: '购买了资产',
      asset: '3D模型包',
      time: '5分钟前',
      avatar: '/avatars/user2.jpg',
    },
    {
      id: 3,
      user: '王五',
      action: '发布了评论',
      asset: '音频素材集',
      time: '10分钟前',
      avatar: '/avatars/user3.jpg',
    },
    {
      id: 4,
      user: '赵六',
      action: '下载了资产',
      asset: '视频特效包',
      time: '15分钟前',
      avatar: '/avatars/user4.jpg',
    },
  ];

  const popularAssets = [
    {
      id: 1,
      name: 'AI生成头像包',
      category: '图像',
      downloads: 1248,
      rating: 4.8,
      price: '¥99',
    },
    {
      id: 2,
      name: '3D建筑模型',
      category: '3D模型',
      downloads: 892,
      rating: 4.6,
      price: '¥199',
    },
    {
      id: 3,
      name: '背景音乐合集',
      category: '音频',
      downloads: 756,
      rating: 4.9,
      price: '¥79',
    },
    {
      id: 4,
      name: '视频转场特效',
      category: '视频',
      downloads: 634,
      rating: 4.7,
      price: '¥129',
    },
  ];

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
        仪表板
      </Typography>

      {/* 统计卡片 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <StatCard {...stat} />
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* 快速操作 */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                快速操作
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<FileUploadIcon />}
                  fullWidth
                  sx={{ justifyContent: 'flex-start' }}
                >
                  上传新资产
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<PeopleIcon />}
                  fullWidth
                  sx={{ justifyContent: 'flex-start' }}
                >
                  添加用户
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<AssessmentIcon />}
                  fullWidth
                  sx={{ justifyContent: 'flex-start' }}
                >
                  查看报表
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* 最近活动 */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                最近活动
              </Typography>
              <List>
                {recentActivities.map((activity) => (
                  <ListItem key={activity.id} divider>
                    <ListItemAvatar>
                      <Avatar>{activity.user[0]}</Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" fontWeight="medium">
                            {activity.user}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {activity.action}
                          </Typography>
                          <Chip
                            label={activity.asset}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem' }}
                          />
                        </Box>
                      }
                      secondary={activity.time}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* 热门资产 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                热门资产
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>资产名称</TableCell>
                      <TableCell>分类</TableCell>
                      <TableCell align="right">下载量</TableCell>
                      <TableCell align="right">评分</TableCell>
                      <TableCell align="right">价格</TableCell>
                      <TableCell align="right">操作</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {popularAssets.map((asset) => (
                      <TableRow key={asset.id} hover>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {asset.name}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={asset.category}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                            <DownloadIcon sx={{ mr: 1, fontSize: 16 }} />
                            {asset.downloads}
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                            <StarIcon sx={{ mr: 1, fontSize: 16, color: 'warning.main' }} />
                            {asset.rating}
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" fontWeight="medium" color="success.main">
                            {asset.price}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <IconButton size="small">
                            <MoreVertIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;