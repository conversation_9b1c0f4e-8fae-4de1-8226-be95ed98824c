# AIGC Service Hub MVP 1.0

> 一个全面的AIGC服务集成平台，提供用户管理、项目管理、API集成等核心功能，采用现代化的微服务架构和Docker化部署方案。

[![CI Pipeline](https://github.com/your-org/aigc-service-hub/actions/workflows/ci.yml/badge.svg)](https://github.com/your-org/aigc-service-hub/actions/workflows/ci.yml)
[![CD Pipeline](https://github.com/your-org/aigc-service-hub/actions/workflows/cd.yml/badge.svg)](https://github.com/your-org/aigc-service-hub/actions/workflows/cd.yml)
[![Security Scan](https://github.com/your-org/aigc-service-hub/actions/workflows/security.yml/badge.svg)](https://github.com/your-org/aigc-service-hub/actions/workflows/security.yml)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Docker](https://img.shields.io/badge/docker-ready-blue.svg)](https://www.docker.com/)
[![Node.js](https://img.shields.io/badge/node.js-18+-green.svg)](https://nodejs.org/)
[![PostgreSQL](https://img.shields.io/badge/postgresql-14+-blue.svg)](https://www.postgresql.org/)
[![Redis](https://img.shields.io/badge/redis-7+-red.svg)](https://redis.io/)

## 🚀 项目特性

### 核心功能
- **用户管理系统** - 完整的用户注册、登录、权限管理
- **项目管理** - 项目创建、配置、版本控制
- **API集成** - 多种AIGC服务API的统一集成
- **实时监控** - 全面的系统监控和告警
- **数据分析** - 用户行为分析和业务指标统计

### 技术特性
- **微服务架构** - 松耦合、高可用的服务设计
- **Docker化部署** - 完整的容器化解决方案
- **云原生** - 支持Kubernetes和多云部署
- **自动化CI/CD** - GitHub Actions驱动的持续集成部署
- **全链路监控** - Prometheus + Grafana + ELK Stack
- **高可用性** - 多副本部署和故障转移
- **安全加固** - 完整的安全扫描和防护机制

## 📋 系统架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        Load Balancer (Nginx)                    │
├─────────────────────────────────────────────────────────────────┤
│                         API Gateway                             │
├─────────────────────────────────────────────────────────────────┤
│  Frontend (React)  │  Backend (Node.js)  │  Admin Dashboard    │
├─────────────────────────────────────────────────────────────────┤
│         Redis Cache        │         Message Queue              │
├─────────────────────────────────────────────────────────────────┤
│                    PostgreSQL Database                          │
├─────────────────────────────────────────────────────────────────┤
│      Monitoring Stack (Prometheus + Grafana + ELK)             │
└─────────────────────────────────────────────────────────────────┘
```

## 🛠️ 技术栈

### 前端技术
- **React 18** - 现代化的用户界面框架
- **TypeScript** - 类型安全的JavaScript超集
- **Tailwind CSS** - 实用优先的CSS框架
- **Zustand** - 轻量级状态管理
- **React Query** - 数据获取和缓存
- **Vite** - 快速的构建工具

### 后端技术
- **Node.js 18+** - 高性能JavaScript运行时
- **Express.js** - 轻量级Web应用框架
- **TypeScript** - 类型安全的开发体验
- **Prisma** - 现代化的ORM框架
- **JWT** - 无状态身份验证
- **Winston** - 结构化日志记录

### 数据库
- **PostgreSQL 14+** - 可靠的关系型数据库
- **Redis 7+** - 高性能缓存和会话存储
- **Prisma Migrate** - 数据库迁移管理

### 基础设施
- **Docker & Docker Compose** - 容器化部署
- **Nginx** - 反向代理和负载均衡
- **Prometheus** - 监控数据收集
- **Grafana** - 数据可视化
- **ELK Stack** - 日志分析
- **Jaeger** - 分布式追踪
- **GitHub Actions** - CI/CD自动化

## 🚀 快速开始

### 系统要求
- **Docker** 20.10+ 和 **Docker Compose** 2.0+
- **Node.js** 18+ (仅开发环境)
- **Git** 2.20+
- **最小系统配置**: 4GB RAM, 20GB 磁盘空间

### 一键部署

```bash
# 1. 克隆项目
git clone https://github.com/your-org/aigc-service-hub.git
cd aigc-service-hub

# 2. 复制环境配置
cp .env.example .env

# 3. 启动所有服务
./scripts/deploy.sh

# 4. 访问应用
# 前端: http://localhost:3000
# 后端API: http://localhost:3001
# 管理后台: http://localhost:3002
# 监控面板: http://localhost:3003
```

### 开发环境部署

```bash
# 1. 安装依赖
npm install

# 2. 启动开发环境
./scripts/dev-start.sh

# 3. 运行测试
npm test

# 4. 构建生产版本
npm run build
```

## 📖 详细文档

### 部署文档
- [📚 部署指南](docs/deployment/README.md)
- [🔧 环境配置](docs/deployment/environment.md)
- [🐳 Docker部署](docs/deployment/docker.md)
- [☸️ Kubernetes部署](docs/deployment/kubernetes.md)
- [🔄 CI/CD流程](docs/deployment/cicd.md)

### 开发文档
- [💻 开发指南](docs/development/README.md)
- [🏗️ 项目结构](docs/development/structure.md)
- [📝 API文档](docs/development/api.md)
- [🧪 测试指南](docs/development/testing.md)
- [🔍 调试指南](docs/development/debugging.md)

### 运维文档
- [📊 监控告警](docs/operations/monitoring.md)
- [🔒 安全配置](docs/operations/security.md)
- [💾 备份恢复](docs/operations/backup.md)
- [⚡ 性能优化](docs/operations/performance.md)
- [🛠️ 故障排除](docs/operations/troubleshooting.md)

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `NODE_ENV` | 运行环境 | `development` | ✅ |
| `DATABASE_URL` | 数据库连接字符串 | - | ✅ |
| `REDIS_URL` | Redis连接字符串 | - | ✅ |
| `JWT_SECRET` | JWT密钥 | - | ✅ |
| `SMTP_HOST` | 邮件服务器 | - | ❌ |
| `MONITORING_ENABLED` | 启用监控 | `true` | ❌ |

### 端口配置

| 服务 | 端口 | 说明 |
|------|------|------|
| 前端应用 | 3000 | React应用 |
| 后端API | 3001 | Express服务 |
| 管理后台 | 3002 | 管理界面 |
| 监控面板 | 3003 | Grafana |
| 数据库 | 5432 | PostgreSQL |
| 缓存 | 6379 | Redis |
| 代理 | 80/443 | Nginx |

## 🧪 测试

### 运行测试套件
```bash
# 单元测试
npm test

# 集成测试
npm run test:integration

# E2E测试
npm run test:e2e

# 覆盖率测试
npm run test:coverage

# 性能测试
npm run test:performance
```

### 测试覆盖率
- **后端**: 目标 > 80%
- **前端**: 目标 > 75%
- **集成**: 目标 > 70%

## 📊 监控和告警

### 监控指标
- **系统指标**: CPU、内存、磁盘、网络
- **应用指标**: 响应时间、错误率、吞吐量
- **业务指标**: 用户活跃度、API调用量
- **安全指标**: 登录失败、异常访问

### 告警配置
- **关键告警**: 立即通知（邮件+短信）
- **警告告警**: 5分钟内通知
- **信息告警**: 30分钟内通知

### 监控面板
- **系统概览**: http://localhost:3003/d/system
- **应用性能**: http://localhost:3003/d/application
- **业务指标**: http://localhost:3003/d/business
- **安全监控**: http://localhost:3003/d/security

## 🔒 安全

### 安全特性
- **身份验证**: JWT + 多因素认证
- **授权控制**: RBAC权限模型
- **数据加密**: 传输和存储加密
- **安全头**: 完整的HTTP安全头
- **输入验证**: 全面的输入验证和清理
- **审计日志**: 完整的操作审计

### 安全扫描
- **依赖扫描**: 自动检测漏洞依赖
- **代码扫描**: 静态代码安全分析
- **容器扫描**: Docker镜像安全扫描
- **渗透测试**: 定期安全测试

## 🚀 性能

### 性能指标
- **响应时间**: P95 < 200ms
- **并发处理**: 1000+ 并发用户
- **可用性**: 99.9% SLA
- **数据库**: < 50ms 查询时间

### 性能优化
- **缓存策略**: 多层缓存架构
- **CDN加速**: 静态资源分发
- **数据库优化**: 索引和查询优化
- **负载均衡**: 多实例部署

## 🤝 贡献指南

### 开发流程
1. Fork项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

### 代码规范
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Husky**: Git钩子管理
- **Conventional Commits**: 提交信息规范

### 问题反馈
- [🐛 Bug报告](https://github.com/your-org/aigc-service-hub/issues/new?template=bug_report.md)
- [✨ 功能请求](https://github.com/your-org/aigc-service-hub/issues/new?template=feature_request.md)
- [❓ 问题讨论](https://github.com/your-org/aigc-service-hub/discussions)

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢所有为本项目做出贡献的开发者和社区成员。

---

## 📞 联系我们

- **项目主页**: https://github.com/your-org/aigc-service-hub
- **文档站点**: https://docs.aigc-hub.com
- **问题反馈**: https://github.com/your-org/aigc-service-hub/issues
- **讨论社区**: https://github.com/your-org/aigc-service-hub/discussions

---

<div align="center">
  <p>🌟 如果这个项目对您有帮助，请给我们一个 Star！</p>
  <p>Made with ❤️ by AIGC Service Hub Team</p>
</div>