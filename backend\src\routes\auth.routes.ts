import { Router } from 'express';
import { AuthController } from '@/controllers/auth.controller';
import authMiddleware from '@/middlewares/auth.middleware';

const router = Router();
const authController = new AuthController();

// 公开路由
router.post('/register', authController.register.bind(authController));
router.post('/login', authController.login.bind(authController));
router.post('/refresh', authController.refreshToken.bind(authController));
router.post('/request-password-reset', authController.requestPasswordReset.bind(authController));
router.post('/reset-password', authController.resetPassword.bind(authController));
router.get('/verify-email', authController.verifyEmail.bind(authController));
router.post('/resend-verification', authController.resendVerification.bind(authController));
router.get('/check-email', authController.checkEmailAvailability.bind(authController));

// OAuth路由
router.get('/google/callback', authController.googleCallback.bind(authController));
router.get('/github/callback', authController.githubCallback.bind(authController));

// 需要认证的路由
router.use(authMiddleware.authenticate);
router.get('/profile', authController.getProfile.bind(authController));
router.post('/change-password', authController.changePassword.bind(authController));
router.get('/permissions', authController.getUserPermissions.bind(authController));
router.post('/logout', authController.logout.bind(authController));

export default router;