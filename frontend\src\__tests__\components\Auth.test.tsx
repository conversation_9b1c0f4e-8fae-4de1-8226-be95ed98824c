import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import LoginForm from '../../components/Auth/LoginForm';
import RegisterForm from '../../components/Auth/RegisterForm';
import { theme } from '../../theme';

// Mock API calls
jest.mock('../../services/api', () => ({
  login: jest.fn(),
  register: jest.fn()
}));

const mockStore = configureStore({
  reducer: {
    auth: (state = { user: null, isAuthenticated: false, loading: false, error: null }, action) => {
      switch (action.type) {
        case 'auth/loginStart':
          return { ...state, loading: true, error: null };
        case 'auth/loginSuccess':
          return { ...state, loading: false, user: action.payload, isAuthenticated: true };
        case 'auth/loginFailure':
          return { ...state, loading: false, error: action.payload };
        default:
          return state;
      }
    }
  }
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('LoginForm Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders login form elements', () => {
    renderWithProviders(<LoginForm />);
    
    expect(screen.getByLabelText(/邮箱/)).toBeInTheDocument();
    expect(screen.getByLabelText(/密码/)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /登录/ })).toBeInTheDocument();
  });

  test('validates email format', async () => {
    const user = userEvent.setup();
    renderWithProviders(<LoginForm />);
    
    const emailInput = screen.getByLabelText(/邮箱/);
    const submitButton = screen.getByRole('button', { name: /登录/ });
    
    await user.type(emailInput, 'invalid-email');
    await user.click(submitButton);
    
    expect(screen.getByText(/请输入有效的邮箱地址/)).toBeInTheDocument();
  });

  test('validates password requirement', async () => {
    const user = userEvent.setup();
    renderWithProviders(<LoginForm />);
    
    const emailInput = screen.getByLabelText(/邮箱/);
    const submitButton = screen.getByRole('button', { name: /登录/ });
    
    await user.type(emailInput, '<EMAIL>');
    await user.click(submitButton);
    
    expect(screen.getByText(/请输入密码/)).toBeInTheDocument();
  });

  test('submits form with valid credentials', async () => {
    const user = userEvent.setup();
    const { login } = require('../../services/api');
    login.mockResolvedValue({
      token: 'jwt-token',
      user: { id: '1', email: '<EMAIL>', displayName: 'Test User' }
    });

    renderWithProviders(<LoginForm />);
    
    const emailInput = screen.getByLabelText(/邮箱/);
    const passwordInput = screen.getByLabelText(/密码/);
    const submitButton = screen.getByRole('button', { name: /登录/ });
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);
    
    expect(login).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123'
    });
  });

  test('displays error message on login failure', async () => {
    const user = userEvent.setup();
    const { login } = require('../../services/api');
    login.mockRejectedValue(new Error('Invalid credentials'));

    renderWithProviders(<LoginForm />);
    
    const emailInput = screen.getByLabelText(/邮箱/);
    const passwordInput = screen.getByLabelText(/密码/);
    const submitButton = screen.getByRole('button', { name: /登录/ });
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'wrongpassword');
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/登录失败/)).toBeInTheDocument();
    });
  });

  test('shows loading state during login', async () => {
    const user = userEvent.setup();
    const { login } = require('../../services/api');
    login.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));

    renderWithProviders(<LoginForm />);
    
    const emailInput = screen.getByLabelText(/邮箱/);
    const passwordInput = screen.getByLabelText(/密码/);
    const submitButton = screen.getByRole('button', { name: /登录/ });
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.click(submitButton);
    
    expect(screen.getByText(/登录中/)).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
  });

  test('provides link to register page', () => {
    renderWithProviders(<LoginForm />);
    
    const registerLink = screen.getByText(/还没有账户/);
    expect(registerLink).toBeInTheDocument();
    expect(registerLink.closest('a')).toHaveAttribute('href', '/register');
  });

  test('provides OAuth login options', () => {
    renderWithProviders(<LoginForm />);
    
    expect(screen.getByText(/使用Google登录/)).toBeInTheDocument();
    expect(screen.getByText(/使用GitHub登录/)).toBeInTheDocument();
  });
});

describe('RegisterForm Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders register form elements', () => {
    renderWithProviders(<RegisterForm />);
    
    expect(screen.getByLabelText(/邮箱/)).toBeInTheDocument();
    expect(screen.getByLabelText(/密码/)).toBeInTheDocument();
    expect(screen.getByLabelText(/确认密码/)).toBeInTheDocument();
    expect(screen.getByLabelText(/显示名称/)).toBeInTheDocument();
    expect(screen.getByLabelText(/账户类型/)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /注册/ })).toBeInTheDocument();
  });

  test('validates email format', async () => {
    const user = userEvent.setup();
    renderWithProviders(<RegisterForm />);
    
    const emailInput = screen.getByLabelText(/邮箱/);
    const submitButton = screen.getByRole('button', { name: /注册/ });
    
    await user.type(emailInput, 'invalid-email');
    await user.click(submitButton);
    
    expect(screen.getByText(/请输入有效的邮箱地址/)).toBeInTheDocument();
  });

  test('validates password strength', async () => {
    const user = userEvent.setup();
    renderWithProviders(<RegisterForm />);
    
    const passwordInput = screen.getByLabelText(/^密码$/);
    const submitButton = screen.getByRole('button', { name: /注册/ });
    
    await user.type(passwordInput, '123');
    await user.click(submitButton);
    
    expect(screen.getByText(/密码至少需要8个字符/)).toBeInTheDocument();
  });

  test('validates password confirmation', async () => {
    const user = userEvent.setup();
    renderWithProviders(<RegisterForm />);
    
    const passwordInput = screen.getByLabelText(/^密码$/);
    const confirmPasswordInput = screen.getByLabelText(/确认密码/);
    const submitButton = screen.getByRole('button', { name: /注册/ });
    
    await user.type(passwordInput, 'password123');
    await user.type(confirmPasswordInput, 'different123');
    await user.click(submitButton);
    
    expect(screen.getByText(/密码不匹配/)).toBeInTheDocument();
  });

  test('validates display name requirement', async () => {
    const user = userEvent.setup();
    renderWithProviders(<RegisterForm />);
    
    const submitButton = screen.getByRole('button', { name: /注册/ });
    await user.click(submitButton);
    
    expect(screen.getByText(/请输入显示名称/)).toBeInTheDocument();
  });

  test('allows selection of creator type', async () => {
    const user = userEvent.setup();
    renderWithProviders(<RegisterForm />);
    
    const creatorTypeSelect = screen.getByLabelText(/账户类型/);
    
    await user.click(creatorTypeSelect);
    expect(screen.getByText(/个人创作者/)).toBeInTheDocument();
    expect(screen.getByText(/企业创作者/)).toBeInTheDocument();
    
    await user.click(screen.getByText(/企业创作者/));
    expect(creatorTypeSelect).toHaveValue('ENTERPRISE');
  });

  test('submits form with valid data', async () => {
    const user = userEvent.setup();
    const { register } = require('../../services/api');
    register.mockResolvedValue({
      user: { id: '1', email: '<EMAIL>', displayName: 'Test User', creatorType: 'PERSONAL' }
    });

    renderWithProviders(<RegisterForm />);
    
    const emailInput = screen.getByLabelText(/邮箱/);
    const passwordInput = screen.getByLabelText(/^密码$/);
    const confirmPasswordInput = screen.getByLabelText(/确认密码/);
    const displayNameInput = screen.getByLabelText(/显示名称/);
    const creatorTypeSelect = screen.getByLabelText(/账户类型/);
    const submitButton = screen.getByRole('button', { name: /注册/ });
    
    await user.type(emailInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.type(confirmPasswordInput, 'password123');
    await user.type(displayNameInput, 'Test User');
    await user.click(creatorTypeSelect);
    await user.click(screen.getByText(/个人创作者/));
    await user.click(submitButton);
    
    expect(register).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123',
      displayName: 'Test User',
      creatorType: 'PERSONAL'
    });
  });

  test('displays error message on registration failure', async () => {
    const user = userEvent.setup();
    const { register } = require('../../services/api');
    register.mockRejectedValue(new Error('Email already exists'));

    renderWithProviders(<RegisterForm />);
    
    // Fill form with valid data
    await user.type(screen.getByLabelText(/邮箱/), '<EMAIL>');
    await user.type(screen.getByLabelText(/^密码$/), 'password123');
    await user.type(screen.getByLabelText(/确认密码/), 'password123');
    await user.type(screen.getByLabelText(/显示名称/), 'Test User');
    await user.click(screen.getByRole('button', { name: /注册/ }));
    
    await waitFor(() => {
      expect(screen.getByText(/注册失败/)).toBeInTheDocument();
    });
  });

  test('provides link to login page', () => {
    renderWithProviders(<RegisterForm />);
    
    const loginLink = screen.getByText(/已有账户/);
    expect(loginLink).toBeInTheDocument();
    expect(loginLink.closest('a')).toHaveAttribute('href', '/login');
  });
});
