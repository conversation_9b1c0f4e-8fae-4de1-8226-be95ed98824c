# ===========================================
# AIGC Service Hub MVP 1.0 - 生产环境配置
# ===========================================

# 应用环境配置
NODE_ENV=production
APP_NAME=AIGC Service Hub
API_VERSION=v1

# 服务端口配置
BACKEND_PORT=3000
FRONTEND_PORT=3001
HTTP_PORT=80
HTTPS_PORT=443

# 数据库配置
POSTGRES_DB=aigc_service_hub
POSTGRES_USER=postgres
POSTGRES_PASSWORD=CHANGE_THIS_SECURE_PASSWORD
POSTGRES_PORT=5432
DATABASE_POOL_SIZE=50

# Redis配置
REDIS_PORT=6379
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD
REDIS_DB=0

# JWT配置
JWT_SECRET=CHANGE_THIS_JWT_SECRET_KEY_MINIMUM_32_CHARACTERS_LONG
JWT_EXPIRES_IN=1d
REFRESH_TOKEN_EXPIRES_IN=7d

# AWS配置
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=CHANGE_THIS_AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY=CHANGE_THIS_AWS_SECRET_ACCESS_KEY
S3_PRIVATE_BUCKET=aigc-private-assets-prod
S3_PUBLIC_BUCKET=aigc-public-assets-prod

# PayPal配置
PAYPAL_CLIENT_ID=CHANGE_THIS_PAYPAL_CLIENT_ID
PAYPAL_CLIENT_SECRET=CHANGE_THIS_PAYPAL_CLIENT_SECRET
PAYPAL_SANDBOX=false
PAYPAL_WEBHOOK_ID=CHANGE_THIS_PAYPAL_WEBHOOK_ID

# OAuth配置
GOOGLE_CLIENT_ID=CHANGE_THIS_GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET=CHANGE_THIS_GOOGLE_CLIENT_SECRET
GITHUB_CLIENT_ID=CHANGE_THIS_GITHUB_CLIENT_ID
GITHUB_CLIENT_SECRET=CHANGE_THIS_GITHUB_CLIENT_SECRET

# 邮件配置
FROM_EMAIL=<EMAIL>
SMTP_HOST=email-smtp.us-west-2.amazonaws.com
SMTP_PORT=587
SMTP_USER=CHANGE_THIS_SMTP_USERNAME
SMTP_PASSWORD=CHANGE_THIS_SMTP_PASSWORD
AWS_SES_REGION=us-west-2

# 应用URL配置
FRONTEND_URL=https://your-domain.com
BACKEND_URL=https://api.your-domain.com
DOMAIN=your-domain.com

# React应用配置
REACT_APP_API_URL=https://api.your-domain.com/api/v1
REACT_APP_WS_URL=wss://api.your-domain.com
REACT_APP_PAYPAL_CLIENT_ID=CHANGE_THIS_PAYPAL_CLIENT_ID
REACT_APP_GOOGLE_CLIENT_ID=CHANGE_THIS_GOOGLE_CLIENT_ID
REACT_APP_GITHUB_CLIENT_ID=CHANGE_THIS_GITHUB_CLIENT_ID
REACT_APP_AWS_REGION=us-west-2
REACT_APP_S3_PUBLIC_BUCKET=aigc-public-assets-prod

# 文件上传配置
MAX_FILE_SIZE=32212254720
UPLOAD_URL_EXPIRES_IN=900
DOWNLOAD_URL_EXPIRES_IN=300

# 缓存配置
CACHE_DEFAULT_TTL=3600
CACHE_ASSETS_TTL=3600
CACHE_USER_PROFILE_TTL=1800

# 速率限制配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=warn
LOG_FILE=logs/app.log
LOG_MAX_SIZE=50m
LOG_MAX_FILES=10

# 监控配置
ENABLE_METRICS=true
METRICS_INTERVAL=60000

# 系统配置
POINTS_RATE=100
COMMISSION_INDIVIDUAL_BASE=5
COMMISSION_INDIVIDUAL_INCREMENT=5
COMMISSION_INDIVIDUAL_MAX=50
COMMISSION_ENTERPRISE_BASE=8
COMMISSION_ENTERPRISE_INCREMENT=8
COMMISSION_ENTERPRISE_MAX=56

# 财务配置
LEDGER_PENDING_DAYS=7
MIN_WITHDRAWAL_AMOUNT=10.00

# 工具服务端口配置（生产环境不暴露）
ADMINER_PORT=8080
REDIS_COMMANDER_PORT=8081
MAILHOG_WEB_PORT=8025
MAILHOG_SMTP_PORT=1025
DOCS_PORT=8082
WEBPACK_DEV_PORT=8083

# 测试环境端口配置（生产环境不使用）
POSTGRES_TEST_PORT=5433
REDIS_TEST_PORT=6380
BACKEND_TEST_PORT=3002
FRONTEND_TEST_PORT=3003
MAILHOG_TEST_WEB_PORT=8026
MAILHOG_TEST_SMTP_PORT=1026

# 监控服务配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3004
POSTGRES_EXPORTER_PORT=9187

# 备份配置
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# SSL配置
SSL_CERT_PATH=/etc/nginx/ssl/server.crt
SSL_KEY_PATH=/etc/nginx/ssl/server.key
SSL_DHPARAM_PATH=/etc/nginx/ssl/dhparam.pem

# 安全配置
ENABLE_HTTPS=true
FORCE_HTTPS=true
HSTS_MAX_AGE=31536000
SECURITY_HEADERS=true

# 性能配置
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=2048
POSTGRES_MAX_CONNECTIONS=200
REDIS_MAX_MEMORY=1gb

# 开发工具配置（生产环境禁用）
ENABLE_SWAGGER=false
ENABLE_CORS=false
ENABLE_MORGAN_LOGGING=false
ENABLE_DEBUG_MODE=false

# 部署配置
DEPLOY_ENVIRONMENT=production
DEPLOY_VERSION=1.0.0
DEPLOY_TIMESTAMP=
DEPLOY_COMMIT_SHA=

# 监控告警配置
ENABLE_ALERTS=true
ALERT_EMAIL=<EMAIL>
ALERT_SLACK_WEBHOOK=CHANGE_THIS_SLACK_WEBHOOK_URL
ALERT_THRESHOLD_CPU=80
ALERT_THRESHOLD_MEMORY=85
ALERT_THRESHOLD_DISK=90
ALERT_THRESHOLD_RESPONSE_TIME=5000

# 数据库连接池配置
DB_POOL_MIN=5
DB_POOL_MAX=50
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_CONNECTION_TIMEOUT=10000

# Redis连接配置
REDIS_POOL_MIN=5
REDIS_POOL_MAX=20
REDIS_CONNECT_TIMEOUT=5000
REDIS_COMMAND_TIMEOUT=3000

# 限流配置
RATE_LIMIT_REDIS_KEY_PREFIX=rate_limit:
RATE_LIMIT_SKIP_FAILED_REQUESTS=true
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false