"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const config_1 = __importDefault(require("./config"));
const connection_1 = require("./database/connection");
const logger_1 = require("./utils/logger");
const errors_1 = require("./utils/errors");
class Application {
    constructor() {
        this.app = (0, express_1.default)();
        this.port = config_1.default.app.port;
        this.initializeMiddlewares();
        this.initializeRoutes();
        this.initializeErrorHandling();
    }
    initializeMiddlewares() {
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
        }));
        this.app.use((0, cors_1.default)({
            origin: config_1.default.app.corsOrigins,
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        }));
        this.app.use((0, express_rate_limit_1.default)({
            windowMs: config_1.default.rateLimit.windowMs,
            max: config_1.default.rateLimit.maxRequests,
            message: {
                success: false,
                error: {
                    code: 'RATE_LIMIT_EXCEEDED',
                    message: 'Too many requests from this IP, please try again later.',
                },
            },
        }));
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        this.app.use((0, logger_1.createRequestLogger)());
        this.app.set('trust proxy', 1);
    }
    initializeRoutes() {
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: '1.0.0',
                environment: config_1.default.app.env,
            });
        });
        this.app.get('/', (req, res) => {
            res.json({
                message: 'AIGC Service Hub API',
                version: '1.0.0',
                documentation: '/api-docs',
                health: '/health',
            });
        });
    }
    async initializeApiRoutes() {
        try {
            logger_1.logger.info('Loading route modules...');
            const authRoutes = (await Promise.resolve().then(() => __importStar(require('./routes/auth.routes')))).default;
            logger_1.logger.info('Auth routes loaded');
            const userRoutes = (await Promise.resolve().then(() => __importStar(require('./routes/user.routes')))).default;
            logger_1.logger.info('User routes loaded');
            const assetRoutes = (await Promise.resolve().then(() => __importStar(require('./routes/asset.routes')))).default;
            logger_1.logger.info('Asset routes loaded');
            const transactionRoutes = (await Promise.resolve().then(() => __importStar(require('./routes/transaction.routes')))).default;
            logger_1.logger.info('Transaction routes loaded');
            const financeRoutes = (await Promise.resolve().then(() => __importStar(require('./routes/finance.routes')))).default;
            logger_1.logger.info('Finance routes loaded');
            const fileRoutes = (await Promise.resolve().then(() => __importStar(require('./routes/file.routes')))).default;
            logger_1.logger.info('File routes loaded');
            const adminRoutes = (await Promise.resolve().then(() => __importStar(require('./routes/admin.routes')))).default;
            logger_1.logger.info('Admin routes loaded');
            const apiRouter = express_1.default.Router();
            apiRouter.use('/auth', authRoutes);
            apiRouter.use('/users', userRoutes);
            apiRouter.use('/assets', assetRoutes);
            apiRouter.use('/transactions', transactionRoutes);
            apiRouter.use('/finance', financeRoutes);
            apiRouter.use('/files', fileRoutes);
            apiRouter.use('/admin', adminRoutes);
            apiRouter.get('/test', (req, res) => {
                res.json({ message: 'API routes are working!', timestamp: new Date().toISOString() });
            });
            this.app.use('/api', apiRouter);
            logger_1.logger.info('API router mounted to /api');
            this.app.use('*', (req, res) => {
                res.status(404).json({
                    success: false,
                    error: {
                        code: 'NOT_FOUND',
                        message: 'The requested resource was not found',
                    },
                });
            });
            logger_1.logger.info('API routes initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize API routes:', error);
            logger_1.logger.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
            throw error;
        }
    }
    initializeErrorHandling() {
        this.app.use((0, logger_1.createErrorLogger)());
        this.app.use(errors_1.errorHandler);
    }
    async start() {
        try {
            await (0, connection_1.initDatabase)();
            logger_1.logger.info('Database connected successfully');
            logger_1.logger.info('Starting API routes initialization...');
            await this.initializeApiRoutes();
            logger_1.logger.info('API routes initialization completed');
            this.app.listen(this.port, () => {
                logger_1.logger.info(`Server running on port ${this.port}`);
                logger_1.logger.info(`Environment: ${config_1.default.app.env}`);
                logger_1.logger.info(`Health check: http://localhost:${this.port}/health`);
                logger_1.logger.info(`API documentation: http://localhost:${this.port}/api-docs`);
            });
            this.setupGracefulShutdown();
        }
        catch (error) {
            logger_1.logger.error('Failed to start server:', error);
            process.exit(1);
        }
    }
    setupGracefulShutdown() {
        const gracefulShutdown = async (signal) => {
            logger_1.logger.info(`Received ${signal}, shutting down gracefully...`);
            process.exit(0);
        };
        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    }
}
const app = new Application();
if (require.main === module) {
    app.start().catch((error) => {
        logger_1.logger.error('Failed to start application:', error);
        process.exit(1);
    });
}
exports.default = app;
//# sourceMappingURL=main.js.map