"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const user_service_1 = require("../services/user.service");
const errors_1 = require("../utils/errors");
const errors_2 = require("../utils/errors");
class UserController {
    constructor() {
        this.getProfile = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const user = await this.userService.getUserById(req.user.id);
            res.json({
                success: true,
                data: { user },
                message: 'User profile retrieved successfully',
            });
        });
        this.updateProfile = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const { displayName } = req.body;
            const updates = {};
            if (displayName !== undefined) {
                updates.displayName = displayName;
            }
            const updatedUser = await this.userService.updateUserProfile(req.user.id, updates);
            res.json({
                success: true,
                data: { user: updatedUser },
                message: 'User profile updated successfully',
            });
        });
        this.getBalance = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const balance = await this.userService.getUserBalance(req.user.id);
            res.json({
                success: true,
                data: balance,
                message: 'User balance retrieved successfully',
            });
        });
        this.getEarningsStats = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const stats = await this.userService.getUserEarningsStats(req.user.id);
            res.json({
                success: true,
                data: stats,
                message: 'User earnings stats retrieved successfully',
            });
        });
        this.getPurchaseHistory = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const query = {
                page: parseInt(req.query.page) || 1,
                limit: parseInt(req.query.limit) || 20,
            };
            const result = await this.userService.getUserPurchaseHistory(req.user.id, query);
            res.json({
                success: true,
                data: result,
                message: 'Purchase history retrieved successfully',
            });
        });
        this.getSalesHistory = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const query = {
                page: parseInt(req.query.page) || 1,
                limit: parseInt(req.query.limit) || 20,
            };
            const result = await this.userService.getUserSalesHistory(req.user.id, query);
            res.json({
                success: true,
                data: result,
                message: 'Sales history retrieved successfully',
            });
        });
        this.getUserList = (0, errors_2.asyncHandler)(async (req, res) => {
            const query = {
                page: parseInt(req.query.page) || 1,
                limit: parseInt(req.query.limit) || 20,
                role: req.query.role,
                isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
            };
            const result = await this.userService.getUserList(query);
            res.json({
                success: true,
                data: result,
                message: 'User list retrieved successfully',
            });
        });
        this.getUserById = (0, errors_2.asyncHandler)(async (req, res) => {
            const userId = parseInt(req.params.id);
            if (isNaN(userId)) {
                throw new errors_1.ValidationError('Invalid user ID');
            }
            const user = await this.userService.getUserById(userId);
            res.json({
                success: true,
                data: { user },
                message: 'User details retrieved successfully',
            });
        });
        this.updateUserStatus = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const userId = parseInt(req.params.id);
            const { isActive } = req.body;
            if (isNaN(userId)) {
                throw new errors_1.ValidationError('Invalid user ID');
            }
            if (typeof isActive !== 'boolean') {
                throw new errors_1.ValidationError('isActive must be a boolean');
            }
            await this.userService.updateUserStatus(userId, isActive, req.user.id);
            res.json({
                success: true,
                message: 'User status updated successfully',
            });
        });
        this.deleteUser = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const userId = parseInt(req.params.id);
            if (isNaN(userId)) {
                throw new errors_1.ValidationError('Invalid user ID');
            }
            await this.userService.deleteUser(userId, req.user.id);
            res.json({
                success: true,
                message: 'User deleted successfully',
            });
        });
        this.userService = new user_service_1.UserService();
    }
}
exports.UserController = UserController;
exports.default = UserController;
//# sourceMappingURL=user.controller.js.map