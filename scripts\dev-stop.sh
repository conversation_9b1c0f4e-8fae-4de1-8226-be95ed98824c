#!/bin/bash

# ===========================================
# AIGC Service Hub MVP 1.0 - 开发环境停止脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AIGC Service Hub MVP 1.0 - 开发环境停止脚本

使用方法: $0 [OPTIONS]

OPTIONS:
    -h, --help          显示帮助信息
    -v, --verbose       详细输出
    -f, --force         强制停止所有容器
    -r, --remove        停止并删除容器
    -c, --clean         清理数据卷
    -i, --images        删除相关镜像
    -a, --all           停止所有Docker容器
    --db-only           仅停止数据库服务
    --api-only          仅停止API服务
    --web-only          仅停止前端服务
    --tools-only        仅停止开发工具

示例:
    $0                  # 停止开发环境
    $0 -r               # 停止并删除容器
    $0 -c               # 清理数据卷
    $0 --db-only        # 仅停止数据库

EOF
}

# 停止特定服务
stop_service() {
    local service=$1
    log_info "停止服务: $service"
    
    if docker-compose -f docker-compose.dev.yml ps | grep -q "$service"; then
        docker-compose -f docker-compose.dev.yml stop "$service"
        log_success "服务 $service 已停止"
    else
        log_warning "服务 $service 未运行"
    fi
}

# 停止数据库服务
stop_database() {
    log_info "停止数据库服务..."
    
    stop_service "postgres"
    stop_service "redis"
    
    log_success "数据库服务停止完成"
}

# 停止API服务
stop_api() {
    log_info "停止API服务..."
    
    stop_service "backend"
    
    log_success "API服务停止完成"
}

# 停止前端服务
stop_frontend() {
    log_info "停止前端服务..."
    
    stop_service "frontend"
    
    log_success "前端服务停止完成"
}

# 停止开发工具
stop_tools() {
    log_info "停止开发工具..."
    
    tools=(
        "adminer"
        "redis-commander"
        "mailhog"
        "docs"
        "webpack-dev-server"
    )
    
    for tool in "${tools[@]}"; do
        stop_service "$tool"
    done
    
    log_success "开发工具停止完成"
}

# 停止所有服务
stop_all_services() {
    log_info "停止所有开发环境服务..."
    
    if [ "$FORCE_STOP" = true ]; then
        docker-compose -f docker-compose.dev.yml kill
        log_warning "强制停止所有容器"
    else
        docker-compose -f docker-compose.dev.yml stop
        log_info "优雅停止所有容器"
    fi
    
    log_success "所有服务停止完成"
}

# 删除容器
remove_containers() {
    if [ "$REMOVE_CONTAINERS" = true ]; then
        log_info "删除开发环境容器..."
        
        docker-compose -f docker-compose.dev.yml down --remove-orphans
        
        log_success "容器删除完成"
    fi
}

# 清理数据卷
clean_volumes() {
    if [ "$CLEAN_VOLUMES" = true ]; then
        log_warning "清理数据卷..."
        
        read -p "确定要清理数据卷吗？这将删除所有数据 (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose -f docker-compose.dev.yml down -v
            
            # 清理本地数据目录
            rm -rf database/postgres-data/* 2>/dev/null || true
            rm -rf database/redis-data/* 2>/dev/null || true
            rm -rf backend/logs/* 2>/dev/null || true
            rm -rf nginx/logs/* 2>/dev/null || true
            
            log_success "数据卷清理完成"
        else
            log_info "跳过数据卷清理"
        fi
    fi
}

# 删除镜像
remove_images() {
    if [ "$REMOVE_IMAGES" = true ]; then
        log_info "删除开发环境镜像..."
        
        # 获取相关镜像
        images=$(docker images --format "table {{.Repository}}:{{.Tag}}" | grep -E "(aigc|postgres|redis|nginx|adminer|mailhog)" | grep -v "REPOSITORY" || true)
        
        if [ -n "$images" ]; then
            echo "$images" | while read image; do
                if [ -n "$image" ]; then
                    docker rmi "$image" 2>/dev/null || true
                    log_info "删除镜像: $image"
                fi
            done
            
            log_success "镜像删除完成"
        else
            log_info "没有找到相关镜像"
        fi
    fi
}

# 停止所有Docker容器
stop_all_containers() {
    if [ "$STOP_ALL" = true ]; then
        log_warning "停止所有Docker容器..."
        
        read -p "确定要停止所有Docker容器吗？ (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker stop $(docker ps -aq) 2>/dev/null || true
            log_success "所有容器停止完成"
        else
            log_info "跳过停止所有容器"
        fi
    fi
}

# 显示状态
show_status() {
    log_info "当前容器状态:"
    
    # 显示开发环境相关容器
    docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(aigc|postgres|redis|nginx|adminer|mailhog)" | head -20 || true
    
    echo
    log_info "Docker系统信息:"
    docker system df
}

# 清理系统
cleanup_system() {
    log_info "清理Docker系统..."
    
    # 清理未使用的资源
    docker system prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    # 清理未使用的卷
    if [ "$CLEAN_VOLUMES" = true ]; then
        docker volume prune -f
    fi
    
    log_success "系统清理完成"
}

# 保存日志
save_logs() {
    log_info "保存容器日志..."
    
    # 创建日志目录
    LOGS_DIR="scripts/logs/stop-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$LOGS_DIR"
    
    # 保存各服务日志
    services=(
        "postgres"
        "redis"
        "backend"
        "frontend"
        "adminer"
        "mailhog"
    )
    
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose.dev.yml ps | grep -q "$service"; then
            docker-compose -f docker-compose.dev.yml logs "$service" > "$LOGS_DIR/${service}.log" 2>&1 || true
            log_info "保存 $service 日志"
        fi
    done
    
    log_success "日志保存完成: $LOGS_DIR"
}

# 显示帮助信息
show_next_steps() {
    log_info "后续操作建议:"
    echo "  重新启动开发环境: ./scripts/dev-start.sh"
    echo "  重置开发环境: ./scripts/dev-reset.sh"
    echo "  查看容器状态: docker ps -a"
    echo "  查看系统资源: docker system df"
    echo "  清理系统资源: docker system prune"
}

# 主函数
main() {
    # 默认参数
    VERBOSE=false
    FORCE_STOP=false
    REMOVE_CONTAINERS=false
    CLEAN_VOLUMES=false
    REMOVE_IMAGES=false
    STOP_ALL=false
    DB_ONLY=false
    API_ONLY=false
    WEB_ONLY=false
    TOOLS_ONLY=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--force)
                FORCE_STOP=true
                shift
                ;;
            -r|--remove)
                REMOVE_CONTAINERS=true
                shift
                ;;
            -c|--clean)
                CLEAN_VOLUMES=true
                shift
                ;;
            -i|--images)
                REMOVE_IMAGES=true
                shift
                ;;
            -a|--all)
                STOP_ALL=true
                shift
                ;;
            --db-only)
                DB_ONLY=true
                shift
                ;;
            --api-only)
                API_ONLY=true
                shift
                ;;
            --web-only)
                WEB_ONLY=true
                shift
                ;;
            --tools-only)
                TOOLS_ONLY=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置详细输出
    if [ "$VERBOSE" = true ]; then
        set -x
    fi
    
    log_info "停止 AIGC Service Hub MVP 1.0 开发环境"
    
    # 检查配置文件
    if [ ! -f "docker-compose.dev.yml" ]; then
        log_error "开发环境配置文件不存在: docker-compose.dev.yml"
        exit 1
    fi
    
    # 保存日志
    save_logs
    
    # 根据参数停止不同服务
    if [ "$DB_ONLY" = true ]; then
        stop_database
    elif [ "$API_ONLY" = true ]; then
        stop_api
    elif [ "$WEB_ONLY" = true ]; then
        stop_frontend
    elif [ "$TOOLS_ONLY" = true ]; then
        stop_tools
    else
        # 停止所有服务
        stop_all_services
    fi
    
    # 删除容器
    remove_containers
    
    # 清理数据卷
    clean_volumes
    
    # 删除镜像
    remove_images
    
    # 停止所有容器
    stop_all_containers
    
    # 清理系统
    cleanup_system
    
    # 显示状态
    show_status
    
    # 显示后续步骤
    show_next_steps
    
    log_success "开发环境停止完成！"
}

# 执行主函数
main "$@"