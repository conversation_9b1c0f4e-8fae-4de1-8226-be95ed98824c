version: '3.8'

services:
  # PostgreSQL 数据库服务 - 生产环境优化
  postgres:
    image: postgres:15-alpine
    container_name: aigc-postgres-prod
    restart: always
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-aigc_service_hub}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: --encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/init.sql:/docker-entrypoint-initdb.d/02-init.sql:ro
      - ./database/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./database/backups:/backups
    command: >
      postgres 
      -c config_file=/etc/postgresql/postgresql.conf
      -c max_connections=200
      -c shared_buffers=512MB
      -c effective_cache_size=2GB
      -c work_mem=8MB
      -c maintenance_work_mem=128MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-aigc_service_hub}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  # Redis 缓存服务 - 生产环境优化
  redis:
    image: redis:7-alpine
    container_name: aigc-redis-prod
    restart: always
    volumes:
      - redis_data:/data
      - ./database/redis.conf:/etc/redis/redis.conf:ro
    command: redis-server /etc/redis/redis.conf --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  # 后端 API 服务 - 生产环境优化
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: aigc-backend-prod
    restart: always
    environment:
      NODE_ENV: production
      PORT: 3000
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-aigc_service_hub}
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      AWS_REGION: ${AWS_REGION}
      S3_PRIVATE_BUCKET: ${S3_PRIVATE_BUCKET}
      S3_PUBLIC_BUCKET: ${S3_PUBLIC_BUCKET}
      PAYPAL_CLIENT_ID: ${PAYPAL_CLIENT_ID}
      PAYPAL_CLIENT_SECRET: ${PAYPAL_CLIENT_SECRET}
      PAYPAL_SANDBOX: ${PAYPAL_SANDBOX:-false}
      FROM_EMAIL: ${FROM_EMAIL}
      SMTP_HOST: ${SMTP_HOST}
      SMTP_PORT: ${SMTP_PORT}
      SMTP_USER: ${SMTP_USER}
      SMTP_PASSWORD: ${SMTP_PASSWORD}
      FRONTEND_URL: ${FRONTEND_URL}
      BACKEND_URL: ${BACKEND_URL}
      LOG_LEVEL: warn
      ENABLE_METRICS: true
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  # 前端应用服务 - 生产环境优化
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
      args:
        REACT_APP_API_URL: ${REACT_APP_API_URL}
        REACT_APP_WS_URL: ${REACT_APP_WS_URL}
        REACT_APP_PAYPAL_CLIENT_ID: ${PAYPAL_CLIENT_ID}
        REACT_APP_GOOGLE_CLIENT_ID: ${GOOGLE_CLIENT_ID}
        REACT_APP_GITHUB_CLIENT_ID: ${GITHUB_CLIENT_ID}
        REACT_APP_AWS_REGION: ${AWS_REGION}
        REACT_APP_S3_PUBLIC_BUCKET: ${S3_PUBLIC_BUCKET}
    container_name: aigc-frontend-prod
    restart: always
    depends_on:
      - backend
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

  # Nginx 反向代理 - 生产环境优化
  nginx:
    image: nginx:alpine
    container_name: aigc-nginx-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
      - ./frontend/build:/usr/share/nginx/html:ro
    depends_on:
      - backend
      - frontend
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  # 数据库备份服务
  postgres-backup:
    image: postgres:15-alpine
    container_name: aigc-postgres-backup-prod
    restart: always
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-aigc_service_hub}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_HOST: postgres
      BACKUP_SCHEDULE: "0 2 * * *"
      BACKUP_RETENTION_DAYS: 30
    volumes:
      - ./database/backups:/backups
      - ./database/backup-script.sh:/backup-script.sh:ro
    command: >
      sh -c '
        apk add --no-cache dcron
        echo "0 2 * * * /backup-script.sh" > /etc/crontabs/root
        crond -f
      '
    networks:
      - aigc-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: aigc-prometheus-prod
    restart: always
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    depends_on:
      - backend
    networks:
      - aigc-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # 日志收集服务
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: aigc-fluentd-prod
    restart: always
    volumes:
      - ./logging/fluentd.conf:/fluentd/etc/fluent.conf:ro
      - ./nginx/logs:/var/log/nginx:ro
      - ./backend/logs:/var/log/backend:ro
    ports:
      - "24224:24224"
    depends_on:
      - backend
      - nginx
    networks:
      - aigc-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local

networks:
  aigc-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 生产环境部署说明:
# 1. 确保所有环境变量已正确设置
# 2. 配置SSL证书用于HTTPS
# 3. 设置合适的资源限制
# 4. 启用监控和日志收集
# 5. 配置定期备份
# 6. 部署命令: docker-compose -f docker-compose.prod.yml up -d