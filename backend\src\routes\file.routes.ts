import { Router } from 'express';
import { FileController } from '@/controllers/file.controller';
import authMiddleware from '@/middlewares/auth.middleware';
import multer from 'multer';
import path from 'path';

const router = Router();
const fileController = new FileController();

// 配置文件上传中间件
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
  },
  fileFilter: (req, file, cb) => {
    // 允许的文件类型
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|txt|zip|rar/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});

// 文件上传
router.post('/upload', authMiddleware.authenticate, upload.single('file'), fileController.uploadFile);

// 批量文件上传
router.post('/upload-multiple', authMiddleware.authenticate, upload.array('files', 10), fileController.uploadMultipleFiles);

// 获取文件列表
router.get('/', authMiddleware.authenticate, fileController.getFiles);

// 获取用户的文件列表
router.get('/user/:userId', authMiddleware.authenticate, fileController.getUserFiles);

// 下载文件
router.get('/download/:fileId', authMiddleware.authenticate, fileController.downloadFile);

// 获取文件信息
router.get('/:fileId', authMiddleware.authenticate, fileController.getFileInfo);

// 删除文件
router.delete('/:fileId', authMiddleware.authenticate, fileController.deleteFile);

// 更新文件信息
router.put('/:fileId', authMiddleware.authenticate, fileController.updateFile);

// 获取文件缩略图
router.get('/:fileId/thumbnail', authMiddleware.authenticate, fileController.getThumbnail);

export default router;