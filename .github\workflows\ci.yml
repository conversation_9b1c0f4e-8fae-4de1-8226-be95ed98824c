name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_DB: aigc_hub_test
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: |
          backend/package-lock.json
          frontend/package-lock.json

    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci

    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Run backend linting
      working-directory: ./backend
      run: npm run lint

    - name: Run frontend linting
      working-directory: ./frontend
      run: npm run lint

    - name: Run backend tests
      working-directory: ./backend
      run: npm test
      env:
        NODE_ENV: test
        DATABASE_URL: postgres://test_user:test_password@localhost:5432/aigc_hub_test
        REDIS_URL: redis://localhost:6379

    - name: Run frontend tests
      working-directory: ./frontend
      run: npm test -- --coverage --watchAll=false

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        directory: ./backend/coverage
        flags: backend
        name: backend-coverage

    - name: Upload frontend coverage
      uses: codecov/codecov-action@v3
      with:
        directory: ./frontend/coverage
        flags: frontend
        name: frontend-coverage

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run npm audit for backend
      working-directory: ./backend
      run: npm audit --audit-level=high

    - name: Run npm audit for frontend
      working-directory: ./frontend
      run: npm audit --audit-level=high

  build:
    name: Build Images
    runs-on: ubuntu-latest
    needs: [test, security-scan]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix=sha-{{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}-backend:${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: true
        tags: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}-frontend:${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push nginx image
      uses: docker/build-push-action@v5
      with:
        context: ./nginx
        push: true
        tags: ${{ env.DOCKER_REGISTRY }}/${{ env.IMAGE_NAME }}-nginx:${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  e2e-test:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: build
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: e2e/package-lock.json

    - name: Install E2E dependencies
      working-directory: ./e2e
      run: npm ci

    - name: Start application
      run: |
        cp .env.test .env
        docker-compose -f docker-compose.test.yml up -d
        sleep 30

    - name: Wait for services
      run: |
        chmod +x scripts/wait-for-it.sh
        ./scripts/wait-for-it.sh localhost:8080 --timeout=60
        ./scripts/wait-for-it.sh localhost:5432 --timeout=60

    - name: Run E2E tests
      working-directory: ./e2e
      run: npm test

    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: e2e/test-results/

    - name: Upload E2E screenshots
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: e2e-screenshots
        path: e2e/screenshots/

    - name: Cleanup
      if: always()
      run: docker-compose -f docker-compose.test.yml down -v

  performance-test:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: build
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup K6
      uses: grafana/k6-action@v0.3.0
      with:
        filename: performance/load-test.js

    - name: Start application
      run: |
        cp .env.test .env
        docker-compose -f docker-compose.test.yml up -d
        sleep 30

    - name: Wait for services
      run: |
        chmod +x scripts/wait-for-it.sh
        ./scripts/wait-for-it.sh localhost:8080 --timeout=60

    - name: Run performance tests
      run: |
        k6 run performance/load-test.js
        k6 run performance/stress-test.js

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-test-results
        path: performance/results/

    - name: Cleanup
      if: always()
      run: docker-compose -f docker-compose.test.yml down -v

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, security-scan, build, e2e-test]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.STAGING_SSH_KEY }}

    - name: Deploy to staging
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }} "
          cd /opt/aigc-hub &&
          git pull origin develop &&
          chmod +x scripts/deploy.sh &&
          ./scripts/deploy.sh --env staging --no-backup
        "

    - name: Run health checks
      run: |
        sleep 30
        curl -f https://staging.aigc-hub.com/health || exit 1

    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, security-scan, build, e2e-test, performance-test]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.PRODUCTION_SSH_KEY }}

    - name: Create backup
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "
          cd /opt/aigc-hub &&
          chmod +x scripts/backup.sh &&
          ./scripts/backup.sh --type full --compress
        "

    - name: Deploy to production
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "
          cd /opt/aigc-hub &&
          git pull origin main &&
          chmod +x scripts/deploy.sh &&
          ./scripts/deploy.sh --env production --backup
        "

    - name: Run health checks
      run: |
        sleep 60
        curl -f https://aigc-hub.com/health || exit 1
        curl -f https://aigc-hub.com/api/health || exit 1

    - name: Run smoke tests
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "
          cd /opt/aigc-hub &&
          docker-compose -f docker-compose.prod.yml exec -T backend npm run test:smoke
        "

    - name: Notify deployment
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow