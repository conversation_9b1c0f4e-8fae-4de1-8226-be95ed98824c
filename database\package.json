{"name": "aigc-service-hub-database", "version": "1.0.0", "description": "AIGC Service Hub MVP 1.0 数据库实现", "main": "config.js", "scripts": {"db:init": "node migrate.js init", "db:migrate": "node migrate.js up", "db:rollback": "node migrate.js rollback", "db:status": "node migrate.js status", "db:create": "node migrate.js create", "db:validate": "node migrate.js validate", "db:backup": "./backup-script.sh", "db:restore": "./backup-script.sh --restore", "db:test": "./backup-script.sh --test", "db:cleanup": "./backup-script.sh --cleanup", "docker:up": "docker-compose -f docker-compose.db.yml up -d", "docker:down": "docker-compose -f docker-compose.db.yml down", "docker:logs": "docker-compose -f docker-compose.db.yml logs -f postgres", "docker:clean": "docker-compose -f docker-compose.db.yml down -v", "health": "node -e \"require('./config').healthCheck().then(console.log)\"", "stats": "node -e \"require('./config').getStats().then ? require('./config').getStats().then(console.log) : console.log(require('./config').getStats())\"", "setup": "npm run db:init && npm run db:migrate", "dev": "npm run docker:up && sleep 10 && npm run setup"}, "dependencies": {"pg": "^8.11.3", "ioredis": "^5.3.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}, "keywords": ["database", "postgresql", "redis", "migration", "aigc", "service-hub"], "author": "AIGC Service Hub Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/aigc-service-hub.git"}, "bugs": {"url": "https://github.com/your-org/aigc-service-hub/issues"}, "homepage": "https://github.com/your-org/aigc-service-hub#readme"}