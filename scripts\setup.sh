#!/bin/bash

# ===========================================
# AIGC Service Hub MVP 1.0 - 环境初始化脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AIGC Service Hub MVP 1.0 - 环境初始化脚本

使用方法: $0 [OPTIONS] [ENVIRONMENT]

ENVIRONMENT:
    dev         初始化开发环境 (默认)
    test        初始化测试环境
    prod        初始化生产环境

OPTIONS:
    -h, --help          显示帮助信息
    -v, --verbose       详细输出
    -f, --force         强制重新初始化
    -s, --skip-deps     跳过依赖安装
    -d, --skip-docker   跳过Docker检查
    -e, --skip-env      跳过环境配置
    -c, --skip-certs    跳过SSL证书生成
    -b, --skip-build    跳过构建镜像
    --install-deps      安装系统依赖
    --generate-certs    生成SSL证书
    --init-db           初始化数据库
    --seed-data         添加种子数据

示例:
    $0                      # 初始化开发环境
    $0 prod                 # 初始化生产环境
    $0 dev -f               # 强制重新初始化开发环境
    $0 prod --install-deps  # 安装依赖并初始化生产环境

EOF
}

# 检查操作系统
check_os() {
    log_info "检查操作系统..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        DISTRO=$(lsb_release -si 2>/dev/null || echo "Unknown")
        log_info "操作系统: Linux ($DISTRO)"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        log_info "操作系统: macOS"
    elif [[ "$OSTYPE" == "cygwin" || "$OSTYPE" == "msys" ]]; then
        OS="windows"
        log_info "操作系统: Windows"
    else
        log_warning "未知操作系统: $OSTYPE"
        OS="unknown"
    fi
}

# 安装系统依赖
install_dependencies() {
    if [ "$INSTALL_DEPS" = true ]; then
        log_info "安装系统依赖..."
        
        case $OS in
            linux)
                # 检查包管理器
                if command -v apt-get &> /dev/null; then
                    PKG_MANAGER="apt-get"
                elif command -v yum &> /dev/null; then
                    PKG_MANAGER="yum"
                elif command -v pacman &> /dev/null; then
                    PKG_MANAGER="pacman"
                else
                    log_error "未找到支持的包管理器"
                    exit 1
                fi
                
                # 安装依赖
                if [ "$PKG_MANAGER" = "apt-get" ]; then
                    sudo apt-get update
                    sudo apt-get install -y curl wget git unzip jq openssl
                elif [ "$PKG_MANAGER" = "yum" ]; then
                    sudo yum install -y curl wget git unzip jq openssl
                elif [ "$PKG_MANAGER" = "pacman" ]; then
                    sudo pacman -S --noconfirm curl wget git unzip jq openssl
                fi
                ;;
            macos)
                # 检查Homebrew
                if ! command -v brew &> /dev/null; then
                    log_info "安装Homebrew..."
                    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
                fi
                
                # 安装依赖
                brew install curl wget git unzip jq openssl
                ;;
            windows)
                log_warning "Windows系统请手动安装以下依赖:"
                log_warning "- Git for Windows"
                log_warning "- Docker Desktop"
                log_warning "- Node.js"
                ;;
        esac
        
        log_success "系统依赖安装完成"
    fi
}

# 检查Docker
check_docker() {
    if [ "$SKIP_DOCKER" = false ]; then
        log_info "检查Docker..."
        
        # 检查Docker是否已安装
        if ! command -v docker &> /dev/null; then
            log_error "Docker未安装"
            
            case $OS in
                linux)
                    log_info "安装Docker..."
                    curl -fsSL https://get.docker.com -o get-docker.sh
                    sh get-docker.sh
                    sudo usermod -aG docker $USER
                    rm get-docker.sh
                    log_warning "请重新登录以应用Docker组权限"
                    ;;
                macos)
                    log_error "请从 https://docs.docker.com/desktop/mac/install/ 安装Docker Desktop"
                    exit 1
                    ;;
                windows)
                    log_error "请从 https://docs.docker.com/desktop/windows/install/ 安装Docker Desktop"
                    exit 1
                    ;;
            esac
        fi
        
        # 检查Docker Compose
        if ! command -v docker-compose &> /dev/null; then
            log_info "安装Docker Compose..."
            
            # 获取最新版本
            COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | jq -r .tag_name)
            
            case $OS in
                linux)
                    sudo curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
                    sudo chmod +x /usr/local/bin/docker-compose
                    ;;
                macos)
                    brew install docker-compose
                    ;;
            esac
        fi
        
        # 检查Docker服务
        if ! docker info &> /dev/null; then
            log_error "Docker服务未运行，请启动Docker服务"
            exit 1
        fi
        
        log_success "Docker检查完成"
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    directories=(
        "database/backups"
        "database/postgres-data"
        "database/redis-data"
        "backend/logs"
        "backend/uploads"
        "backend/coverage"
        "frontend/build"
        "frontend/coverage"
        "frontend/test-results"
        "frontend/playwright-report"
        "nginx/logs"
        "nginx/ssl"
        "monitoring/data"
        "monitoring/prometheus"
        "monitoring/grafana"
        "scripts/logs"
        "docs/api"
        "docs/deployment"
        "brand-assets/logos"
        "brand-assets/colors"
        "brand-assets/fonts"
        "brand-assets/images"
        "performance-tests/results"
        "security-tests/results"
        "logging/fluentd"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    log_success "目录结构创建完成"
}

# 环境配置
setup_environment() {
    if [ "$SKIP_ENV" = false ]; then
        log_info "设置环境配置..."
        
        # 检查环境配置文件
        if [ ! -f ".env.$ENVIRONMENT" ]; then
            log_error "环境配置文件 .env.$ENVIRONMENT 不存在"
            exit 1
        fi
        
        # 复制环境配置文件
        if [ ! -f ".env" ] || [ "$FORCE_INIT" = true ]; then
            cp ".env.$ENVIRONMENT" ".env"
            log_info "复制环境配置: .env.$ENVIRONMENT -> .env"
        fi
        
        # 生成随机密钥
        if grep -q "CHANGE_THIS" ".env" || [ "$FORCE_INIT" = true ]; then
            log_info "生成随机密钥..."
            
            # 生成JWT密钥
            JWT_SECRET=$(openssl rand -base64 32)
            sed -i.bak "s/CHANGE_THIS_JWT_SECRET_KEY_MINIMUM_32_CHARACTERS_LONG/$JWT_SECRET/" ".env"
            
            # 生成数据库密码
            DB_PASSWORD=$(openssl rand -base64 16)
            sed -i.bak "s/CHANGE_THIS_SECURE_PASSWORD/$DB_PASSWORD/" ".env"
            
            # 生成Redis密码
            REDIS_PASSWORD=$(openssl rand -base64 16)
            sed -i.bak "s/CHANGE_THIS_REDIS_PASSWORD/$REDIS_PASSWORD/" ".env"
            
            # 清理备份文件
            rm -f ".env.bak"
            
            log_success "随机密钥生成完成"
        fi
        
        log_success "环境配置完成"
    fi
}

# 生成SSL证书
generate_ssl_certs() {
    if [ "$SKIP_CERTS" = false ] && [ "$GENERATE_CERTS" = true ]; then
        log_info "生成SSL证书..."
        
        # 创建SSL目录
        mkdir -p nginx/ssl
        
        # 生成自签名证书
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/server.key \
            -out nginx/ssl/server.crt \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"
        
        # 生成DH参数
        openssl dhparam -out nginx/ssl/dhparam.pem 2048
        
        # 设置权限
        chmod 600 nginx/ssl/server.key
        chmod 644 nginx/ssl/server.crt
        chmod 644 nginx/ssl/dhparam.pem
        
        log_success "SSL证书生成完成"
    fi
}

# 初始化数据库
init_database() {
    if [ "$INIT_DB" = true ]; then
        log_info "初始化数据库..."
        
        # 启动数据库服务
        docker-compose up -d postgres redis
        
        # 等待数据库启动
        sleep 30
        
        # 运行数据库迁移
        docker-compose exec backend npm run db:migrate
        
        log_success "数据库初始化完成"
    fi
}

# 添加种子数据
seed_data() {
    if [ "$SEED_DATA" = true ]; then
        log_info "添加种子数据..."
        
        # 运行种子数据脚本
        docker-compose exec backend npm run db:seed
        
        log_success "种子数据添加完成"
    fi
}

# 构建镜像
build_images() {
    if [ "$SKIP_BUILD" = false ]; then
        log_info "构建Docker镜像..."
        
        # 确定Docker Compose文件
        case $ENVIRONMENT in
            dev)
                compose_file="docker-compose.dev.yml"
                ;;
            test)
                compose_file="docker-compose.test.yml"
                ;;
            prod)
                compose_file="docker-compose.prod.yml"
                ;;
            *)
                compose_file="docker-compose.yml"
                ;;
        esac
        
        # 构建镜像
        docker-compose -f "$compose_file" build
        
        log_success "Docker镜像构建完成"
    fi
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    # 检查Docker
    if command -v docker &> /dev/null; then
        log_success "Docker: $(docker --version)"
    else
        log_error "Docker未安装"
    fi
    
    # 检查Docker Compose
    if command -v docker-compose &> /dev/null; then
        log_success "Docker Compose: $(docker-compose --version)"
    else
        log_error "Docker Compose未安装"
    fi
    
    # 检查配置文件
    if [ -f ".env" ]; then
        log_success "环境配置文件: .env"
    else
        log_error "环境配置文件不存在"
    fi
    
    # 检查SSL证书
    if [ -f "nginx/ssl/server.crt" ]; then
        log_success "SSL证书: nginx/ssl/server.crt"
    else
        log_warning "SSL证书不存在"
    fi
    
    log_success "安装验证完成"
}

# 显示后续步骤
show_next_steps() {
    log_success "环境初始化完成！"
    
    echo
    echo "=================== 后续步骤 ==================="
    echo "1. 检查并修改 .env 文件中的配置"
    echo "2. 设置第三方服务凭证 (AWS, PayPal, etc.)"
    echo "3. 运行部署脚本:"
    echo "   ./scripts/deploy.sh $ENVIRONMENT"
    echo
    echo "4. 访问应用:"
    echo "   - 前端: http://localhost:3001"
    echo "   - 后端: http://localhost:3000"
    echo "   - 文档: http://localhost:3000/api/docs"
    echo
    echo "5. 开发工具:"
    echo "   - 数据库管理: http://localhost:8080"
    echo "   - Redis管理: http://localhost:8081"
    echo "   - 邮件测试: http://localhost:8025"
    echo "=============================================="
}

# 主函数
main() {
    # 默认参数
    ENVIRONMENT="dev"
    VERBOSE=false
    FORCE_INIT=false
    SKIP_DEPS=false
    SKIP_DOCKER=false
    SKIP_ENV=false
    SKIP_CERTS=false
    SKIP_BUILD=false
    INSTALL_DEPS=false
    GENERATE_CERTS=false
    INIT_DB=false
    SEED_DATA=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--force)
                FORCE_INIT=true
                shift
                ;;
            -s|--skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            -d|--skip-docker)
                SKIP_DOCKER=true
                shift
                ;;
            -e|--skip-env)
                SKIP_ENV=true
                shift
                ;;
            -c|--skip-certs)
                SKIP_CERTS=true
                shift
                ;;
            -b|--skip-build)
                SKIP_BUILD=true
                shift
                ;;
            --install-deps)
                INSTALL_DEPS=true
                shift
                ;;
            --generate-certs)
                GENERATE_CERTS=true
                shift
                ;;
            --init-db)
                INIT_DB=true
                shift
                ;;
            --seed-data)
                SEED_DATA=true
                shift
                ;;
            dev|test|prod)
                ENVIRONMENT=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置详细输出
    if [ "$VERBOSE" = true ]; then
        set -x
    fi
    
    log_info "开始初始化 AIGC Service Hub MVP 1.0"
    log_info "环境: $ENVIRONMENT"
    
    # 执行初始化步骤
    check_os
    install_dependencies
    check_docker
    create_directories
    setup_environment
    generate_ssl_certs
    build_images
    init_database
    seed_data
    verify_installation
    show_next_steps
    
    log_success "初始化完成！"
}

# 执行主函数
main "$@"