import { Request, Response, NextFunction } from 'express';
export declare class AdminController {
    private adminService;
    constructor();
    getUsers: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getUserById: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    updateUser: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    deleteUser: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    activateUser: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    deactivateUser: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getAssets: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getAssetById: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    updateAsset: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    deleteAsset: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    approveAsset: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    rejectAsset: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getTransactions: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getTransactionById: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    refundTransaction: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getWithdrawals: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getWithdrawalById: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    approveWithdrawal: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    rejectWithdrawal: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getOverviewStats: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getUserStats: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getAssetStats: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getTransactionStats: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getRevenueStats: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getSettings: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    updateSettings: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getSecurityLogs: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getErrorLogs: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getAuditLogs: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getSystemHealth: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getSystemMetrics: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getReports: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    resolveReport: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    bulkActivateUsers: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    bulkDeactivateUsers: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    bulkApproveAssets: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    bulkRejectAssets: (req: Request, res: Response, next: NextFunction) => Promise<void>;
}
//# sourceMappingURL=admin.controller.d.ts.map