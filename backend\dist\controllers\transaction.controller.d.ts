export declare class TransactionController {
    private transactionService;
    constructor();
    createPurchaseTransaction: (req: any, res: any, next: any) => void;
    confirmPurchase: (req: any, res: any, next: any) => void;
    cancelPurchase: (req: any, res: any, next: any) => void;
    getMyTransactions: (req: any, res: any, next: any) => void;
    getTransactionById: (req: any, res: any, next: any) => void;
    getTransactionStatus: (req: any, res: any, next: any) => void;
    getAllTransactions: (req: any, res: any, next: any) => void;
    updateTransactionStatus: (req: any, res: any, next: any) => void;
}
export default TransactionController;
//# sourceMappingURL=transaction.controller.d.ts.map