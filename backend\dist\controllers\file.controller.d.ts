import { Request, Response, NextFunction } from 'express';
export declare class FileController {
    private fileService;
    constructor();
    uploadFile: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    uploadMultipleFiles: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getFiles: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getUserFiles: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    downloadFile: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getFileInfo: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    deleteFile: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    updateFile: (req: Request, res: Response, next: NextFunction) => Promise<void>;
    getThumbnail: (req: Request, res: Response, next: NextFunction) => Promise<void>;
}
//# sourceMappingURL=file.controller.d.ts.map