"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionController = void 0;
const transaction_service_1 = require("../services/transaction.service");
const errors_1 = require("../utils/errors");
const errors_2 = require("../utils/errors");
class TransactionController {
    constructor() {
        this.createPurchaseTransaction = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const purchaseData = req.body;
            const transaction = await this.transactionService.createPurchaseTransaction(req.user.id, purchaseData);
            res.status(201).json({
                success: true,
                data: { transaction },
                message: 'Purchase transaction created successfully',
            });
        });
        this.confirmPurchase = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const { transactionId, paypalTransactionId } = req.body;
            if (!transactionId) {
                throw new errors_1.ValidationError('Transaction ID is required');
            }
            const transaction = await this.transactionService.confirmPurchase(transactionId, paypalTransactionId);
            res.json({
                success: true,
                data: { transaction },
                message: 'Purchase confirmed successfully',
            });
        });
        this.cancelPurchase = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const { transactionId } = req.body;
            if (!transactionId) {
                throw new errors_1.ValidationError('Transaction ID is required');
            }
            await this.transactionService.cancelTransaction(transactionId);
            res.json({
                success: true,
                message: 'Purchase cancelled successfully',
            });
        });
        this.getMyTransactions = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const query = {
                page: parseInt(req.query.page) || 1,
                limit: parseInt(req.query.limit) || 20,
                currency: req.query.currency,
                status: req.query.status,
                startDate: req.query.startDate ? new Date(req.query.startDate) : undefined,
                endDate: req.query.endDate ? new Date(req.query.endDate) : undefined,
            };
            const result = await this.transactionService.getUserTransactions(req.user.id, query);
            res.json({
                success: true,
                data: result,
                message: 'Transactions retrieved successfully',
            });
        });
        this.getTransactionById = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const transactionId = parseInt(req.params.id);
            if (isNaN(transactionId)) {
                throw new errors_1.ValidationError('Invalid transaction ID');
            }
            const transaction = await this.transactionService.getTransactionById(transactionId);
            res.json({
                success: true,
                data: { transaction },
                message: 'Transaction details retrieved successfully',
            });
        });
        this.getTransactionStatus = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const transactionId = parseInt(req.params.id);
            if (isNaN(transactionId)) {
                throw new errors_1.ValidationError('Invalid transaction ID');
            }
            const transaction = await this.transactionService.getTransactionById(transactionId);
            res.json({
                success: true,
                data: {
                    id: transaction.id,
                    status: transaction.status,
                    createdAt: transaction.createdAt,
                    completedAt: transaction.completedAt,
                },
                message: 'Transaction status retrieved successfully',
            });
        });
        this.getAllTransactions = (0, errors_2.asyncHandler)(async (req, res) => {
            const query = {
                page: parseInt(req.query.page) || 1,
                limit: parseInt(req.query.limit) || 20,
                currency: req.query.currency,
                status: req.query.status,
                startDate: req.query.startDate ? new Date(req.query.startDate) : undefined,
                endDate: req.query.endDate ? new Date(req.query.endDate) : undefined,
            };
            const result = await this.transactionService.getAllTransactions(query);
            res.json({
                success: true,
                data: result,
                message: 'All transactions retrieved successfully',
            });
        });
        this.updateTransactionStatus = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const transactionId = parseInt(req.params.id);
            const { status } = req.body;
            if (isNaN(transactionId)) {
                throw new errors_1.ValidationError('Invalid transaction ID');
            }
            if (!status) {
                throw new errors_1.ValidationError('Status is required');
            }
            await this.transactionService.updateTransactionStatus(transactionId, status);
            res.json({
                success: true,
                message: 'Transaction status updated successfully',
            });
        });
        this.transactionService = new transaction_service_1.TransactionService();
    }
}
exports.TransactionController = TransactionController;
exports.default = TransactionController;
//# sourceMappingURL=transaction.controller.js.map