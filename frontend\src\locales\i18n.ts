import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 基础翻译资源
const resources = {
  'zh-CN': {
    common: {
      loading: '加载中...',
      error: '错误',
      success: '成功',
      cancel: '取消',
      confirm: '确认',
      save: '保存',
      edit: '编辑',
      delete: '删除',
      add: '添加',
      search: '搜索',
      logout: '退出登录',
      login: '登录',
      register: '注册',
      username: '用户名',
      password: '密码',
      email: '邮箱',
      name: '姓名',
      status: '状态',
      action: '操作',
      submit: '提交',
      close: '关闭',
      download: '下载',
      upload: '上传',
      view: '查看',
      settings: '设置',
      dashboard: '仪表板',
      home: '首页',
      back: '返回',
      help: '帮助',
      language: '语言',
      theme: '主题',
      yes: '是',
      no: '否',
      ok: '确定',
      warning: '警告',
      info: '信息'
    },
    auth: {
      login: '登录',
      register: '注册',
      logout: '退出登录',
      forgot_password: '忘记密码',
      reset_password: '重置密码',
      change_password: '修改密码',
      username: '用户名',
      password: '密码',
      confirm_password: '确认密码',
      email: '邮箱',
      phone: '手机号',
      login_success: '登录成功',
      login_failed: '登录失败',
      register_success: '注册成功',
      register_failed: '注册失败',
      invalid_credentials: '用户名或密码错误'
    },
    dashboard: {
      title: '仪表板',
      welcome: '欢迎',
      overview: '概览',
      statistics: '统计',
      recent_activities: '最近活动',
      quick_actions: '快速操作',
      notifications: '通知',
      system_status: '系统状态'
    },
    assets: {
      title: '资产管理',
      list: '资产列表',
      create: '创建资产',
      edit: '编辑资产',
      delete: '删除资产',
      upload: '上传资产',
      download: '下载资产',
      preview: '预览',
      metadata: '元数据',
      tags: '标签',
      categories: '分类'
    },
    transactions: {
      title: '交易记录',
      list: '交易列表',
      create: '创建交易',
      edit: '编辑交易',
      delete: '删除交易',
      amount: '金额',
      date: '日期',
      type: '类型',
      description: '描述',
      status: '状态'
    },
    admin: {
      title: '系统管理',
      users: '用户管理',
      roles: '角色管理',
      permissions: '权限管理',
      system: '系统设置',
      logs: '系统日志',
      backup: '数据备份',
      maintenance: '系统维护'
    }
  },
  'en': {
    common: {
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      cancel: 'Cancel',
      confirm: 'Confirm',
      save: 'Save',
      edit: 'Edit',
      delete: 'Delete',
      add: 'Add',
      search: 'Search',
      logout: 'Logout',
      login: 'Login',
      register: 'Register',
      username: 'Username',
      password: 'Password',
      email: 'Email',
      name: 'Name',
      status: 'Status',
      action: 'Action',
      submit: 'Submit',
      close: 'Close',
      download: 'Download',
      upload: 'Upload',
      view: 'View',
      settings: 'Settings',
      dashboard: 'Dashboard',
      home: 'Home',
      back: 'Back',
      help: 'Help',
      language: 'Language',
      theme: 'Theme',
      yes: 'Yes',
      no: 'No',
      ok: 'OK',
      warning: 'Warning',
      info: 'Info'
    },
    auth: {
      login: 'Login',
      register: 'Register',
      logout: 'Logout',
      forgot_password: 'Forgot Password',
      reset_password: 'Reset Password',
      change_password: 'Change Password',
      username: 'Username',
      password: 'Password',
      confirm_password: 'Confirm Password',
      email: 'Email',
      phone: 'Phone',
      login_success: 'Login successful',
      login_failed: 'Login failed',
      register_success: 'Registration successful',
      register_failed: 'Registration failed',
      invalid_credentials: 'Invalid username or password'
    },
    dashboard: {
      title: 'Dashboard',
      welcome: 'Welcome',
      overview: 'Overview',
      statistics: 'Statistics',
      recent_activities: 'Recent Activities',
      quick_actions: 'Quick Actions',
      notifications: 'Notifications',
      system_status: 'System Status'
    },
    assets: {
      title: 'Asset Management',
      list: 'Asset List',
      create: 'Create Asset',
      edit: 'Edit Asset',
      delete: 'Delete Asset',
      upload: 'Upload Asset',
      download: 'Download Asset',
      preview: 'Preview',
      metadata: 'Metadata',
      tags: 'Tags',
      categories: 'Categories'
    },
    transactions: {
      title: 'Transactions',
      list: 'Transaction List',
      create: 'Create Transaction',
      edit: 'Edit Transaction',
      delete: 'Delete Transaction',
      amount: 'Amount',
      date: 'Date',
      type: 'Type',
      description: 'Description',
      status: 'Status'
    },
    admin: {
      title: 'Administration',
      users: 'User Management',
      roles: 'Role Management',
      permissions: 'Permission Management',
      system: 'System Settings',
      logs: 'System Logs',
      backup: 'Data Backup',
      maintenance: 'System Maintenance'
    }
  }
};

// 初始化 i18next
i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    lng: 'zh-CN',
    fallbackLng: 'zh-CN',
    debug: process.env.NODE_ENV === 'development',
    
    interpolation: {
      escapeValue: false,
    },
    
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },
    
    defaultNS: 'common',
    ns: ['common', 'auth', 'dashboard', 'assets', 'transactions', 'admin'],
    
    react: {
      useSuspense: false,
    }
  });

export default i18n;