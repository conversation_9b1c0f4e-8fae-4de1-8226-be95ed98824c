import { AuthService } from '../../services/auth.service';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';

// Mock dependencies
jest.mock('bcrypt');
jest.mock('jsonwebtoken');
jest.mock('../../database/connection');

const mockedBcrypt = bcrypt as jest.Mocked<typeof bcrypt>;
const mockedJwt = jwt as jest.Mocked<typeof jwt>;

describe('AuthService', () => {
  let authService: AuthService;
  let mockUserRepository: any;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock user repository
    mockUserRepository = {
      findByEmail: jest.fn(),
      create: jest.fn(),
      findById: jest.fn(),
      update: jest.fn()
    };

    authService = new AuthService();
    // Inject mock repository
    (authService as any).userRepository = mockUserRepository;
  });

  describe('register', () => {
    test('should successfully register a new individual user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'Test User',
        creatorType: 'PERSONAL'
      };

      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockedBcrypt.hash.mockResolvedValue('hashedPassword');
      mockUserRepository.create.mockResolvedValue({
        id: '1',
        email: userData.email,
        displayName: userData.displayName,
        creatorType: userData.creatorType,
        createdAt: new Date()
      });

      const result = await authService.register(userData);

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(userData.email);
      expect(mockedBcrypt.hash).toHaveBeenCalledWith(userData.password, 12);
      expect(mockUserRepository.create).toHaveBeenCalledWith({
        email: userData.email,
        passwordHash: 'hashedPassword',
        displayName: userData.displayName,
        creatorType: userData.creatorType
      });
      expect(result).toHaveProperty('id', '1');
      expect(result).toHaveProperty('email', userData.email);
    });

    test('should successfully register a new enterprise user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'Enterprise Corp',
        creatorType: 'ENTERPRISE'
      };

      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockedBcrypt.hash.mockResolvedValue('hashedPassword');
      mockUserRepository.create.mockResolvedValue({
        id: '2',
        email: userData.email,
        displayName: userData.displayName,
        creatorType: userData.creatorType,
        createdAt: new Date()
      });

      const result = await authService.register(userData);

      expect(result).toHaveProperty('creatorType', 'ENTERPRISE');
    });

    test('should throw error if email already exists', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'Test User',
        creatorType: 'PERSONAL'
      };

      mockUserRepository.findByEmail.mockResolvedValue({
        id: '1',
        email: userData.email
      });

      await expect(authService.register(userData)).rejects.toThrow('Email already exists');
      expect(mockedBcrypt.hash).not.toHaveBeenCalled();
      expect(mockUserRepository.create).not.toHaveBeenCalled();
    });

    test('should validate email format', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'password123',
        displayName: 'Test User',
        creatorType: 'PERSONAL'
      };

      await expect(authService.register(userData)).rejects.toThrow('Invalid email format');
    });

    test('should validate password strength', async () => {
      const userData = {
        email: '<EMAIL>',
        password: '123', // Too short
        displayName: 'Test User',
        creatorType: 'PERSONAL'
      };

      await expect(authService.register(userData)).rejects.toThrow('Password must be at least 8 characters');
    });

    test('should validate creator type', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'Test User',
        creatorType: 'INVALID'
      };

      await expect(authService.register(userData)).rejects.toThrow('Invalid creator type');
    });
  });

  describe('login', () => {
    test('should successfully login with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const mockUser = {
        id: '1',
        email: loginData.email,
        passwordHash: 'hashedPassword',
        displayName: 'Test User',
        creatorType: 'PERSONAL'
      };

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(true);
      mockedJwt.sign.mockReturnValue('jwt-token');

      const result = await authService.login(loginData);

      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(loginData.email);
      expect(mockedBcrypt.compare).toHaveBeenCalledWith(loginData.password, mockUser.passwordHash);
      expect(mockedJwt.sign).toHaveBeenCalledWith(
        { userId: mockUser.id, email: mockUser.email, creatorType: mockUser.creatorType },
        expect.any(String),
        { expiresIn: '24h' }
      );
      expect(result).toHaveProperty('token', 'jwt-token');
      expect(result).toHaveProperty('user');
      expect(result.user).not.toHaveProperty('passwordHash');
    });

    test('should throw error for non-existent user', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      };

      mockUserRepository.findByEmail.mockResolvedValue(null);

      await expect(authService.login(loginData)).rejects.toThrow('Invalid credentials');
      expect(mockedBcrypt.compare).not.toHaveBeenCalled();
    });

    test('should throw error for invalid password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const mockUser = {
        id: '1',
        email: loginData.email,
        passwordHash: 'hashedPassword',
        displayName: 'Test User',
        creatorType: 'PERSONAL'
      };

      mockUserRepository.findByEmail.mockResolvedValue(mockUser);
      mockedBcrypt.compare.mockResolvedValue(false);

      await expect(authService.login(loginData)).rejects.toThrow('Invalid credentials');
      expect(mockedJwt.sign).not.toHaveBeenCalled();
    });
  });

  describe('verifyToken', () => {
    test('should successfully verify valid token', async () => {
      const token = 'valid-jwt-token';
      const decodedPayload = {
        userId: '1',
        email: '<EMAIL>',
        creatorType: 'PERSONAL'
      };

      mockedJwt.verify.mockReturnValue(decodedPayload as any);
      mockUserRepository.findById.mockResolvedValue({
        id: '1',
        email: '<EMAIL>',
        displayName: 'Test User',
        creatorType: 'PERSONAL'
      });

      const result = await authService.verifyToken(token);

      expect(mockedJwt.verify).toHaveBeenCalledWith(token, expect.any(String));
      expect(mockUserRepository.findById).toHaveBeenCalledWith('1');
      expect(result).toHaveProperty('id', '1');
    });

    test('should throw error for invalid token', async () => {
      const token = 'invalid-token';

      mockedJwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await expect(authService.verifyToken(token)).rejects.toThrow('Invalid token');
    });

    test('should throw error if user not found', async () => {
      const token = 'valid-jwt-token';
      const decodedPayload = {
        userId: '999',
        email: '<EMAIL>',
        creatorType: 'PERSONAL'
      };

      mockedJwt.verify.mockReturnValue(decodedPayload as any);
      mockUserRepository.findById.mockResolvedValue(null);

      await expect(authService.verifyToken(token)).rejects.toThrow('User not found');
    });
  });

  describe('OAuth integration', () => {
    test('should handle Google OAuth registration', async () => {
      const oauthData = {
        provider: 'google',
        providerId: 'google-123',
        email: '<EMAIL>',
        displayName: 'OAuth User'
      };

      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserRepository.create.mockResolvedValue({
        id: '3',
        email: oauthData.email,
        displayName: oauthData.displayName,
        creatorType: 'PERSONAL',
        oauthProvider: 'google',
        oauthId: 'google-123'
      });

      const result = await authService.handleOAuthLogin(oauthData);

      expect(mockUserRepository.create).toHaveBeenCalledWith({
        email: oauthData.email,
        displayName: oauthData.displayName,
        creatorType: 'PERSONAL',
        oauthProvider: 'google',
        oauthId: 'google-123',
        passwordHash: null
      });
      expect(result).toHaveProperty('id', '3');
    });

    test('should handle existing OAuth user login', async () => {
      const oauthData = {
        provider: 'google',
        providerId: 'google-123',
        email: '<EMAIL>',
        displayName: 'Existing User'
      };

      const existingUser = {
        id: '4',
        email: oauthData.email,
        displayName: oauthData.displayName,
        creatorType: 'PERSONAL',
        oauthProvider: 'google',
        oauthId: 'google-123'
      };

      mockUserRepository.findByEmail.mockResolvedValue(existingUser);

      const result = await authService.handleOAuthLogin(oauthData);

      expect(mockUserRepository.create).not.toHaveBeenCalled();
      expect(result).toHaveProperty('id', '4');
    });
  });
});
