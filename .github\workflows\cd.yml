name: CD Pipeline

on:
  workflow_run:
    workflows: ["CI Pipeline"]
    branches: [main, develop]
    types: [completed]
  
  release:
    types: [published]

env:
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    if: |
      github.event.workflow_run.conclusion == 'success' && 
      github.event.workflow_run.head_branch == 'develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.STAGING_SSH_KEY }}

    - name: Add SSH known hosts
      run: |
        ssh-keyscan -H ${{ secrets.STAGING_HOST }} >> ~/.ssh/known_hosts

    - name: Deploy to staging environment
      run: |
        ssh ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }} "
          cd /opt/aigc-hub &&
          git fetch origin &&
          git checkout develop &&
          git pull origin develop &&
          
          # 创建备份
          chmod +x scripts/backup.sh &&
          ./scripts/backup.sh --type quick --env staging &&
          
          # 部署应用
          chmod +x scripts/deploy.sh &&
          ./scripts/deploy.sh --env staging --no-confirm &&
          
          # 等待服务启动
          sleep 30
        "

    - name: Health check
      run: |
        max_attempts=10
        attempt=0
        
        while [ $attempt -lt $max_attempts ]; do
          if curl -f https://staging.aigc-hub.com/health; then
            echo "Health check passed"
            break
          fi
          
          attempt=$((attempt + 1))
          echo "Health check failed, attempt $attempt/$max_attempts"
          sleep 15
        done
        
        if [ $attempt -eq $max_attempts ]; then
          echo "Health check failed after $max_attempts attempts"
          exit 1
        fi

    - name: Run smoke tests
      run: |
        ssh ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }} "
          cd /opt/aigc-hub &&
          docker-compose -f docker-compose.test.yml exec -T backend npm run test:smoke
        "

    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        text: '🚀 Successfully deployed to staging environment'

    - name: Notify deployment failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        text: '❌ Failed to deploy to staging environment'

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    if: |
      github.event.workflow_run.conclusion == 'success' && 
      github.event.workflow_run.head_branch == 'main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.PRODUCTION_SSH_KEY }}

    - name: Add SSH known hosts
      run: |
        ssh-keyscan -H ${{ secrets.PRODUCTION_HOST }} >> ~/.ssh/known_hosts

    - name: Create production backup
      run: |
        ssh ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "
          cd /opt/aigc-hub &&
          chmod +x scripts/backup.sh &&
          ./scripts/backup.sh --type full --env production --compress --encrypt
        "

    - name: Deploy to production environment
      run: |
        ssh ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "
          cd /opt/aigc-hub &&
          git fetch origin &&
          git checkout main &&
          git pull origin main &&
          
          # 部署应用
          chmod +x scripts/deploy.sh &&
          ./scripts/deploy.sh --env production --backup --no-confirm &&
          
          # 等待服务启动
          sleep 60
        "

    - name: Health check
      run: |
        max_attempts=15
        attempt=0
        
        while [ $attempt -lt $max_attempts ]; do
          if curl -f https://aigc-hub.com/health && curl -f https://aigc-hub.com/api/health; then
            echo "Health check passed"
            break
          fi
          
          attempt=$((attempt + 1))
          echo "Health check failed, attempt $attempt/$max_attempts"
          sleep 20
        done
        
        if [ $attempt -eq $max_attempts ]; then
          echo "Health check failed after $max_attempts attempts"
          exit 1
        fi

    - name: Run production smoke tests
      run: |
        ssh ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "
          cd /opt/aigc-hub &&
          docker-compose -f docker-compose.prod.yml exec -T backend npm run test:smoke
        "

    - name: Run production integration tests
      run: |
        ssh ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "
          cd /opt/aigc-hub &&
          docker-compose -f docker-compose.prod.yml exec -T backend npm run test:integration
        "

    - name: Update deployment status
      run: |
        curl -X POST \
          -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
          -H "Content-Type: application/json" \
          -d '{
            "state": "success",
            "description": "Deployment successful",
            "environment": "production",
            "environment_url": "https://aigc-hub.com"
          }' \
          "https://api.github.com/repos/${{ github.repository }}/deployments"

    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        text: '🎉 Successfully deployed to production environment'

    - name: Rollback on failure
      if: failure()
      run: |
        ssh ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "
          cd /opt/aigc-hub &&
          chmod +x scripts/restore.sh &&
          ./scripts/restore.sh --type quick --env production --no-confirm
        "

    - name: Notify deployment failure
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        text: '🚨 Failed to deploy to production environment - rollback initiated'

  release-deploy:
    name: Deploy Release
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.release.tag_name }}

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.PRODUCTION_SSH_KEY }}

    - name: Add SSH known hosts
      run: |
        ssh-keyscan -H ${{ secrets.PRODUCTION_HOST }} >> ~/.ssh/known_hosts

    - name: Deploy release
      run: |
        ssh ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "
          cd /opt/aigc-hub &&
          git fetch origin &&
          git checkout ${{ github.event.release.tag_name }} &&
          
          # 创建发布备份
          chmod +x scripts/backup.sh &&
          ./scripts/backup.sh --type full --env production --tag ${{ github.event.release.tag_name }} &&
          
          # 部署发布版本
          chmod +x scripts/deploy.sh &&
          ./scripts/deploy.sh --env production --tag ${{ github.event.release.tag_name }} --no-confirm &&
          
          # 等待服务启动
          sleep 60
        "

    - name: Health check
      run: |
        max_attempts=15
        attempt=0
        
        while [ $attempt -lt $max_attempts ]; do
          if curl -f https://aigc-hub.com/health && curl -f https://aigc-hub.com/api/health; then
            echo "Health check passed"
            break
          fi
          
          attempt=$((attempt + 1))
          echo "Health check failed, attempt $attempt/$max_attempts"
          sleep 20
        done
        
        if [ $attempt -eq $max_attempts ]; then
          echo "Health check failed after $max_attempts attempts"
          exit 1
        fi

    - name: Run release tests
      run: |
        ssh ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "
          cd /opt/aigc-hub &&
          docker-compose -f docker-compose.prod.yml exec -T backend npm run test:release
        "

    - name: Update release status
      run: |
        curl -X POST \
          -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
          -H "Content-Type: application/json" \
          -d '{
            "state": "success",
            "description": "Release deployed successfully",
            "environment": "production",
            "environment_url": "https://aigc-hub.com"
          }' \
          "https://api.github.com/repos/${{ github.repository }}/deployments"

    - name: Notify release success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        channel: '#releases'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        text: '🎊 Release ${{ github.event.release.tag_name }} deployed successfully'

  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production, release-deploy]
    if: always()
    
    steps:
    - name: Cleanup old images
      run: |
        # 保留最近的5个镜像版本
        echo "Cleaning up old Docker images..."
        
        # 这里可以添加清理逻辑，比如删除旧的镜像标签
        # 由于这需要访问容器注册表，这里只是示例
        echo "Cleanup completed"

    - name: Cleanup old deployments
      run: |
        # 清理旧的部署记录
        echo "Cleaning up old deployment records..."
        
        # 这里可以添加清理GitHub部署记录的逻辑
        echo "Cleanup completed"

    - name: Archive deployment logs
      uses: actions/upload-artifact@v3
      with:
        name: deployment-logs
        path: |
          *.log
          deployment-*.json
        retention-days: 30