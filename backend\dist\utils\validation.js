"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateAmount = exports.validatePoints = exports.sanitizeHtml = exports.isValidUrl = exports.isStrongPassword = exports.isValidEmail = exports.validateFileUpload = exports.validateData = exports.validateParams = exports.validateQuery = exports.validate = exports.validationSchemas = exports.validationRules = void 0;
const joi_1 = __importDefault(require("joi"));
const errors_1 = require("./errors");
exports.validationRules = {
    email: joi_1.default.string().email().required().messages({
        'string.email': 'Invalid email format',
        'any.required': 'Email is required',
    }),
    password: joi_1.default.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/).required().messages({
        'string.min': 'Password must be at least 8 characters long',
        'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
        'any.required': 'Password is required',
    }),
    displayName: joi_1.default.string().min(2).max(50).required().messages({
        'string.min': 'Display name must be at least 2 characters long',
        'string.max': 'Display name must not exceed 50 characters',
        'any.required': 'Display name is required',
    }),
    userRole: joi_1.default.string().valid('PERSONAL_CREATOR', 'ENTERPRISE_CREATOR').required().messages({
        'any.only': 'User role must be either PERSONAL_CREATOR or ENTERPRISE_CREATOR',
        'any.required': 'User role is required',
    }),
    assetTitle: joi_1.default.string().min(1).max(255).required().messages({
        'string.min': 'Asset title cannot be empty',
        'string.max': 'Asset title must not exceed 255 characters',
        'any.required': 'Asset title is required',
    }),
    assetDescription: joi_1.default.string().max(5000).optional().messages({
        'string.max': 'Asset description must not exceed 5000 characters',
    }),
    assetType: joi_1.default.string().valid('MODEL', 'LORA', 'WORKFLOW', 'PROMPT', 'TOOL').required().messages({
        'any.only': 'Asset type must be one of: MODEL, LORA, WORKFLOW, PROMPT, TOOL',
        'any.required': 'Asset type is required',
    }),
    priceUsd: joi_1.default.number().positive().precision(2).optional().messages({
        'number.positive': 'USD price must be positive',
        'number.precision': 'USD price can have at most 2 decimal places',
    }),
    pricePoints: joi_1.default.number().integer().positive().optional().messages({
        'number.integer': 'Points price must be an integer',
        'number.positive': 'Points price must be positive',
    }),
    tags: joi_1.default.array().items(joi_1.default.string().min(1).max(50)).max(20).optional().messages({
        'array.max': 'Maximum 20 tags allowed',
        'string.min': 'Tag cannot be empty',
        'string.max': 'Tag must not exceed 50 characters',
    }),
    fileKey: joi_1.default.string().required().messages({
        'any.required': 'File key is required',
    }),
    fileSize: joi_1.default.number().integer().positive().max(32212254720).required().messages({
        'number.integer': 'File size must be an integer',
        'number.positive': 'File size must be positive',
        'number.max': 'File size must not exceed 30GB',
        'any.required': 'File size is required',
    }),
    fileName: joi_1.default.string().max(255).required().messages({
        'string.max': 'File name must not exceed 255 characters',
        'any.required': 'File name is required',
    }),
    currency: joi_1.default.string().valid('USD', 'POINTS').required().messages({
        'any.only': 'Currency must be either USD or POINTS',
        'any.required': 'Currency is required',
    }),
    withdrawalAmount: joi_1.default.number().positive().min(10).precision(2).required().messages({
        'number.positive': 'Withdrawal amount must be positive',
        'number.min': 'Minimum withdrawal amount is $10.00',
        'number.precision': 'Withdrawal amount can have at most 2 decimal places',
        'any.required': 'Withdrawal amount is required',
    }),
    paypalEmail: joi_1.default.string().email().required().messages({
        'string.email': 'Invalid PayPal email format',
        'any.required': 'PayPal email is required',
    }),
    page: joi_1.default.number().integer().min(1).default(1).messages({
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1',
    }),
    limit: joi_1.default.number().integer().min(1).max(100).default(20).messages({
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit must not exceed 100',
    }),
    sortBy: joi_1.default.string().valid('created_at', 'updated_at', 'price_usd', 'download_count').default('created_at').messages({
        'any.only': 'Sort by must be one of: created_at, updated_at, price_usd, download_count',
    }),
    sortOrder: joi_1.default.string().valid('asc', 'desc').default('desc').messages({
        'any.only': 'Sort order must be either asc or desc',
    }),
    id: joi_1.default.number().integer().positive().required().messages({
        'number.integer': 'ID must be an integer',
        'number.positive': 'ID must be positive',
        'any.required': 'ID is required',
    }),
    optionalId: joi_1.default.number().integer().positive().optional().messages({
        'number.integer': 'ID must be an integer',
        'number.positive': 'ID must be positive',
    }),
};
exports.validationSchemas = {
    register: joi_1.default.object({
        email: exports.validationRules.email,
        password: exports.validationRules.password,
        displayName: exports.validationRules.displayName,
        userRole: exports.validationRules.userRole,
    }),
    login: joi_1.default.object({
        email: exports.validationRules.email,
        password: joi_1.default.string().required().messages({
            'any.required': 'Password is required',
        }),
    }),
    refreshToken: joi_1.default.object({
        refreshToken: joi_1.default.string().required().messages({
            'any.required': 'Refresh token is required',
        }),
    }),
    createAsset: joi_1.default.object({
        title: exports.validationRules.assetTitle,
        description: exports.validationRules.assetDescription,
        assetType: exports.validationRules.assetType,
        priceUsd: exports.validationRules.priceUsd,
        pricePoints: exports.validationRules.pricePoints,
        categories: exports.validationRules.tags,
        styles: exports.validationRules.tags,
        coverImageUrl: joi_1.default.string().uri().optional().messages({
            'string.uri': 'Cover image URL must be a valid URI',
        }),
    }).custom((value, helpers) => {
        if (!value.priceUsd && !value.pricePoints) {
            return helpers.error('any.custom', { message: 'At least one price (USD or Points) is required' });
        }
        return value;
    }),
    updateAsset: joi_1.default.object({
        title: exports.validationRules.assetTitle.optional(),
        description: exports.validationRules.assetDescription,
        priceUsd: exports.validationRules.priceUsd,
        pricePoints: exports.validationRules.pricePoints,
        categories: exports.validationRules.tags,
        styles: exports.validationRules.tags,
        coverImageUrl: joi_1.default.string().uri().optional().messages({
            'string.uri': 'Cover image URL must be a valid URI',
        }),
        status: joi_1.default.string().valid('DRAFT', 'PUBLISHED', 'ARCHIVED').optional().messages({
            'any.only': 'Status must be one of: DRAFT, PUBLISHED, ARCHIVED',
        }),
    }),
    confirmUpload: joi_1.default.object({
        fileKey: exports.validationRules.fileKey,
        fileSize: exports.validationRules.fileSize,
        fileName: exports.validationRules.fileName,
    }),
    purchase: joi_1.default.object({
        assetId: exports.validationRules.id,
        currency: exports.validationRules.currency,
    }),
    withdrawal: joi_1.default.object({
        amount: exports.validationRules.withdrawalAmount,
        paypalEmail: exports.validationRules.paypalEmail,
    }),
    pagination: joi_1.default.object({
        page: exports.validationRules.page,
        limit: exports.validationRules.limit,
    }),
    assetQuery: joi_1.default.object({
        page: exports.validationRules.page,
        limit: exports.validationRules.limit,
        category: joi_1.default.string().optional(),
        style: joi_1.default.string().optional(),
        assetType: exports.validationRules.assetType.optional(),
        sortBy: exports.validationRules.sortBy,
        sortOrder: exports.validationRules.sortOrder,
        search: joi_1.default.string().max(100).optional().messages({
            'string.max': 'Search term must not exceed 100 characters',
        }),
    }),
    transactionQuery: joi_1.default.object({
        page: exports.validationRules.page,
        limit: exports.validationRules.limit,
        currency: exports.validationRules.currency.optional(),
        status: joi_1.default.string().valid('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED').optional().messages({
            'any.only': 'Status must be one of: PENDING, COMPLETED, FAILED, REFUNDED',
        }),
        startDate: joi_1.default.date().optional(),
        endDate: joi_1.default.date().optional(),
    }),
    pathId: joi_1.default.object({
        id: exports.validationRules.id,
    }),
    updateProfile: joi_1.default.object({
        displayName: exports.validationRules.displayName.optional(),
        currentPassword: joi_1.default.string().when('newPassword', {
            is: joi_1.default.exist(),
            then: joi_1.default.required(),
            otherwise: joi_1.default.optional(),
        }).messages({
            'any.required': 'Current password is required when changing password',
        }),
        newPassword: exports.validationRules.password.optional(),
    }),
    adminUpdateUser: joi_1.default.object({
        displayName: exports.validationRules.displayName.optional(),
        isActive: joi_1.default.boolean().optional(),
        userRole: joi_1.default.string().valid('PERSONAL_CREATOR', 'ENTERPRISE_CREATOR', 'ADMIN').optional().messages({
            'any.only': 'User role must be one of: PERSONAL_CREATOR, ENTERPRISE_CREATOR, ADMIN',
        }),
    }),
    processWithdrawal: joi_1.default.object({
        action: joi_1.default.string().valid('APPROVE', 'REJECT').required().messages({
            'any.only': 'Action must be either APPROVE or REJECT',
            'any.required': 'Action is required',
        }),
        adminNotes: joi_1.default.string().max(500).optional().messages({
            'string.max': 'Admin notes must not exceed 500 characters',
        }),
        rejectionReason: joi_1.default.string().max(500).when('action', {
            is: 'REJECT',
            then: joi_1.default.required(),
            otherwise: joi_1.default.optional(),
        }).messages({
            'string.max': 'Rejection reason must not exceed 500 characters',
            'any.required': 'Rejection reason is required when rejecting withdrawal',
        }),
    }),
    refund: joi_1.default.object({
        transactionId: exports.validationRules.id,
        reason: joi_1.default.string().max(500).required().messages({
            'string.max': 'Refund reason must not exceed 500 characters',
            'any.required': 'Refund reason is required',
        }),
    }),
};
const validate = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.body, { abortEarly: false });
        if (error) {
            const details = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message,
            }));
            throw new errors_1.ValidationError('Validation failed', details);
        }
        req.body = value;
        next();
    };
};
exports.validate = validate;
const validateQuery = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.query, { abortEarly: false });
        if (error) {
            const details = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message,
            }));
            throw new errors_1.ValidationError('Query validation failed', details);
        }
        req.query = value;
        next();
    };
};
exports.validateQuery = validateQuery;
const validateParams = (schema) => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req.params, { abortEarly: false });
        if (error) {
            const details = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message,
            }));
            throw new errors_1.ValidationError('Parameter validation failed', details);
        }
        req.params = value;
        next();
    };
};
exports.validateParams = validateParams;
const validateData = (schema, data) => {
    const { error, value } = schema.validate(data, { abortEarly: false });
    if (error) {
        const details = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
        }));
        throw new errors_1.ValidationError('Data validation failed', details);
    }
    return value;
};
exports.validateData = validateData;
const validateFileUpload = (req, res, next) => {
    if (!req.file) {
        throw new errors_1.ValidationError('No file uploaded');
    }
    const allowedMimeTypes = [
        'application/zip',
        'application/x-zip-compressed',
        'application/x-rar-compressed',
        'application/x-7z-compressed',
        'application/gzip',
        'application/x-tar',
        'application/octet-stream',
    ];
    if (!allowedMimeTypes.includes(req.file.mimetype)) {
        throw new errors_1.ValidationError('Invalid file type. Only compressed files are allowed.');
    }
    if (req.file.size > 32212254720) {
        throw new errors_1.ValidationError('File size too large. Maximum size is 30GB.');
    }
    next();
};
exports.validateFileUpload = validateFileUpload;
const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
exports.isValidEmail = isValidEmail;
const isStrongPassword = (password) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers;
};
exports.isStrongPassword = isStrongPassword;
const isValidUrl = (url) => {
    try {
        new URL(url);
        return true;
    }
    catch {
        return false;
    }
};
exports.isValidUrl = isValidUrl;
const sanitizeHtml = (html) => {
    return html.replace(/<script[^>]*>.*?<\/script>/gi, '')
        .replace(/<[^>]*>/g, '')
        .trim();
};
exports.sanitizeHtml = sanitizeHtml;
const validatePoints = (points) => {
    return Number.isInteger(points) && points > 0;
};
exports.validatePoints = validatePoints;
const validateAmount = (amount) => {
    return Number.isFinite(amount) && amount > 0 && Number((amount * 100).toFixed(0)) / 100 === amount;
};
exports.validateAmount = validateAmount;
//# sourceMappingURL=validation.js.map