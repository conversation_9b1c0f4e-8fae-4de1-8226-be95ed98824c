import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Asset, AssetCategory, AssetType, AssetStatus, AssetSearchParams, PaginationParams } from '../../types';
import { assetsAPI } from '../../services/api';

// 异步actions
export const fetchAssets = createAsyncThunk(
  'assets/fetchAssets',
  async (params: AssetSearchParams & PaginationParams, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.getAssets(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取资产失败' });
    }
  }
);

export const fetchAssetById = createAsyncThunk(
  'assets/fetchAssetById',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.getAssetById(id);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取资产详情失败' });
    }
  }
);

export const fetchMyAssets = createAsyncThunk(
  'assets/fetchMyAssets',
  async (params: PaginationParams & { status?: AssetStatus }, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.getMyAssets(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取我的资产失败' });
    }
  }
);

export const fetchPurchasedAssets = createAsyncThunk(
  'assets/fetchPurchasedAssets',
  async (params: PaginationParams, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.getPurchasedAssets(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取已购买资产失败' });
    }
  }
);

export const createAsset = createAsyncThunk(
  'assets/createAsset',
  async (assetData: FormData, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.createAsset(assetData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '创建资产失败' });
    }
  }
);

export const updateAsset = createAsyncThunk(
  'assets/updateAsset',
  async (params: { id: string; data: Partial<Asset> }, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.updateAsset(params.id, params.data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '更新资产失败' });
    }
  }
);

export const deleteAsset = createAsyncThunk(
  'assets/deleteAsset',
  async (id: string, { rejectWithValue }) => {
    try {
      await assetsAPI.deleteAsset(id);
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '删除资产失败' });
    }
  }
);

export const purchaseAsset = createAsyncThunk(
  'assets/purchaseAsset',
  async (assetId: string, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.purchaseAsset(assetId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '购买资产失败' });
    }
  }
);

export const toggleLikeAsset = createAsyncThunk(
  'assets/toggleLikeAsset',
  async (assetId: string, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.toggleLike(assetId);
      return { assetId, isLiked: response.data.data.isLiked };
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '操作失败' });
    }
  }
);

export const addAssetView = createAsyncThunk(
  'assets/addAssetView',
  async (assetId: string, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.addView(assetId);
      return { assetId, views: response.data.data.views };
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '记录浏览失败' });
    }
  }
);

export const downloadAsset = createAsyncThunk(
  'assets/downloadAsset',
  async (assetId: string, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.downloadAsset(assetId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '下载资产失败' });
    }
  }
);

export const fetchCategories = createAsyncThunk(
  'assets/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.getCategories();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取分类失败' });
    }
  }
);

export const fetchFeaturedAssets = createAsyncThunk(
  'assets/fetchFeaturedAssets',
  async (params: { limit?: number; category?: string }, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.getFeaturedAssets(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取精选资产失败' });
    }
  }
);

export const fetchTrendingAssets = createAsyncThunk(
  'assets/fetchTrendingAssets',
  async (params: { limit?: number; timeRange?: string }, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.getTrendingAssets(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取热门资产失败' });
    }
  }
);

export const fetchRecommendedAssets = createAsyncThunk(
  'assets/fetchRecommendedAssets',
  async (params: { limit?: number; userId?: string }, { rejectWithValue }) => {
    try {
      const response = await assetsAPI.getRecommendedAssets(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取推荐资产失败' });
    }
  }
);

// 分页响应接口
interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

// 状态接口
interface AssetsState {
  // 资产列表
  assets: Asset[];
  myAssets: Asset[];
  purchasedAssets: Asset[];
  featuredAssets: Asset[];
  trendingAssets: Asset[];
  recommendedAssets: Asset[];
  
  // 当前资产
  currentAsset: Asset | null;
  
  // 分类数据
  categories: AssetCategory[];
  
  // 分页信息
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  
  // 搜索和过滤
  searchParams: AssetSearchParams;
  filters: {
    category: string;
    type: AssetType | '';
    priceRange: [number, number];
    sortBy: string;
    sortOrder: 'asc' | 'desc';
  };
  
  // 加载状态
  isLoading: boolean;
  isLoadingMore: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isPurchasing: boolean;
  isDownloading: boolean;
  
  // 错误状态
  error: string | null;
  
  // 上传状态
  uploadProgress: number;
  uploadStatus: 'idle' | 'uploading' | 'processing' | 'success' | 'error';
  
  // 预览状态
  previewAsset: Asset | null;
  previewMode: 'image' | 'video' | 'model' | 'audio' | null;
  
  // 收藏状态
  likedAssets: string[];
  
  // 最近浏览
  recentlyViewed: Asset[];
  
  // 购物车
  cart: {
    items: Asset[];
    total: number;
  };
  
  // 统计信息
  stats: {
    totalAssets: number;
    totalViews: number;
    totalDownloads: number;
    totalEarnings: number;
  };
}

// 初始状态
const initialState: AssetsState = {
  assets: [],
  myAssets: [],
  purchasedAssets: [],
  featuredAssets: [],
  trendingAssets: [],
  recommendedAssets: [],
  currentAsset: null,
  categories: [],
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
  },
  searchParams: {
    query: '',
    category: '',
    type: '' as AssetType,
    tags: [],
    priceMin: 0,
    priceMax: 1000,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  },
  filters: {
    category: '',
    type: '',
    priceRange: [0, 1000],
    sortBy: 'createdAt',
    sortOrder: 'desc',
  },
  isLoading: false,
  isLoadingMore: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isPurchasing: false,
  isDownloading: false,
  error: null,
  uploadProgress: 0,
  uploadStatus: 'idle',
  previewAsset: null,
  previewMode: null,
  likedAssets: JSON.parse(localStorage.getItem('likedAssets') || '[]'),
  recentlyViewed: JSON.parse(localStorage.getItem('recentlyViewed') || '[]'),
  cart: {
    items: JSON.parse(localStorage.getItem('cart') || '[]'),
    total: 0,
  },
  stats: {
    totalAssets: 0,
    totalViews: 0,
    totalDownloads: 0,
    totalEarnings: 0,
  },
};

// 计算购物车总价
const calculateCartTotal = (items: Asset[]): number => {
  return items.reduce((total, item) => total + (item.price || 0), 0);
};

// 创建slice
const assetsSlice = createSlice({
  name: 'assets',
  initialState,
  reducers: {
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    
    // 设置搜索参数
    setSearchParams: (state, action: PayloadAction<Partial<AssetSearchParams>>) => {
      state.searchParams = { ...state.searchParams, ...action.payload };
    },
    
    // 设置过滤器
    setFilters: (state, action: PayloadAction<Partial<AssetsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // 重置过滤器
    resetFilters: (state) => {
      state.filters = initialState.filters;
      state.searchParams = initialState.searchParams;
    },
    
    // 设置预览资产
    setPreviewAsset: (state, action: PayloadAction<{
      asset: Asset | null;
      mode?: 'image' | 'video' | 'model' | 'audio' | null;
    }>) => {
      state.previewAsset = action.payload.asset;
      state.previewMode = action.payload.mode || null;
    },
    
    // 添加到最近浏览
    addToRecentlyViewed: (state, action: PayloadAction<Asset>) => {
      const asset = action.payload;
      const existingIndex = state.recentlyViewed.findIndex(item => item.id === asset.id);
      
      if (existingIndex >= 0) {
        state.recentlyViewed.splice(existingIndex, 1);
      }
      
      state.recentlyViewed.unshift(asset);
      
      // 限制最多保存20个
      if (state.recentlyViewed.length > 20) {
        state.recentlyViewed.pop();
      }
      
      localStorage.setItem('recentlyViewed', JSON.stringify(state.recentlyViewed));
    },
    
    // 清除最近浏览
    clearRecentlyViewed: (state) => {
      state.recentlyViewed = [];
      localStorage.removeItem('recentlyViewed');
    },
    
    // 购物车操作
    addToCart: (state, action: PayloadAction<Asset>) => {
      const asset = action.payload;
      const existingItem = state.cart.items.find(item => item.id === asset.id);
      
      if (!existingItem) {
        state.cart.items.push(asset);
        state.cart.total = calculateCartTotal(state.cart.items);
        localStorage.setItem('cart', JSON.stringify(state.cart.items));
      }
    },
    
    removeFromCart: (state, action: PayloadAction<string>) => {
      state.cart.items = state.cart.items.filter(item => item.id !== action.payload);
      state.cart.total = calculateCartTotal(state.cart.items);
      localStorage.setItem('cart', JSON.stringify(state.cart.items));
    },
    
    clearCart: (state) => {
      state.cart.items = [];
      state.cart.total = 0;
      localStorage.removeItem('cart');
    },
    
    // 设置上传进度
    setUploadProgress: (state, action: PayloadAction<number>) => {
      state.uploadProgress = action.payload;
    },
    
    // 设置上传状态
    setUploadStatus: (state, action: PayloadAction<AssetsState['uploadStatus']>) => {
      state.uploadStatus = action.payload;
    },
    
    // 更新统计信息
    updateStats: (state, action: PayloadAction<Partial<AssetsState['stats']>>) => {
      state.stats = { ...state.stats, ...action.payload };
    },
    
    // 重置状态
    resetAssets: (state) => {
      state.assets = [];
      state.pagination = initialState.pagination;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // 获取资产列表
    builder
      .addCase(fetchAssets.pending, (state, action) => {
        if (action.meta.arg.page === 1) {
          state.isLoading = true;
        } else {
          state.isLoadingMore = true;
        }
        state.error = null;
      })
      .addCase(fetchAssets.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        
        const { data, pagination } = action.payload.data as PaginatedResponse<Asset>;
        
        if (action.meta.arg.page === 1) {
          state.assets = data;
        } else {
          state.assets.push(...data);
        }
        
        state.pagination = pagination;
        state.error = null;
      })
      .addCase(fetchAssets.rejected, (state, action) => {
        state.isLoading = false;
        state.isLoadingMore = false;
        state.error = action.payload as string;
      });

    // 获取资产详情
    builder
      .addCase(fetchAssetById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAssetById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentAsset = action.payload.data;
        state.error = null;
      })
      .addCase(fetchAssetById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取我的资产
    builder
      .addCase(fetchMyAssets.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMyAssets.fulfilled, (state, action) => {
        state.isLoading = false;
        state.myAssets = action.payload.data.data;
        state.error = null;
      })
      .addCase(fetchMyAssets.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取已购买资产
    builder
      .addCase(fetchPurchasedAssets.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPurchasedAssets.fulfilled, (state, action) => {
        state.isLoading = false;
        state.purchasedAssets = action.payload.data.data;
        state.error = null;
      })
      .addCase(fetchPurchasedAssets.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 创建资产
    builder
      .addCase(createAsset.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createAsset.fulfilled, (state, action) => {
        state.isCreating = false;
        state.myAssets.unshift(action.payload.data);
        state.error = null;
      })
      .addCase(createAsset.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.payload as string;
      });

    // 更新资产
    builder
      .addCase(updateAsset.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateAsset.fulfilled, (state, action) => {
        state.isUpdating = false;
        const updatedAsset = action.payload.data;
        
        // 更新各个列表中的资产
        const updateAssetInList = (list: Asset[]) => {
          const index = list.findIndex(asset => asset.id === updatedAsset.id);
          if (index >= 0) {
            list[index] = updatedAsset;
          }
        };
        
        updateAssetInList(state.assets);
        updateAssetInList(state.myAssets);
        updateAssetInList(state.purchasedAssets);
        updateAssetInList(state.featuredAssets);
        updateAssetInList(state.trendingAssets);
        updateAssetInList(state.recommendedAssets);
        
        if (state.currentAsset?.id === updatedAsset.id) {
          state.currentAsset = updatedAsset;
        }
        
        state.error = null;
      })
      .addCase(updateAsset.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload as string;
      });

    // 删除资产
    builder
      .addCase(deleteAsset.pending, (state) => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteAsset.fulfilled, (state, action) => {
        state.isDeleting = false;
        const assetId = action.payload;
        
        // 从各个列表中删除资产
        const removeAssetFromList = (list: Asset[]) => {
          return list.filter(asset => asset.id !== assetId);
        };
        
        state.assets = removeAssetFromList(state.assets);
        state.myAssets = removeAssetFromList(state.myAssets);
        state.purchasedAssets = removeAssetFromList(state.purchasedAssets);
        state.featuredAssets = removeAssetFromList(state.featuredAssets);
        state.trendingAssets = removeAssetFromList(state.trendingAssets);
        state.recommendedAssets = removeAssetFromList(state.recommendedAssets);
        
        if (state.currentAsset?.id === assetId) {
          state.currentAsset = null;
        }
        
        state.error = null;
      })
      .addCase(deleteAsset.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.payload as string;
      });

    // 购买资产
    builder
      .addCase(purchaseAsset.pending, (state) => {
        state.isPurchasing = true;
        state.error = null;
      })
      .addCase(purchaseAsset.fulfilled, (state, action) => {
        state.isPurchasing = false;
        const purchasedAsset = action.payload.data;
        state.purchasedAssets.unshift(purchasedAsset);
        
        // 从购物车中移除
        state.cart.items = state.cart.items.filter(item => item.id !== purchasedAsset.id);
        state.cart.total = calculateCartTotal(state.cart.items);
        localStorage.setItem('cart', JSON.stringify(state.cart.items));
        
        state.error = null;
      })
      .addCase(purchaseAsset.rejected, (state, action) => {
        state.isPurchasing = false;
        state.error = action.payload as string;
      });

    // 切换收藏状态
    builder
      .addCase(toggleLikeAsset.fulfilled, (state, action) => {
        const { assetId, isLiked } = action.payload;
        
        if (isLiked) {
          if (!state.likedAssets.includes(assetId)) {
            state.likedAssets.push(assetId);
          }
        } else {
          state.likedAssets = state.likedAssets.filter(id => id !== assetId);
        }
        
        localStorage.setItem('likedAssets', JSON.stringify(state.likedAssets));
      });

    // 添加浏览记录
    builder
      .addCase(addAssetView.fulfilled, (state, action) => {
        const { assetId, views } = action.payload;
        
        // 更新资产的浏览次数
        const updateViews = (list: Asset[]) => {
          const asset = list.find(item => item.id === assetId);
          if (asset && asset.stats) {
            asset.stats.views = views;
          }
        };
        
        updateViews(state.assets);
        updateViews(state.myAssets);
        updateViews(state.featuredAssets);
        updateViews(state.trendingAssets);
        updateViews(state.recommendedAssets);
        
        if (state.currentAsset?.id === assetId && state.currentAsset.stats) {
          state.currentAsset.stats.views = views;
        }
      });

    // 下载资产
    builder
      .addCase(downloadAsset.pending, (state) => {
        state.isDownloading = true;
        state.error = null;
      })
      .addCase(downloadAsset.fulfilled, (state) => {
        state.isDownloading = false;
        state.error = null;
      })
      .addCase(downloadAsset.rejected, (state, action) => {
        state.isDownloading = false;
        state.error = action.payload as string;
      });

    // 获取分类
    builder
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.categories = action.payload.data;
      });

    // 获取精选资产
    builder
      .addCase(fetchFeaturedAssets.fulfilled, (state, action) => {
        state.featuredAssets = action.payload.data;
      });

    // 获取热门资产
    builder
      .addCase(fetchTrendingAssets.fulfilled, (state, action) => {
        state.trendingAssets = action.payload.data;
      });

    // 获取推荐资产
    builder
      .addCase(fetchRecommendedAssets.fulfilled, (state, action) => {
        state.recommendedAssets = action.payload.data;
      });
  },
});

// 导出actions
export const {
  clearError,
  setSearchParams,
  setFilters,
  resetFilters,
  setPreviewAsset,
  addToRecentlyViewed,
  clearRecentlyViewed,
  addToCart,
  removeFromCart,
  clearCart,
  setUploadProgress,
  setUploadStatus,
  updateStats,
  resetAssets,
} = assetsSlice.actions;

// 选择器
export const selectAssets = (state: { assets: AssetsState }) => state.assets;
export const selectAssetsList = (state: { assets: AssetsState }) => state.assets.assets;
export const selectMyAssets = (state: { assets: AssetsState }) => state.assets.myAssets;
export const selectPurchasedAssets = (state: { assets: AssetsState }) => state.assets.purchasedAssets;
export const selectFeaturedAssets = (state: { assets: AssetsState }) => state.assets.featuredAssets;
export const selectTrendingAssets = (state: { assets: AssetsState }) => state.assets.trendingAssets;
export const selectRecommendedAssets = (state: { assets: AssetsState }) => state.assets.recommendedAssets;
export const selectCurrentAsset = (state: { assets: AssetsState }) => state.assets.currentAsset;
export const selectCategories = (state: { assets: AssetsState }) => state.assets.categories;
export const selectPagination = (state: { assets: AssetsState }) => state.assets.pagination;
export const selectSearchParams = (state: { assets: AssetsState }) => state.assets.searchParams;
export const selectFilters = (state: { assets: AssetsState }) => state.assets.filters;
export const selectIsLoading = (state: { assets: AssetsState }) => state.assets.isLoading;
export const selectIsLoadingMore = (state: { assets: AssetsState }) => state.assets.isLoadingMore;
export const selectError = (state: { assets: AssetsState }) => state.assets.error;
export const selectPreviewAsset = (state: { assets: AssetsState }) => state.assets.previewAsset;
export const selectRecentlyViewed = (state: { assets: AssetsState }) => state.assets.recentlyViewed;
export const selectCart = (state: { assets: AssetsState }) => state.assets.cart;
export const selectLikedAssets = (state: { assets: AssetsState }) => state.assets.likedAssets;
export const selectUploadProgress = (state: { assets: AssetsState }) => state.assets.uploadProgress;
export const selectUploadStatus = (state: { assets: AssetsState }) => state.assets.uploadStatus;
export const selectStats = (state: { assets: AssetsState }) => state.assets.stats;

// 导出reducer
export default assetsSlice.reducer;