import { WithdrawalRequest, BalanceResponse, UserEarningsStats, PlatformStats, PaginationQuery } from '../types';
export declare class FinanceService {
    private db;
    constructor();
    createWithdrawalRequest(userId: number, amount: number, paypalEmail: string): Promise<WithdrawalRequest>;
    getUserWithdrawalRequests(userId: number, query: PaginationQuery): Promise<{
        withdrawals: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getWithdrawalById(withdrawalId: number): Promise<WithdrawalRequest>;
    getUserEarnings(userId: number): Promise<UserEarningsStats>;
    getUserBalance(userId: number): Promise<BalanceResponse>;
    getAllWithdrawalRequests(query: PaginationQuery & {
        status?: string;
    }): Promise<{
        withdrawals: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    approveWithdrawal(withdrawalId: number, adminId: number, adminNotes?: string): Promise<void>;
    rejectWithdrawal(withdrawalId: number, adminId: number, rejectionReason: string): Promise<void>;
    getPlatformStats(): Promise<PlatformStats>;
}
export default FinanceService;
//# sourceMappingURL=finance.service.d.ts.map