"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_controller_1 = require("../controllers/user.controller");
const auth_middleware_1 = __importDefault(require("../middlewares/auth.middleware"));
const router = (0, express_1.Router)();
const userController = new user_controller_1.UserController();
router.use(auth_middleware_1.default.authenticate);
router.get('/profile', userController.getProfile);
router.put('/profile', userController.updateProfile);
router.get('/balance', userController.getBalance);
router.get('/earnings', userController.getEarningsStats);
router.get('/purchase-history', userController.getPurchaseHistory);
router.get('/sales-history', userController.getSalesHistory);
router.get('/', auth_middleware_1.default.requireAdmin, userController.getUserList);
router.get('/:id', auth_middleware_1.default.requireAdmin, userController.getUserById);
router.put('/:id/status', auth_middleware_1.default.requireAdmin, userController.updateUserStatus);
router.delete('/:id', auth_middleware_1.default.requireAdmin, userController.deleteUser);
exports.default = router;
//# sourceMappingURL=user.routes.js.map