global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'aigc-hub'
    replica: 'prometheus-1'

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus自监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s
    metrics_path: /metrics

  # Node Exporter - 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    metrics_path: /metrics

  # cAdvisor - 容器监控
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
    metrics_path: /metrics

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s
    metrics_path: /metrics

  # PostgreSQL监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 15s
    metrics_path: /metrics

  # 后端API监控
  - job_name: 'backend-api'
    static_configs:
      - targets: ['backend:3000']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 15s
    metrics_path: /nginx_status
    scrape_timeout: 5s

  # Alertmanager监控
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 15s
    metrics_path: /metrics

  # Grafana监控
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 15s
    metrics_path: /metrics

  # Elasticsearch监控
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']
    scrape_interval: 30s
    metrics_path: /_prometheus/metrics
    scrape_timeout: 10s

  # Jaeger监控
  - job_name: 'jaeger'
    static_configs:
      - targets: ['jaeger:14269']
    scrape_interval: 15s
    metrics_path: /metrics

  # 动态服务发现配置
  - job_name: 'docker-services'
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 30s
        port: 9090
    relabel_configs:
      - source_labels: [__meta_docker_container_name]
        target_label: container_name
      - source_labels: [__meta_docker_container_id]
        target_label: container_id
      - source_labels: [__meta_docker_container_label_prometheus_job]
        target_label: __job_override
        regex: (.+)
      - source_labels: [__job_override]
        target_label: job
      - source_labels: [__meta_docker_container_label_prometheus_port]
        target_label: __port_override
        regex: (.+)
      - source_labels: [__address__, __port_override]
        target_label: __address__
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2

  # 健康检查端点
  - job_name: 'health-checks'
    static_configs:
      - targets: 
        - 'backend:3000'
        - 'nginx:80'
    metrics_path: /health
    scrape_interval: 30s
    scrape_timeout: 10s
    params:
      format: ['prometheus']

  # 业务指标监控
  - job_name: 'business-metrics'
    static_configs:
      - targets: ['backend:3000']
    metrics_path: /api/metrics
    scrape_interval: 30s
    scrape_timeout: 10s
    basic_auth:
      username: 'metrics'
      password: 'metrics_password'

  # 外部服务监控
  - job_name: 'external-services'
    static_configs:
      - targets: 
        - 'httpbin.org'
        - 'jsonplaceholder.typicode.com'
    metrics_path: /
    scrape_interval: 60s
    scrape_timeout: 10s
    scheme: https

# 远程存储配置（可选）
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint.com/api/v1/write"
#     basic_auth:
#       username: "remote_write_username"
#       password: "remote_write_password"

# remote_read:
#   - url: "https://prometheus-remote-read-endpoint.com/api/v1/read"
#     basic_auth:
#       username: "remote_read_username"
#       password: "remote_read_password"