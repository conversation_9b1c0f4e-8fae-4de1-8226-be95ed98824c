/* AIGC Service Hub - 品牌色彩变量 */

:root {
  /* 品牌主色 */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* 品牌次要色 */
  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;

  /* 语义化颜色 */
  --color-success-50: #ecfdf5;
  --color-success-100: #d1fae5;
  --color-success-200: #a7f3d0;
  --color-success-300: #6ee7b7;
  --color-success-400: #34d399;
  --color-success-500: #10b981;
  --color-success-600: #059669;
  --color-success-700: #047857;
  --color-success-800: #065f46;
  --color-success-900: #064e3b;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;

  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;

  --color-info-50: #eff6ff;
  --color-info-100: #dbeafe;
  --color-info-200: #bfdbfe;
  --color-info-300: #93c5fd;
  --color-info-400: #60a5fa;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;
  --color-info-800: #1e40af;
  --color-info-900: #1e3a8a;

  /* 中性色 */
  --color-neutral-50: #f9fafb;
  --color-neutral-100: #f3f4f6;
  --color-neutral-200: #e5e7eb;
  --color-neutral-300: #d1d5db;
  --color-neutral-400: #9ca3af;
  --color-neutral-500: #6b7280;
  --color-neutral-600: #4b5563;
  --color-neutral-700: #374151;
  --color-neutral-800: #1f2937;
  --color-neutral-900: #111827;

  /* 背景色 */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-bg-tertiary: #f3f4f6;
  --color-bg-dark: #1f2937;
  --color-bg-overlay: rgba(0, 0, 0, 0.5);

  /* 边框色 */
  --color-border-light: #e5e7eb;
  --color-border-medium: #d1d5db;
  --color-border-dark: #9ca3af;

  /* 文本色 */
  --color-text-primary: #111827;
  --color-text-secondary: #374151;
  --color-text-tertiary: #6b7280;
  --color-text-inverse: #ffffff;
  --color-text-muted: #9ca3af;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warning: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);
  --gradient-error: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  --gradient-dark: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  /* 缓动函数 */
  --easing-ease: ease;
  --easing-ease-in: ease-in;
  --easing-ease-out: ease-out;
  --easing-ease-in-out: ease-in-out;
  --easing-linear: linear;

  /* 透明度 */
  --opacity-0: 0;
  --opacity-5: 0.05;
  --opacity-10: 0.1;
  --opacity-20: 0.2;
  --opacity-25: 0.25;
  --opacity-30: 0.3;
  --opacity-40: 0.4;
  --opacity-50: 0.5;
  --opacity-60: 0.6;
  --opacity-70: 0.7;
  --opacity-75: 0.75;
  --opacity-80: 0.8;
  --opacity-90: 0.9;
  --opacity-95: 0.95;
  --opacity-100: 1;
}

/* 深色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-bg-primary: #1f2937;
    --color-bg-secondary: #374151;
    --color-bg-tertiary: #4b5563;
    --color-bg-dark: #111827;
    --color-bg-overlay: rgba(255, 255, 255, 0.1);

    --color-text-primary: #ffffff;
    --color-text-secondary: #e5e7eb;
    --color-text-tertiary: #d1d5db;
    --color-text-inverse: #111827;
    --color-text-muted: #9ca3af;

    --color-border-light: #4b5563;
    --color-border-medium: #6b7280;
    --color-border-dark: #9ca3af;
  }
}

/* 实用工具类 */
.text-primary { color: var(--color-primary-600); }
.text-secondary { color: var(--color-secondary-600); }
.text-success { color: var(--color-success-600); }
.text-warning { color: var(--color-warning-600); }
.text-error { color: var(--color-error-600); }
.text-info { color: var(--color-info-600); }

.bg-primary { background-color: var(--color-primary-600); }
.bg-secondary { background-color: var(--color-secondary-600); }
.bg-success { background-color: var(--color-success-600); }
.bg-warning { background-color: var(--color-warning-600); }
.bg-error { background-color: var(--color-error-600); }
.bg-info { background-color: var(--color-info-600); }

.border-primary { border-color: var(--color-primary-600); }
.border-secondary { border-color: var(--color-secondary-600); }
.border-success { border-color: var(--color-success-600); }
.border-warning { border-color: var(--color-warning-600); }
.border-error { border-color: var(--color-error-600); }
.border-info { border-color: var(--color-info-600); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

.gradient-primary { background: var(--gradient-primary); }
.gradient-secondary { background: var(--gradient-secondary); }
.gradient-success { background: var(--gradient-success); }
.gradient-warning { background: var(--gradient-warning); }
.gradient-error { background: var(--gradient-error); }
.gradient-dark { background: var(--gradient-dark); }

/* 状态类 */
.is-loading {
  opacity: var(--opacity-50);
  pointer-events: none;
}

.is-disabled {
  opacity: var(--opacity-50);
  cursor: not-allowed;
}

.is-active {
  color: var(--color-primary-600);
  background-color: var(--color-primary-50);
}

.is-error {
  color: var(--color-error-600);
  border-color: var(--color-error-600);
}

.is-success {
  color: var(--color-success-600);
  border-color: var(--color-success-600);
}

.is-warning {
  color: var(--color-warning-600);
  border-color: var(--color-warning-600);
}

/* 动画类 */
.animate-fade-in {
  animation: fade-in var(--duration-normal) var(--easing-ease-out);
}

.animate-fade-out {
  animation: fade-out var(--duration-normal) var(--easing-ease-out);
}

.animate-slide-in {
  animation: slide-in var(--duration-normal) var(--easing-ease-out);
}

.animate-slide-out {
  animation: slide-out var(--duration-normal) var(--easing-ease-out);
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slide-in {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slide-out {
  from { transform: translateY(0); opacity: 1; }
  to { transform: translateY(-10px); opacity: 0; }
}

/* 响应式断点 */
@media (min-width: 640px) {
  .sm\:text-primary { color: var(--color-primary-600); }
  .sm\:bg-primary { background-color: var(--color-primary-600); }
}

@media (min-width: 768px) {
  .md\:text-primary { color: var(--color-primary-600); }
  .md\:bg-primary { background-color: var(--color-primary-600); }
}

@media (min-width: 1024px) {
  .lg\:text-primary { color: var(--color-primary-600); }
  .lg\:bg-primary { background-color: var(--color-primary-600); }
}

@media (min-width: 1280px) {
  .xl\:text-primary { color: var(--color-primary-600); }
  .xl\:bg-primary { background-color: var(--color-primary-600); }
}

@media (min-width: 1536px) {
  .\32xl\:text-primary { color: var(--color-primary-600); }
  .\32xl\:bg-primary { background-color: var(--color-primary-600); }
}