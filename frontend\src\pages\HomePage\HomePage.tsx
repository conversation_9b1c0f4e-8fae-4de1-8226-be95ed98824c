import React, { useState, useEffect } from 'react';
import { Box, CssBaseline, useTheme, useMediaQuery } from '@mui/material';
import { useTranslation } from 'react-i18next';

// 组件导入
import ModernNavbar from './components/ModernNavbar';
import ModernHeroSection from './components/ModernHeroSection';
import FeaturedAssets from './components/FeaturedAssets';
import ModernAssetGrid from './components/ModernAssetGrid';
import HomeFooter from './components/HomeFooter';

// API和类型导入
import { assetsAPI, searchAPI } from '../../services/api';
import { Asset, AssetSearchParams, PaginationParams } from '../../types';

// Redux相关导入
// import { RootState } from '../../store';

// 扩展的搜索参数类型
type ExtendedSearchParams = AssetSearchParams & PaginationParams;

interface HomePageProps {}

const HomePage: React.FC<HomePageProps> = () => {
  const { t } = useTranslation();
  const theme = useTheme();

  // 响应式断点
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [featuredAssets, setFeaturedAssets] = useState<Asset[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [tags] = useState<any[]>([]);
  const [searchFilters, setSearchFilters] = useState<ExtendedSearchParams>({
    page: 1,
    limit: 20,
    sortBy: 'newest'
  });
  const [hasMore, setHasMore] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // 获取初始数据
  useEffect(() => {
    loadInitialData();
  }, []);

  // 加载初始数据
  const loadInitialData = async () => {
    setLoading(true);
    try {
      // 并行加载数据
      const [assetsResponse, featuredResponse, categoriesResponse] = await Promise.all([
        assetsAPI.getAssets({ page: 1, limit: 20, sortBy: 'newest' }),
        assetsAPI.getFeaturedAssets({ limit: 3 }),
        assetsAPI.getCategories()
      ]);

      setAssets(assetsResponse.data.data.data);
      setFeaturedAssets(featuredResponse.data.data);
      setCategories(categoriesResponse.data.data);

      // 设置分页信息
      const pagination = assetsResponse.data.data.pagination;
      setHasMore(pagination ? pagination.currentPage < pagination.totalPages : false);
    } catch (error) {
      console.error('Failed to load initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载更多资源
  const loadMoreAssets = async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    try {
      const nextPage = searchFilters.page! + 1;
      const response = await assetsAPI.getAssets({
        ...searchFilters,
        page: nextPage
      });

      const newAssets = response.data.data.data;
      setAssets(prev => [...prev, ...newAssets]);
      setSearchFilters(prev => ({ ...prev, page: nextPage }));
      
      const pagination = response.data.data.pagination;
      setHasMore(pagination ? pagination.currentPage < pagination.totalPages : false);
    } catch (error) {
      console.error('Failed to load more assets:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = async (query: string, filters?: ExtendedSearchParams) => {
    setLoading(true);
    setSearchQuery(query);
    
    try {
      const searchParams = {
        query,
        page: 1,
        limit: 20,
        sortBy: 'newest' as const,
        ...filters
      };

      const response = await searchAPI.globalSearch({
        query,
        type: 'assets' as const,
        filters: searchParams
      });

      setAssets(response.data.data.data);
      setSearchFilters(searchParams);
      
      const pagination = response.data.data.pagination;
      setHasMore(pagination ? pagination.currentPage < pagination.totalPages : false);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理过滤器变化
  const handleFiltersChange = async (newFilters: ExtendedSearchParams) => {
    setLoading(true);
    
    try {
      const searchParams = {
        ...searchFilters,
        ...newFilters,
        page: 1
      };

      const response = searchQuery
        ? await searchAPI.globalSearch({
            query: searchQuery,
            type: 'assets' as const,
            filters: searchParams
          })
        : await assetsAPI.getAssets(searchParams);

      setAssets(response.data.data.data);
      setSearchFilters(searchParams);
      
      const pagination = response.data.data.pagination;
      setHasMore(pagination ? pagination.currentPage < pagination.totalPages : false);
    } catch (error) {
      console.error('Filter change failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ 
      minHeight: '100vh',
      backgroundColor: 'background.default',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <CssBaseline />
      
      {/* H1 - 页面头部区域 (156px) */}
      <ModernNavbar
        onSearch={handleSearch}
        cartItemCount={0}
        notificationCount={3}
        user={null}
        onLogin={() => console.log('Login')}
        onRegister={() => console.log('Register')}
        onLogout={() => console.log('Logout')}
        darkMode={false}
        onToggleDarkMode={() => console.log('Toggle dark mode')}
      />

      {/* 主要内容区域 */}
      <Box component="main" sx={{ flexGrow: 1 }}>
        {/* H2 - 主要内容区域 (350px) */}
        <ModernHeroSection />

        {/* H3 - 精选作品区 (180px) */}
        <FeaturedAssets 
          assets={featuredAssets}
          loading={loading}
        />

        {/* H4 - 资源展示区 (自适应高度) */}
        <ModernAssetGrid
          assets={assets}
          loading={loading}
          hasMore={hasMore}
          onLoadMore={loadMoreAssets}
          searchQuery={searchQuery}
          currentFilters={searchFilters}
          onFiltersChange={handleFiltersChange}
          onSearch={handleSearch}
        />
      </Box>

      {/* H5 - 页脚区域 (260px) */}
      <HomeFooter />
    </Box>
  );
};

export default HomePage;
