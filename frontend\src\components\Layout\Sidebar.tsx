import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Collapse,
  Typography,
  Divider,
  Chip,
  useTheme,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Inventory as AssetsIcon,
  TrendingUp as TradingIcon,
  People as UsersIcon,
  Folder as FilesIcon,
  Settings as SettingsIcon,
  ExpandLess,
  ExpandMore,
  Analytics as AnalyticsIcon,
  ShoppingCart as PurchasesIcon,
  Upload as UploadIcon,
  Category as CategoryIcon,
  Assessment as ReportsIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Payment as PaymentIcon,
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

interface SidebarProps {
  onItemClick?: () => void;
}

interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path?: string;
  children?: MenuItem[];
  badge?: string | number;
  divider?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ onItemClick }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const [expandedItems, setExpandedItems] = useState<string[]>(['assets', 'management']);

  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: '仪表板',
      icon: <DashboardIcon />,
      path: '/dashboard',
    },
    {
      id: 'assets',
      label: '资产管理',
      icon: <AssetsIcon />,
      children: [
        {
          id: 'assets-list',
          label: '资产列表',
          icon: <CategoryIcon />,
          path: '/assets',
        },
        {
          id: 'assets-upload',
          label: '上传资产',
          icon: <UploadIcon />,
          path: '/assets/upload',
        },
        {
          id: 'assets-categories',
          label: '分类管理',
          icon: <CategoryIcon />,
          path: '/assets/categories',
        },
      ],
    },
    {
      id: 'trading',
      label: '交易中心',
      icon: <TradingIcon />,
      children: [
        {
          id: 'trading-overview',
          label: '交易概览',
          icon: <AnalyticsIcon />,
          path: '/trading',
        },
        {
          id: 'trading-orders',
          label: '订单管理',
          icon: <PurchasesIcon />,
          path: '/trading/orders',
        },
        {
          id: 'trading-payments',
          label: '支付记录',
          icon: <PaymentIcon />,
          path: '/trading/payments',
        },
      ],
    },
    {
      id: 'divider-1',
      label: '',
      icon: null,
      divider: true,
    },
    {
      id: 'management',
      label: '系统管理',
      icon: <SettingsIcon />,
      children: [
        {
          id: 'users',
          label: '用户管理',
          icon: <UsersIcon />,
          path: '/users',
          badge: '新',
        },
        {
          id: 'files',
          label: '文件管理',
          icon: <FilesIcon />,
          path: '/files',
        },
        {
          id: 'reports',
          label: '报表统计',
          icon: <ReportsIcon />,
          path: '/reports',
        },
        {
          id: 'notifications',
          label: '通知管理',
          icon: <NotificationsIcon />,
          path: '/notifications',
        },
      ],
    },
    {
      id: 'security',
      label: '安全设置',
      icon: <SecurityIcon />,
      path: '/security',
    },
    {
      id: 'settings',
      label: '系统设置',
      icon: <SettingsIcon />,
      path: '/settings',
    },
  ];

  const handleItemClick = (item: MenuItem) => {
    if (item.children) {
      // 切换展开/折叠状态
      setExpandedItems(prev => 
        prev.includes(item.id) 
          ? prev.filter(id => id !== item.id)
          : [...prev, item.id]
      );
    } else if (item.path) {
      // 导航到指定路径
      navigate(item.path);
      onItemClick?.();
    }
  };

  const isItemActive = (path: string) => {
    return location.pathname === path;
  };

  const isParentActive = (item: MenuItem) => {
    if (item.children) {
      return item.children.some(child => child.path && isItemActive(child.path));
    }
    return false;
  };

  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    if (item.divider) {
      return <Divider key={item.id} sx={{ my: 1 }} />;
    }

    const isExpanded = expandedItems.includes(item.id);
    const isActive = item.path ? isItemActive(item.path) : isParentActive(item);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <React.Fragment key={item.id}>
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => handleItemClick(item)}
            selected={isActive}
            sx={{
              pl: 2 + level * 2,
              borderRadius: 1,
              mx: 1,
              mb: 0.5,
              '&.Mui-selected': {
                backgroundColor: theme.palette.primary.main + '20',
                color: theme.palette.primary.main,
                '& .MuiListItemIcon-root': {
                  color: theme.palette.primary.main,
                },
              },
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          >
            {item.icon && (
              <ListItemIcon
                sx={{
                  minWidth: 40,
                  color: isActive ? theme.palette.primary.main : 'inherit',
                }}
              >
                {item.icon}
              </ListItemIcon>
            )}
            <ListItemText
              primary={item.label}
              primaryTypographyProps={{
                fontSize: level === 0 ? '0.875rem' : '0.8rem',
                fontWeight: isActive ? 600 : 500,
              }}
            />
            {item.badge && (
              <Chip
                label={item.badge}
                size="small"
                color="error"
                sx={{ height: 20, fontSize: '0.7rem' }}
              />
            )}
            {hasChildren && (
              isExpanded ? <ExpandLess /> : <ExpandMore />
            )}
          </ListItemButton>
        </ListItem>
        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children!.map(child => renderMenuItem(child, level + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  return (
    <Box
      sx={{
        height: '100vh',
        backgroundColor: 'background.paper',
        borderRight: 1,
        borderColor: 'divider',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Logo区域 */}
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: 1,
          borderColor: 'divider',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontWeight: 700,
            color: 'primary.main',
            fontSize: '1.1rem',
          }}
        >
          AIGC Hub
        </Typography>
      </Box>

      {/* 导航菜单 */}
      <Box sx={{ flex: 1, overflow: 'auto', py: 1 }}>
        <List>
          {menuItems.map(item => renderMenuItem(item))}
        </List>
      </Box>

      {/* 底部信息 */}
      <Box
        sx={{
          p: 2,
          borderTop: 1,
          borderColor: 'divider',
          backgroundColor: 'background.default',
        }}
      >
        <Typography variant="caption" color="text.secondary" display="block">
          版本 v1.0.0
        </Typography>
        <Typography variant="caption" color="text.secondary" display="block">
          © 2024 AIGC Service Hub
        </Typography>
      </Box>
    </Box>
  );
};

export default Sidebar;