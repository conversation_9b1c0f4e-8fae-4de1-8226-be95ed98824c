import { AssetService } from '../../services/asset.service';

// Mock dependencies
jest.mock('../../database/connection');
jest.mock('aws-sdk');

describe('AssetService', () => {
  let assetService: AssetService;
  let mockAssetRepository: any;
  let mockS3Service: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockAssetRepository = {
      create: jest.fn(),
      findById: jest.fn(),
      findByCreator: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findPublished: jest.fn(),
      findByCategory: jest.fn()
    };

    mockS3Service = {
      generatePresignedUploadUrl: jest.fn(),
      generatePresignedDownloadUrl: jest.fn(),
      deleteFile: jest.fn()
    };

    assetService = new AssetService();
    (assetService as any).assetRepository = mockAssetRepository;
    (assetService as any).s3Service = mockS3Service;
  });

  describe('createAsset', () => {
    test('should successfully create a new asset', async () => {
      const assetData = {
        title: 'Test Asset',
        description: 'A test digital asset',
        assetType: 'MODEL',
        priceUsd: 29.99,
        categories: ['AI', 'Machine Learning'],
        styles: ['Modern', 'Technical'],
        creatorId: 'user-123'
      };

      const mockCreatedAsset = {
        id: 'asset-123',
        ...assetData,
        status: 'DRAFT',
        createdAt: new Date(),
        publishedAt: null
      };

      mockAssetRepository.create.mockResolvedValue(mockCreatedAsset);

      const result = await assetService.createAsset(assetData);

      expect(mockAssetRepository.create).toHaveBeenCalledWith({
        ...assetData,
        status: 'DRAFT'
      });
      expect(result).toEqual(mockCreatedAsset);
    });

    test('should validate required fields', async () => {
      const invalidAssetData = {
        description: 'Missing title',
        assetType: 'MODEL',
        priceUsd: 29.99,
        creatorId: 'user-123'
      };

      await expect(assetService.createAsset(invalidAssetData as any))
        .rejects.toThrow('Title is required');
    });

    test('should validate asset type', async () => {
      const assetData = {
        title: 'Test Asset',
        description: 'A test digital asset',
        assetType: 'INVALID_TYPE',
        priceUsd: 29.99,
        creatorId: 'user-123'
      };

      await expect(assetService.createAsset(assetData as any))
        .rejects.toThrow('Invalid asset type');
    });

    test('should validate price', async () => {
      const assetData = {
        title: 'Test Asset',
        description: 'A test digital asset',
        assetType: 'MODEL',
        priceUsd: -10, // Negative price
        creatorId: 'user-123'
      };

      await expect(assetService.createAsset(assetData))
        .rejects.toThrow('Price must be greater than 0');
    });
  });

  describe('generateUploadUrl', () => {
    test('should generate presigned upload URL for valid asset', async () => {
      const assetId = 'asset-123';
      const fileName = 'model.zip';
      const fileSize = 1024 * 1024 * 100; // 100MB

      const mockAsset = {
        id: assetId,
        creatorId: 'user-123',
        status: 'DRAFT'
      };

      const mockPresignedUrl = {
        uploadUrl: 'https://s3.amazonaws.com/bucket/presigned-url',
        fileKey: 'assets/asset-123/model.zip',
        expiresIn: 900
      };

      mockAssetRepository.findById.mockResolvedValue(mockAsset);
      mockS3Service.generatePresignedUploadUrl.mockResolvedValue(mockPresignedUrl);

      const result = await assetService.generateUploadUrl(assetId, fileName, fileSize, 'user-123');

      expect(mockAssetRepository.findById).toHaveBeenCalledWith(assetId);
      expect(mockS3Service.generatePresignedUploadUrl).toHaveBeenCalledWith(
        `assets/${assetId}/${fileName}`,
        fileSize,
        'application/zip'
      );
      expect(result).toEqual(mockPresignedUrl);
    });

    test('should reject upload for non-existent asset', async () => {
      const assetId = 'non-existent';
      const fileName = 'model.zip';
      const fileSize = 1024;

      mockAssetRepository.findById.mockResolvedValue(null);

      await expect(assetService.generateUploadUrl(assetId, fileName, fileSize, 'user-123'))
        .rejects.toThrow('Asset not found');
    });

    test('should reject upload for unauthorized user', async () => {
      const assetId = 'asset-123';
      const fileName = 'model.zip';
      const fileSize = 1024;

      const mockAsset = {
        id: assetId,
        creatorId: 'user-123',
        status: 'DRAFT'
      };

      mockAssetRepository.findById.mockResolvedValue(mockAsset);

      await expect(assetService.generateUploadUrl(assetId, fileName, fileSize, 'user-456'))
        .rejects.toThrow('Unauthorized');
    });

    test('should reject files larger than 30GB', async () => {
      const assetId = 'asset-123';
      const fileName = 'huge-model.zip';
      const fileSize = 32 * 1024 * 1024 * 1024; // 32GB

      const mockAsset = {
        id: assetId,
        creatorId: 'user-123',
        status: 'DRAFT'
      };

      mockAssetRepository.findById.mockResolvedValue(mockAsset);

      await expect(assetService.generateUploadUrl(assetId, fileName, fileSize, 'user-123'))
        .rejects.toThrow('File size exceeds 30GB limit');
    });
  });

  describe('publishAsset', () => {
    test('should successfully publish a draft asset', async () => {
      const assetId = 'asset-123';
      const userId = 'user-123';

      const mockAsset = {
        id: assetId,
        creatorId: userId,
        status: 'DRAFT',
        s3FileKey: 'assets/asset-123/model.zip'
      };

      const mockPublishedAsset = {
        ...mockAsset,
        status: 'PUBLISHED',
        publishedAt: new Date()
      };

      mockAssetRepository.findById.mockResolvedValue(mockAsset);
      mockAssetRepository.update.mockResolvedValue(mockPublishedAsset);

      const result = await assetService.publishAsset(assetId, userId);

      expect(mockAssetRepository.update).toHaveBeenCalledWith(assetId, {
        status: 'PUBLISHED',
        publishedAt: expect.any(Date)
      });
      expect(result.status).toBe('PUBLISHED');
    });

    test('should reject publishing asset without uploaded file', async () => {
      const assetId = 'asset-123';
      const userId = 'user-123';

      const mockAsset = {
        id: assetId,
        creatorId: userId,
        status: 'DRAFT',
        s3FileKey: null
      };

      mockAssetRepository.findById.mockResolvedValue(mockAsset);

      await expect(assetService.publishAsset(assetId, userId))
        .rejects.toThrow('Asset file must be uploaded before publishing');
    });

    test('should reject publishing already published asset', async () => {
      const assetId = 'asset-123';
      const userId = 'user-123';

      const mockAsset = {
        id: assetId,
        creatorId: userId,
        status: 'PUBLISHED',
        s3FileKey: 'assets/asset-123/model.zip'
      };

      mockAssetRepository.findById.mockResolvedValue(mockAsset);

      await expect(assetService.publishAsset(assetId, userId))
        .rejects.toThrow('Asset is already published');
    });
  });

  describe('generateDownloadUrl', () => {
    test('should generate download URL for purchased asset', async () => {
      const assetId = 'asset-123';
      const userId = 'buyer-456';

      const mockAsset = {
        id: assetId,
        s3FileKey: 'assets/asset-123/model.zip',
        status: 'PUBLISHED'
      };

      const mockDownloadUrl = {
        downloadUrl: 'https://s3.amazonaws.com/bucket/download-url',
        expiresIn: 300
      };

      mockAssetRepository.findById.mockResolvedValue(mockAsset);
      // Mock purchase verification
      (assetService as any).verifyPurchase = jest.fn().mockResolvedValue(true);
      mockS3Service.generatePresignedDownloadUrl.mockResolvedValue(mockDownloadUrl);

      const result = await assetService.generateDownloadUrl(assetId, userId);

      expect(result).toEqual(mockDownloadUrl);
    });

    test('should reject download for non-purchased asset', async () => {
      const assetId = 'asset-123';
      const userId = 'non-buyer-789';

      const mockAsset = {
        id: assetId,
        s3FileKey: 'assets/asset-123/model.zip',
        status: 'PUBLISHED'
      };

      mockAssetRepository.findById.mockResolvedValue(mockAsset);
      (assetService as any).verifyPurchase = jest.fn().mockResolvedValue(false);

      await expect(assetService.generateDownloadUrl(assetId, userId))
        .rejects.toThrow('Asset not purchased');
    });
  });

  describe('searchAssets', () => {
    test('should search assets by title and description', async () => {
      const searchQuery = 'machine learning';
      const filters = {
        assetType: 'MODEL',
        categories: ['AI'],
        priceRange: { min: 0, max: 100 }
      };

      const mockSearchResults = [
        {
          id: 'asset-1',
          title: 'Machine Learning Model',
          description: 'Advanced ML model',
          assetType: 'MODEL',
          priceUsd: 49.99
        },
        {
          id: 'asset-2',
          title: 'AI Training Dataset',
          description: 'Machine learning dataset',
          assetType: 'MODEL',
          priceUsd: 29.99
        }
      ];

      (assetService as any).searchRepository = {
        search: jest.fn().mockResolvedValue(mockSearchResults)
      };

      const result = await assetService.searchAssets(searchQuery, filters);

      expect(result).toEqual(mockSearchResults);
    });
  });
});
