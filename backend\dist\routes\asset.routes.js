"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const asset_controller_1 = require("../controllers/asset.controller");
const auth_middleware_1 = __importDefault(require("../middlewares/auth.middleware"));
const router = (0, express_1.Router)();
const assetController = new asset_controller_1.AssetController();
router.get('/', assetController.getAssetList);
router.get('/:id', assetController.getAssetById);
router.get('/:id/stats', assetController.getAssetStats);
router.use(auth_middleware_1.default.authenticate);
router.post('/', auth_middleware_1.default.requireCreator, assetController.createAsset);
router.put('/:id', auth_middleware_1.default.requireCreator, assetController.updateAsset);
router.post('/:id/publish', auth_middleware_1.default.requireCreator, assetController.publishAsset);
router.delete('/:id', auth_middleware_1.default.requireCreator, assetController.deleteAsset);
router.post('/:id/file', auth_middleware_1.default.requireCreator, assetController.updateAssetFile);
router.get('/:id/download', assetController.downloadAsset);
router.get('/creator/:creatorId', assetController.getCreatorAssets);
router.get('/my/assets', assetController.getMyAssets);
exports.default = router;
//# sourceMappingURL=asset.routes.js.map