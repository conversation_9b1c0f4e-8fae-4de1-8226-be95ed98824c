/**
 * AIGC Service Hub - 数据库连接配置
 * 支持 PostgreSQL 和 Redis 连接配置
 */

const { Pool } = require('pg');
const Redis = require('ioredis');

// 环境变量配置
const config = {
  // 数据库连接配置
  database: {
    // PostgreSQL 配置
    postgres: {
      host: process.env.POSTGRES_HOST || 'localhost',
      port: parseInt(process.env.POSTGRES_PORT) || 5432,
      database: process.env.POSTGRES_DB || 'aigc_service_hub',
      user: process.env.POSTGRES_USER || 'postgres',
      password: process.env.POSTGRES_PASSWORD || 'postgres123',
      
      // 连接池配置
      pool: {
        min: parseInt(process.env.DB_POOL_MIN) || 2,
        max: parseInt(process.env.DB_POOL_MAX) || 20,
        idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000,
        connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 2000,
        acquireTimeoutMillis: parseInt(process.env.DB_ACQUIRE_TIMEOUT) || 60000,
      },
      
      // SSL 配置
      ssl: process.env.NODE_ENV === 'production' ? {
        rejectUnauthorized: false,
        ca: process.env.DB_CA_CERT,
        cert: process.env.DB_CLIENT_CERT,
        key: process.env.DB_CLIENT_KEY,
      } : false,
      
      // 应用名称
      application_name: process.env.DB_APPLICATION_NAME || 'aigc-service-hub',
      
      // 查询超时
      query_timeout: parseInt(process.env.DB_QUERY_TIMEOUT) || 30000,
      
      // 语句超时
      statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT) || 30000,
      
      // 空闲事务超时
      idle_in_transaction_session_timeout: parseInt(process.env.DB_IDLE_TRANSACTION_TIMEOUT) || 30000,
    },
    
    // Redis 配置
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD || null,
      db: parseInt(process.env.REDIS_DB) || 0,
      
      // 连接配置
      connectTimeout: parseInt(process.env.REDIS_CONNECT_TIMEOUT) || 10000,
      commandTimeout: parseInt(process.env.REDIS_COMMAND_TIMEOUT) || 5000,
      lazyConnect: true,
      keepAlive: parseInt(process.env.REDIS_KEEP_ALIVE) || 30000,
      
      // 重试策略
      retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY) || 100,
      maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES) || 3,
      
      // 键前缀
      keyPrefix: process.env.REDIS_KEY_PREFIX || 'aigc:',
      
      // 集群配置（如果使用 Redis 集群）
      cluster: process.env.REDIS_CLUSTER === 'true' ? {
        nodes: process.env.REDIS_CLUSTER_NODES?.split(',').map(node => {
          const [host, port] = node.split(':');
          return { host, port: parseInt(port) };
        }) || [],
        options: {
          redisOptions: {
            password: process.env.REDIS_PASSWORD,
          },
        },
      } : null,
    },
  },
  
  // 应用配置
  app: {
    env: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT) || 3000,
    logLevel: process.env.LOG_LEVEL || 'info',
  },
  
  // 缓存配置
  cache: {
    // 默认TTL配置
    defaultTTL: parseInt(process.env.CACHE_DEFAULT_TTL) || 300, // 5分钟
    
    // 特定缓存TTL配置
    ttl: {
      assets: parseInt(process.env.CACHE_TTL_ASSETS) || 300,
      userProfile: parseInt(process.env.CACHE_TTL_USER_PROFILE) || 600,
      transactions: parseInt(process.env.CACHE_TTL_TRANSACTIONS) || 60,
      systemConfigs: parseInt(process.env.CACHE_TTL_SYSTEM_CONFIGS) || 3600,
      tags: parseInt(process.env.CACHE_TTL_TAGS) || 1800,
    },
  },
  
  // 监控配置
  monitoring: {
    enableMetrics: process.env.ENABLE_METRICS === 'true',
    metricsInterval: parseInt(process.env.METRICS_INTERVAL) || 60000,
  },
};

// 创建 PostgreSQL 连接池
let pgPool = null;

function createPostgresPool() {
  if (pgPool) {
    return pgPool;
  }
  
  const poolConfig = {
    host: config.database.postgres.host,
    port: config.database.postgres.port,
    database: config.database.postgres.database,
    user: config.database.postgres.user,
    password: config.database.postgres.password,
    ssl: config.database.postgres.ssl,
    application_name: config.database.postgres.application_name,
    query_timeout: config.database.postgres.query_timeout,
    statement_timeout: config.database.postgres.statement_timeout,
    idle_in_transaction_session_timeout: config.database.postgres.idle_in_transaction_session_timeout,
    
    // 连接池配置
    min: config.database.postgres.pool.min,
    max: config.database.postgres.pool.max,
    idleTimeoutMillis: config.database.postgres.pool.idleTimeoutMillis,
    connectionTimeoutMillis: config.database.postgres.pool.connectionTimeoutMillis,
    acquireTimeoutMillis: config.database.postgres.pool.acquireTimeoutMillis,
  };
  
  pgPool = new Pool(poolConfig);
  
  // 连接事件监听
  pgPool.on('connect', (client) => {
    console.log('PostgreSQL client connected');
    
    // 设置客户端配置
    client.query('SET statement_timeout = $1', [config.database.postgres.statement_timeout]);
    client.query('SET idle_in_transaction_session_timeout = $1', [config.database.postgres.idle_in_transaction_session_timeout]);
  });
  
  pgPool.on('error', (err, client) => {
    console.error('PostgreSQL client error:', err);
  });
  
  pgPool.on('remove', (client) => {
    console.log('PostgreSQL client removed');
  });
  
  return pgPool;
}

// 创建 Redis 连接
let redisClient = null;

function createRedisClient() {
  if (redisClient) {
    return redisClient;
  }
  
  const redisConfig = {
    host: config.database.redis.host,
    port: config.database.redis.port,
    password: config.database.redis.password,
    db: config.database.redis.db,
    connectTimeout: config.database.redis.connectTimeout,
    commandTimeout: config.database.redis.commandTimeout,
    lazyConnect: config.database.redis.lazyConnect,
    keepAlive: config.database.redis.keepAlive,
    retryDelayOnFailover: config.database.redis.retryDelayOnFailover,
    maxRetriesPerRequest: config.database.redis.maxRetriesPerRequest,
    keyPrefix: config.database.redis.keyPrefix,
  };
  
  // 如果配置了集群，使用集群模式
  if (config.database.redis.cluster) {
    redisClient = new Redis.Cluster(
      config.database.redis.cluster.nodes,
      config.database.redis.cluster.options
    );
  } else {
    redisClient = new Redis(redisConfig);
  }
  
  // Redis 事件监听
  redisClient.on('connect', () => {
    console.log('Redis client connected');
  });
  
  redisClient.on('ready', () => {
    console.log('Redis client ready');
  });
  
  redisClient.on('error', (err) => {
    console.error('Redis client error:', err);
  });
  
  redisClient.on('close', () => {
    console.log('Redis client closed');
  });
  
  redisClient.on('reconnecting', () => {
    console.log('Redis client reconnecting');
  });
  
  return redisClient;
}

// 数据库连接管理器
class DatabaseManager {
  constructor() {
    this.pgPool = null;
    this.redisClient = null;
    this.isConnected = false;
  }
  
  // 初始化数据库连接
  async connect() {
    try {
      // 创建 PostgreSQL 连接池
      this.pgPool = createPostgresPool();
      
      // 测试 PostgreSQL 连接
      const client = await this.pgPool.connect();
      const result = await client.query('SELECT NOW()');
      client.release();
      console.log('PostgreSQL connected successfully at:', result.rows[0].now);
      
      // 创建 Redis 连接
      this.redisClient = createRedisClient();
      
      // 测试 Redis 连接
      await this.redisClient.ping();
      console.log('Redis connected successfully');
      
      this.isConnected = true;
      return true;
    } catch (error) {
      console.error('Database connection failed:', error);
      throw error;
    }
  }
  
  // 关闭数据库连接
  async disconnect() {
    try {
      if (this.pgPool) {
        await this.pgPool.end();
        this.pgPool = null;
      }
      
      if (this.redisClient) {
        await this.redisClient.quit();
        this.redisClient = null;
      }
      
      this.isConnected = false;
      console.log('Database connections closed');
    } catch (error) {
      console.error('Error closing database connections:', error);
      throw error;
    }
  }
  
  // 获取 PostgreSQL 连接
  getPostgresPool() {
    if (!this.pgPool) {
      throw new Error('PostgreSQL pool not initialized');
    }
    return this.pgPool;
  }
  
  // 获取 Redis 连接
  getRedisClient() {
    if (!this.redisClient) {
      throw new Error('Redis client not initialized');
    }
    return this.redisClient;
  }
  
  // 健康检查
  async healthCheck() {
    const health = {
      postgres: false,
      redis: false,
      timestamp: new Date().toISOString(),
    };
    
    try {
      // 检查 PostgreSQL
      if (this.pgPool) {
        const client = await this.pgPool.connect();
        await client.query('SELECT 1');
        client.release();
        health.postgres = true;
      }
      
      // 检查 Redis
      if (this.redisClient) {
        await this.redisClient.ping();
        health.redis = true;
      }
    } catch (error) {
      console.error('Health check failed:', error);
    }
    
    return health;
  }
  
  // 获取连接统计信息
  getConnectionStats() {
    const stats = {
      postgres: {
        totalCount: 0,
        idleCount: 0,
        waitingCount: 0,
      },
      redis: {
        status: 'disconnected',
      },
    };
    
    if (this.pgPool) {
      stats.postgres.totalCount = this.pgPool.totalCount;
      stats.postgres.idleCount = this.pgPool.idleCount;
      stats.postgres.waitingCount = this.pgPool.waitingCount;
    }
    
    if (this.redisClient) {
      stats.redis.status = this.redisClient.status;
    }
    
    return stats;
  }
}

// 创建全局数据库管理器实例
const dbManager = new DatabaseManager();

// 导出配置和实例
module.exports = {
  config,
  DatabaseManager,
  dbManager,
  createPostgresPool,
  createRedisClient,
  
  // 便捷方法
  getDb: () => dbManager.getPostgresPool(),
  getRedis: () => dbManager.getRedisClient(),
  
  // 初始化方法
  init: async () => {
    await dbManager.connect();
    return dbManager;
  },
  
  // 关闭方法
  close: async () => {
    await dbManager.disconnect();
  },
  
  // 健康检查方法
  healthCheck: async () => {
    return await dbManager.healthCheck();
  },
  
  // 获取统计信息
  getStats: () => {
    return dbManager.getConnectionStats();
  },
};

// 优雅关闭处理
process.on('SIGINT', async () => {
  console.log('Received SIGINT, closing database connections...');
  await dbManager.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, closing database connections...');
  await dbManager.disconnect();
  process.exit(0);
});

// 未捕获异常处理
process.on('uncaughtException', async (error) => {
  console.error('Uncaught exception:', error);
  await dbManager.disconnect();
  process.exit(1);
});

process.on('unhandledRejection', async (reason, promise) => {
  console.error('Unhandled rejection at:', promise, 'reason:', reason);
  await dbManager.disconnect();
  process.exit(1);
});