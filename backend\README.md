# AIGC Service Hub Backend API

AIGC Service Hub MVP 1.0 的后端API服务，基于Node.js + TypeScript + Express.js构建。

## 项目概述

这是一个AI创作者服务平台的后端API，支持：
- 用户认证与授权（JWT + OAuth）
- 数字资产管理（上传、发布、交易）
- 财务系统（自动化分佣、提现）
- 文件处理（30GB大文件支持）
- 支付集成（PayPal）
- 积分系统

## 技术栈

- **运行时**: Node.js 18+
- **语言**: TypeScript
- **框架**: Express.js
- **数据库**: PostgreSQL
- **缓存**: Redis  
- **对象存储**: AWS S3
- **支付**: PayPal API
- **邮件**: AWS SES
- **认证**: JWT + OAuth (Google/GitHub)
- **容器化**: Docker

## 项目结构

```
backend/
├── src/
│   ├── config/              # 配置文件
│   │   └── index.ts         # 主配置
│   ├── controllers/         # 控制器层
│   │   ├── auth.controller.ts    # 认证控制器
│   │   ├── user.controller.ts    # 用户控制器
│   │   ├── asset.controller.ts   # 资产控制器
│   │   ├── transaction.controller.ts  # 交易控制器
│   │   ├── finance.controller.ts     # 财务控制器
│   │   └── admin.controller.ts       # 管理员控制器
│   ├── services/            # 业务逻辑层
│   │   ├── auth.service.ts       # 认证服务
│   │   ├── user.service.ts       # 用户服务
│   │   ├── asset.service.ts      # 资产服务
│   │   ├── transaction.service.ts    # 交易服务
│   │   ├── finance.service.ts        # 财务服务
│   │   ├── file.service.ts           # 文件服务
│   │   ├── payment.service.ts        # 支付服务
│   │   ├── commission.service.ts     # 分佣服务
│   │   └── notification.service.ts   # 通知服务
│   ├── middlewares/         # 中间件
│   │   ├── auth.middleware.ts    # 认证中间件
│   │   ├── validation.middleware.ts  # 验证中间件
│   │   └── rateLimit.middleware.ts   # 限流中间件
│   ├── routes/              # 路由定义
│   │   ├── auth.routes.ts        # 认证路由
│   │   ├── user.routes.ts        # 用户路由
│   │   ├── asset.routes.ts       # 资产路由
│   │   ├── transaction.routes.ts     # 交易路由
│   │   ├── finance.routes.ts         # 财务路由
│   │   ├── file.routes.ts            # 文件路由
│   │   └── admin.routes.ts           # 管理员路由
│   ├── database/            # 数据库相关
│   │   ├── connection.ts         # 数据库连接
│   │   ├── migrations/           # 数据库迁移
│   │   └── seeds/               # 数据种子
│   ├── utils/               # 工具函数
│   │   ├── logger.ts            # 日志工具
│   │   ├── errors.ts            # 错误处理
│   │   ├── validation.ts        # 验证工具
│   │   └── helpers.ts           # 助手函数
│   ├── workers/             # 后台任务
│   │   ├── cron.ts             # 定时任务
│   │   └── jobs/               # 异步任务
│   ├── types/               # TypeScript类型定义
│   │   └── index.ts            # 类型定义
│   └── main.ts              # 应用入口
├── tests/                   # 测试文件
├── logs/                    # 日志文件
├── .env.example             # 环境变量示例
├── .eslintrc.js            # ESLint配置
├── .prettierrc             # Prettier配置
├── tsconfig.json           # TypeScript配置
├── package.json            # 依赖配置
└── README.md               # 项目说明
```

## 快速开始

### 1. 环境准备

- Node.js 18+
- PostgreSQL 13+
- Redis 6+
- AWS账户（S3, SES）
- PayPal开发者账户

### 2. 安装依赖

```bash
npm install
```

### 3. 环境配置

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下必要参数：

```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/aigc_service_hub

# Redis配置
REDIS_URL=redis://localhost:6379

# AWS配置
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
S3_PRIVATE_BUCKET=your_private_bucket
S3_PUBLIC_BUCKET=your_public_bucket

# JWT配置
JWT_SECRET=your_jwt_secret_key

# PayPal配置
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
```

### 4. 数据库初始化

```bash
# 运行数据库迁移
npm run db:migrate

# 填充种子数据
npm run db:seed
```

### 5. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm run build
npm start
```

## API文档

### 认证相关

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "displayName": "用户名",
  "userRole": "PERSONAL_CREATOR"
}
```

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 刷新令牌
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "your_refresh_token"
}
```

### 资产管理

#### 获取资产列表
```http
GET /api/assets?page=1&limit=20&category=视频&assetType=MODEL
```

#### 创建资产
```http
POST /api/assets
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "title": "资产标题",
  "description": "资产描述",
  "assetType": "MODEL",
  "priceUsd": 9.99,
  "pricePoints": 1000,
  "categories": ["视频"],
  "styles": ["电商"]
}
```

#### 获取上传URL
```http
GET /api/files/upload-url?fileName=model.zip&fileSize=1024000
Authorization: Bearer your_access_token
```

### 交易系统

#### 购买资产
```http
POST /api/transactions/purchase
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "assetId": 1,
  "currency": "USD"
}
```

#### 获取购买历史
```http
GET /api/users/purchases?page=1&limit=10
Authorization: Bearer your_access_token
```

### 财务系统

#### 获取余额
```http
GET /api/finance/balance
Authorization: Bearer your_access_token
```

#### 申请提现
```http
POST /api/finance/withdrawal
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "amount": 100.00,
  "paypalEmail": "<EMAIL>"
}
```

## 核心功能

### 1. 用户认证系统
- JWT令牌认证
- OAuth集成（Google/GitHub）
- 角色权限管理
- 密码加密存储

### 2. 资产管理系统
- 大文件上传（30GB支持）
- S3预签名URL机制
- 资产分类和标签
- 发布状态管理

### 3. 交易处理系统
- PayPal支付集成
- 积分支付系统
- 交易记录管理
- 安全下载机制

### 4. 财务系统
- 自动化阶梯式分佣
- 7天资金保护期
- 提现管理
- 收益统计报表

### 5. 权限控制系统
- 四种用户角色
- 细粒度权限控制
- 资源访问验证
- 安全审计日志

## 分佣算法

### 个人创作者
- 首次销售：平台5%，创作者95%
- 每次销售递增5%平台分成
- 最高平台分成：50%

### 企业创作者
- 首次销售：平台8%，创作者92%
- 每次销售递增8%平台分成
- 最高平台分成：56%

## 开发指南

### 代码规范
```bash
# 代码检查
npm run lint

# 代码格式化
npm run format

# 类型检查
npm run build
```

### 测试
```bash
# 单元测试
npm test

# 测试覆盖率
npm run test:cov

# 端到端测试
npm run test:e2e
```

### 数据库操作
```bash
# 创建新迁移
npm run db:create migration_name

# 运行迁移
npm run db:migrate

# 回滚迁移
npm run db:rollback

# 检查迁移状态
npm run db:status
```

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t aigc-service-hub-backend .

# 运行容器
docker run -p 3000:3000 aigc-service-hub-backend
```

### 使用Docker Compose
```bash
# 开发环境
docker-compose -f docker-compose.dev.yml up

# 生产环境
docker-compose -f docker-compose.prod.yml up
```

## 监控与日志

### 健康检查
```http
GET /health
```

### 日志级别
- `error`: 错误日志
- `warn`: 警告日志
- `info`: 信息日志
- `debug`: 调试日志

### 性能指标
- 请求响应时间
- 数据库查询性能
- 文件上传/下载速度
- 错误率统计

## 安全考虑

1. **数据保护**
   - 密码加盐哈希
   - JWT令牌安全
   - 环境变量保护

2. **API安全**
   - 输入验证
   - SQL注入防护
   - XSS防护
   - CSRF防护
   - 速率限制

3. **文件安全**
   - 文件类型验证
   - 大小限制
   - 私有存储
   - 访问权限控制

4. **财务安全**
   - 交易完整性
   - 双重确认
   - 审计日志
   - 资金保护期

## 常见问题

### Q: 如何处理30GB大文件上传？
A: 使用AWS S3预签名URL，前端直接上传到S3，避免通过服务器转发。

### Q: 分佣算法如何实现？
A: 基于资产历史销售次数和创作者类型，自动计算分佣比例。

### Q: 如何保证交易安全？
A: 使用PayPal官方API，实现交易状态机和7天资金保护期。

### Q: 如何处理并发请求？
A: 使用数据库事务和Redis锁机制，确保数据一致性。

## 许可证

MIT License

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 联系方式

- 项目仓库: https://github.com/your-org/aigc-service-hub
- 问题反馈: https://github.com/your-org/aigc-service-hub/issues
- 技术支持: <EMAIL>