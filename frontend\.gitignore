# 依赖目录
node_modules/
.pnpm
.yarn/

# 构建输出
dist/
build/
out/
*.tsbuildinfo

# 缓存目录
.next/
.nuxt/
.vuepress/dist/
.temp/
.cache/
.parcel-cache/
.vite/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
*.lcov
.nyc_output

# 测试工具
.eslintcache
.stylelintcache

# 编辑器配置
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp

# 包管理器锁文件 (保留用于生产环境)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# TypeScript
*.tsbuildinfo

# 生成的文件
*.generated.*
*.auto.*

# 第三方库
public/libs/
public/vendors/

# 文档生成
docs/
storybook-static/

# 测试快照
**/__snapshots__/

# 本地配置
.local
*.local

# 数据库
*.db
*.sqlite
*.sqlite3

# 备份文件
*.backup
*.bak
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar

# 分析报告
bundle-analyzer-report.html
stats.json
report.html

# PWA 文件
sw.js
workbox-*.js
precache-manifest.*.js

# 热重载文件
.hot-update.*

# 安全相关文件
*.pem
*.key
*.crt
*.p12

# 本地开发工具
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json

# 测试相关
coverage/
junit.xml
test-results/

# 性能分析
*.cpuprofile
*.heapprofile

# 移动端
.expo/
.expo-shared/

# 其他
*.tgz
*.orig
.turbo