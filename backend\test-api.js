const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testAPI() {
  console.log('🔍 Testing AIGC Service Hub API Endpoints...\n');

  const tests = [
    {
      name: 'Health Check',
      method: 'GET',
      url: '/health',
      expectedStatus: 200
    },
    {
      name: 'Root API',
      method: 'GET', 
      url: '/',
      expectedStatus: 200
    },
    {
      name: 'Check Email Availability',
      method: 'GET',
      url: '/api/auth/check-email?email=<EMAIL>',
      expectedStatus: [200, 404, 500] // Any of these could be valid
    },
    {
      name: 'User Registration',
      method: 'POST',
      url: '/api/auth/register',
      data: {
        email: '<EMAIL>',
        password: 'password123',
        displayName: 'Test User',
        creatorType: 'PERSONAL'
      },
      expectedStatus: [201, 400, 409, 500]
    },
    {
      name: 'User Login (Invalid)',
      method: 'POST',
      url: '/api/auth/login',
      data: {
        email: '<EMAIL>',
        password: 'wrongpassword'
      },
      expectedStatus: [400, 401, 404, 500]
    },
    {
      name: 'Assets List',
      method: 'GET',
      url: '/api/assets',
      expectedStatus: [200, 401, 500]
    },
    {
      name: 'Transactions List',
      method: 'GET',
      url: '/api/transactions',
      expectedStatus: [200, 401, 500]
    }
  ];

  const results = [];

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      
      const config = {
        method: test.method,
        url: `${BASE_URL}${test.url}`,
        timeout: 5000,
        validateStatus: () => true // Don't throw on any status code
      };

      if (test.data) {
        config.data = test.data;
        config.headers = { 'Content-Type': 'application/json' };
      }

      const response = await axios(config);
      
      const expectedStatuses = Array.isArray(test.expectedStatus) 
        ? test.expectedStatus 
        : [test.expectedStatus];
      
      const isSuccess = expectedStatuses.includes(response.status);
      
      results.push({
        test: test.name,
        status: response.status,
        success: isSuccess,
        url: test.url,
        method: test.method,
        response: response.data
      });

      console.log(`  ✅ Status: ${response.status} ${isSuccess ? '(Expected)' : '(Unexpected)'}`);
      
      if (response.data && typeof response.data === 'object') {
        console.log(`  📄 Response: ${JSON.stringify(response.data).substring(0, 100)}...`);
      }
      
    } catch (error) {
      results.push({
        test: test.name,
        status: 'ERROR',
        success: false,
        url: test.url,
        method: test.method,
        error: error.message
      });
      
      console.log(`  ❌ Error: ${error.message}`);
    }
    
    console.log('');
  }

  // Summary
  console.log('\n📊 API Test Results Summary:');
  console.log('=' .repeat(50));
  
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`Total Tests: ${total}`);
  console.log(`Successful: ${successful}`);
  console.log(`Failed: ${total - successful}`);
  console.log(`Success Rate: ${((successful / total) * 100).toFixed(1)}%`);
  
  console.log('\n📋 Detailed Results:');
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.test}: ${result.method} ${result.url} -> ${result.status}`);
    
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });

  // API Endpoint Discovery
  console.log('\n🔍 API Endpoint Discovery:');
  const workingEndpoints = results.filter(r => r.status !== 'ERROR' && r.status !== 404);
  
  if (workingEndpoints.length > 0) {
    console.log('Working endpoints found:');
    workingEndpoints.forEach(endpoint => {
      console.log(`  ${endpoint.method} ${endpoint.url} (Status: ${endpoint.status})`);
    });
  } else {
    console.log('⚠️  No working API endpoints found. This suggests:');
    console.log('   1. API routes may not be properly loaded');
    console.log('   2. Path alias resolution issues');
    console.log('   3. Service dependencies not available');
  }

  return results;
}

// Run the tests
if (require.main === module) {
  testAPI().catch(console.error);
}

module.exports = { testAPI };
