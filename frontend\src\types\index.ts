// 用户相关类型
export interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  role: UserRole;
  userRole: UserRole; // 兼容旧的属性名
  status: UserStatus;
  isEmailVerified: boolean;
  isTwoFactorEnabled: boolean;
  isVerified?: boolean;
  createdAt: string;
  updatedAt: string;
  profile?: UserProfile;
  settings?: UserSettings;
  stats?: UserStats;
}

export enum UserRole {
  USER = 'user',
  CREATOR = 'creator',
  PERSONAL_CREATOR = 'personal_creator',
  ENTERPRISE_CREATOR = 'enterprise_creator',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  BANNED = 'banned',
}

export interface UserProfile {
  id: string;
  userId: string;
  displayName: string;
  avatar?: string;
  bio?: string;
  website?: string;
  location?: string;
  profession?: string;
  skills: string[];
  socialLinks: {
    twitter?: string;
    linkedin?: string;
    github?: string;
    instagram?: string;
    facebook?: string;
    youtube?: string;
    behance?: string;
    dribbble?: string;
  };
  isPublic: boolean;
  showEmail: boolean;
  showStats: boolean;
  coverImage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserSettings {
  id: string;
  userId: string;
  theme: ThemeMode;
  language: string;
  currency: string;
  timezone: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

export interface UserStats {
  totalAssets: number;
  totalSales: number;
  totalEarnings: number;
  totalPurchases: number;
  totalDownloads: number;
  totalViews: number;
  totalLikes: number;
  totalFollowers: number;
  totalFollowing: number;
  rating: number;
  reviewCount: number;
  joinDate: string;
  lastActiveAt: string;
}

export interface NotificationSettings {
  email: {
    newPurchase: boolean;
    newComment: boolean;
    newFollower: boolean;
    newAssetApproval: boolean;
    marketing: boolean;
    newsletter: boolean;
  };
  push: {
    newPurchase: boolean;
    newComment: boolean;
    newFollower: boolean;
    newAssetApproval: boolean;
  };
  sms: {
    security: boolean;
    important: boolean;
  };
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private' | 'followers_only';
  showOnlineStatus: boolean;
  showLastSeen: boolean;
  showEmail: boolean;
  showStats: boolean;
  allowMessages: 'everyone' | 'followers' | 'none';
  allowComments: 'everyone' | 'followers' | 'none';
  blockedUsers: string[];
}

export interface UserPreferences {
  autoPlay: boolean;
  showThumbnails: boolean;
  compactView: boolean;
  showNSFW: boolean;
  defaultSort: 'newest' | 'oldest' | 'popular' | 'trending';
  itemsPerPage: number;
  autoSave: boolean;
  showTutorials: boolean;
}

// 资产相关类型
export interface Asset {
  id: string;
  title: string;
  description: string;
  shortDescription?: string;
  category: AssetCategory;
  type: AssetType;
  tags: string[];
  price: number;
  currency: string;
  isPremium: boolean;
  isFree: boolean;
  isExclusive: boolean;
  thumbnailUrl: string;
  previewUrls: string[];
  files: AssetFile[];
  metadata: AssetMetadata;
  status: AssetStatus;
  visibility: AssetVisibility;
  creatorId: string;
  creator: User;
  stats: AssetStats;
  ratings: AssetRating[];
  comments: AssetComment[];
  versions: AssetVersion[];
  licenses: AssetLicense[];
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  featuredAt?: string;
}

export enum AssetType {
  IMAGE = 'image',
  VECTOR = 'vector',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document',
  TEMPLATE = 'template',
  FONT = 'font',
  ICON = 'icon',
  TEXTURE = 'texture',
  MODEL_3D = '3d_model',
  ANIMATION = 'animation',
  PLUGIN = 'plugin',
  PRESET = 'preset',
  BRUSH = 'brush',
  OTHER = 'other',
}

export interface AssetCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parentId?: string;
  children?: AssetCategory[];
  isActive: boolean;
  sortOrder: number;
  icon?: string;
  color?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AssetFile {
  id: string;
  assetId: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  downloadUrl: string;
  isPreview: boolean;
  isPrimary: boolean;
  sortOrder: number;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface AssetMetadata {
  dimensions?: {
    width: number;
    height: number;
    depth?: number;
  };
  fileFormat: string;
  software?: string[];
  plugins?: string[];
  requirements?: string[];
  compatibility?: string[];
  resolution?: string;
  colorMode?: string;
  fileSize: number;
  duration?: number;
  frameRate?: number;
  bitrate?: number;
  quality?: string;
  version?: string;
  customProperties?: Record<string, any>;
}

export enum AssetStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
}

export enum AssetVisibility {
  PUBLIC = 'public',
  PRIVATE = 'private',
  UNLISTED = 'unlisted',
}

export interface AssetStats {
  views: number;
  downloads: number;
  likes: number;
  comments: number;
  shares: number;
  purchases: number;
  revenue: number;
  avgRating: number;
  totalRatings: number;
  conversionRate: number;
  createdAt: string;
  updatedAt: string;
}

export interface AssetRating {
  id: string;
  assetId: string;
  userId: string;
  user: User;
  rating: number;
  comment?: string;
  isVerifiedPurchase: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AssetComment {
  id: string;
  assetId: string;
  userId: string;
  user: User;
  parentId?: string;
  content: string;
  isEdited: boolean;
  likes: number;
  dislikes: number;
  createdAt: string;
  updatedAt: string;
  replies?: AssetComment[];
}

export interface AssetVersion {
  id: string;
  assetId: string;
  version: string;
  title: string;
  description: string;
  changelog: string;
  files: AssetFile[];
  isLatest: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AssetLicense {
  id: string;
  name: string;
  type: 'standard' | 'extended' | 'exclusive';
  description: string;
  permissions: string[];
  restrictions: string[];
  price: number;
  currency: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 交易相关类型
export interface Transaction {
  id: string;
  type: TransactionType;
  status: TransactionStatus;
  amount: number;
  currency: string;
  fee: number;
  netAmount: number;
  paymentMethod: PaymentMethod;
  paymentIntentId?: string;
  stripePaymentIntentId?: string;
  paypalOrderId?: string;
  buyerId: string;
  buyer: User;
  sellerId?: string;
  seller?: User;
  assets: Asset[];
  metadata: TransactionMetadata;
  refunds: TransactionRefund[];
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  failedAt?: string;
}

export enum TransactionType {
  PURCHASE = 'purchase',
  SALE = 'sale',
  REFUND = 'refund',
  WITHDRAWAL = 'withdrawal',
  DEPOSIT = 'deposit',
  FEE = 'fee',
  COMMISSION = 'commission',
}

export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  DISPUTED = 'disputed',
}

export enum PaymentMethod {
  PAYPAL = 'paypal',
  STRIPE = 'stripe',
  CREDIT_CARD = 'credit_card',
  BANK_TRANSFER = 'bank_transfer',
  DIGITAL_WALLET = 'digital_wallet',
}

export interface TransactionMetadata {
  ipAddress?: string;
  userAgent?: string;
  location?: string;
  deviceInfo?: Record<string, any>;
  referrer?: string;
  couponCode?: string;
  discountAmount?: number;
  taxAmount?: number;
  shippingAmount?: number;
  notes?: string;
  customFields?: Record<string, any>;
}

export interface TransactionRefund {
  id: string;
  transactionId: string;
  amount: number;
  currency: string;
  reason: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  refundId?: string;
  createdAt: string;
  updatedAt: string;
}

// 表单相关类型
export interface LoginForm {
  email: string;
  password: string;
  rememberMe?: boolean;
  twoFactorCode?: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  username: string;
  acceptTerms: boolean;
  subscribeNewsletter?: boolean;
}

export interface PasswordResetForm {
  email: string;
}

export interface PasswordUpdateForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

// 搜索和分页相关类型
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface AssetSearchParams {
  query?: string;
  category?: string;
  type?: AssetType;
  tags?: string[];
  minPrice?: number;
  maxPrice?: number;
  priceMin?: number;
  priceMax?: number;
  isPremium?: boolean;
  isFree?: boolean;
  creatorId?: string;
  sortBy?: 'newest' | 'oldest' | 'popular' | 'trending' | 'price_asc' | 'price_desc' | 'rating' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  dateRange?: {
    start: string;
    end: string;
  };
  fileTypes?: string[];
  software?: string[];
  resolution?: string;
  orientation?: 'landscape' | 'portrait' | 'square';
  color?: string;
  isExclusive?: boolean;
  hasPreview?: boolean;
  license?: string;
}

export interface SearchFilters {
  category?: string;
  tags?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  dateRange?: {
    start: string;
    end: string;
  };
  fileTypes?: string[];
  software?: string[];
  creator?: string;
  isExclusive?: boolean;
  isFree?: boolean;
  isPremium?: boolean;
  rating?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 主题相关类型
export enum ThemeMode {
  LIGHT = 'light',
  DARK = 'dark',
  SYSTEM = 'system',
}

export interface ThemeConfig {
  mode: ThemeMode;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  surfaceColor: string;
  textColor: string;
  borderColor: string;
  shadowColor: string;
  customColors?: Record<string, string>;
  typography?: {
    fontFamily: string;
    fontSize: string;
    lineHeight: string;
    fontWeight: string;
  };
  spacing?: {
    unit: number;
    scale: number[];
  };
  borderRadius?: {
    small: number;
    medium: number;
    large: number;
  };
  shadows?: {
    small: string;
    medium: string;
    large: string;
  };
  animations?: {
    duration: number;
    easing: string;
  };
}

// 通知相关类型
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  userId: string;
  isRead: boolean;
  isArchived: boolean;
  priority: NotificationPriority;
  actionUrl?: string;
  actionText?: string;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

export enum NotificationType {
  PURCHASE = 'purchase',
  SALE = 'sale',
  COMMENT = 'comment',
  LIKE = 'like',
  FOLLOW = 'follow',
  SYSTEM = 'system',
  PROMOTION = 'promotion',
  SECURITY = 'security',
  REMINDER = 'reminder',
  ACHIEVEMENT = 'achievement',
}

export enum NotificationPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

// 上传相关类型
export interface UploadFile {
  id: string;
  name: string;
  size: number;
  type: string;
  lastModified: number;
  progress: number;
  status: UploadStatus;
  error?: string;
  url?: string;
  preview?: string;
  chunks?: UploadChunk[];
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export enum UploadStatus {
  PENDING = 'pending',
  UPLOADING = 'uploading',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export interface UploadChunk {
  index: number;
  size: number;
  status: UploadStatus;
  progress: number;
  etag?: string;
  error?: string;
  retryCount: number;
  uploadedAt?: string;
}

export interface UploadConfig {
  maxFileSize: number;
  maxFiles: number;
  allowedTypes: string[];
  chunkSize: number;
  maxConcurrentUploads: number;
  retryAttempts: number;
  retryDelay: number;
  autoUpload: boolean;
  generateThumbnails: boolean;
  validateFiles: boolean;
  compressImages: boolean;
  endpoint: string;
  headers: Record<string, string>;
}

// API响应类型
export interface ApiResponse<T = any> {
  data: T;
  message: string;
  success: boolean;
  timestamp: string;
  errors?: ApiError[];
}

export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  filters?: SearchFilters;
  sorting?: {
    field: string;
    direction: 'asc' | 'desc';
  };
}

export interface ApiError {
  field?: string;
  message: string;
  code?: string;
  type?: 'validation' | 'authentication' | 'authorization' | 'not_found' | 'server_error';
}

// 通用类型
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  group?: string;
  icon?: string;
  description?: string;
}

export interface MenuItem {
  id: string;
  label: string;
  icon?: string;
  url?: string;
  onClick?: () => void;
  children?: MenuItem[];
  disabled?: boolean;
  badge?: string | number;
  separator?: boolean;
}

export interface BreadcrumbItem {
  label: string;
  url?: string;
  icon?: string;
  onClick?: () => void;
}

export interface TabItem {
  id: string;
  label: string;
  icon?: string;
  content?: any;
  disabled?: boolean;
  badge?: string | number;
  closable?: boolean;
}

export interface ModalConfig {
  title: string;
  content: any;
  width?: number | string;
  height?: number | string;
  closable?: boolean;
  maskClosable?: boolean;
  showFooter?: boolean;
  showHeader?: boolean;
  className?: string;
  onOk?: () => void;
  onCancel?: () => void;
  okText?: string;
  cancelText?: string;
  okButtonProps?: any;
  cancelButtonProps?: any;
}

export interface DrawerConfig {
  title: string;
  content: any;
  width?: number | string;
  height?: number | string;
  placement?: 'left' | 'right' | 'top' | 'bottom';
  closable?: boolean;
  maskClosable?: boolean;
  showHeader?: boolean;
  className?: string;
  onClose?: () => void;
}

export interface ToastConfig {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
  showIcon?: boolean;
  showClose?: boolean;
  persistent?: boolean;
  onClick?: () => void;
  onClose?: () => void;
  actions?: Array<{
    text: string;
    onClick: () => void;
  }>;
}

// 导出所有类型
// 将在后续创建更多类型文件时取消注释
// export type * from './api';
// export type * from './ui';
// export type * from './utils';