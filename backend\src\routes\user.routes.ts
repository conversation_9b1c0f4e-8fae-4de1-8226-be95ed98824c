import { Router } from 'express';
import { UserController } from '@/controllers/user.controller';
import authMiddleware from '@/middlewares/auth.middleware';

const router = Router();
const userController = new UserController();

// 所有用户路由都需要认证
router.use(authMiddleware.authenticate);

// 用户信息路由
router.get('/profile', userController.getProfile);
router.put('/profile', userController.updateProfile);
router.get('/balance', userController.getBalance);
router.get('/earnings', userController.getEarningsStats);

// 用户历史记录
router.get('/purchase-history', userController.getPurchaseHistory);
router.get('/sales-history', userController.getSalesHistory);

// 管理员路由
router.get('/', authMiddleware.requireAdmin, userController.getUserList);
router.get('/:id', authMiddleware.requireAdmin, userController.getUserById);
router.put('/:id/status', authMiddleware.requireAdmin, userController.updateUserStatus);
router.delete('/:id', authMiddleware.requireAdmin, userController.deleteUser);

export default router;