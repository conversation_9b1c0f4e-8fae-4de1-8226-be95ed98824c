"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const finance_controller_1 = require("../controllers/finance.controller");
const auth_middleware_1 = __importDefault(require("../middlewares/auth.middleware"));
const router = (0, express_1.Router)();
const financeController = new finance_controller_1.FinanceController();
router.use(auth_middleware_1.default.authenticate);
router.post('/withdraw', financeController.createWithdrawalRequest);
router.get('/withdrawals', financeController.getWithdrawalRequests);
router.get('/withdrawals/:id', financeController.getWithdrawalById);
router.get('/earnings', financeController.getEarnings);
router.get('/balance', financeController.getBalance);
router.get('/admin/withdrawals', auth_middleware_1.default.requireAdmin, financeController.getAllWithdrawals);
router.put('/admin/withdrawals/:id/approve', auth_middleware_1.default.requireAdmin, financeController.approveWithdrawal);
router.put('/admin/withdrawals/:id/reject', auth_middleware_1.default.requireAdmin, financeController.rejectWithdrawal);
router.get('/admin/stats', auth_middleware_1.default.requireAdmin, financeController.getPlatformStats);
exports.default = router;
//# sourceMappingURL=finance.routes.js.map