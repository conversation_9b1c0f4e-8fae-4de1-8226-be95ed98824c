-- AIGC Service Hub 数据库初始化脚本
-- 创建数据库和基础配置

-- 创建数据库（如果不存在）
-- 注意：这通常在数据库服务器层面执行
-- CREATE DATABASE aigc_service_hub;

-- 使用数据库
-- \c aigc_service_hub;

-- 创建应用用户（生产环境）
-- CREATE USER aigc_app WITH PASSWORD 'your_secure_password';
-- GRANT CONNECT ON DATABASE aigc_service_hub TO aigc_app;

-- 执行Schema创建
-- 如果单独执行此脚本，需要先执行 schema.sql

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description) VALUES
('POINTS_RATE', '100', 'NUMBER', 'Points per USD (100 points = $1)'),
('MAX_FILE_SIZE', '32212254720', 'NUMBER', 'Maximum file size in bytes (30GB)'),
('COMMISSION_INDIVIDUAL_BASE', '5', 'NUMBER', 'Base commission for individual creators (%)'),
('COMMISSION_INDIVIDUAL_INCREMENT', '5', 'NUMBER', 'Commission increment for individual creators (%)'),
('COMMISSION_INDIVIDUAL_MAX', '50', 'NUMBER', 'Maximum commission for individual creators (%)'),
('COMMISSION_ENTERPRISE_BASE', '8', 'NUMBER', 'Base commission for enterprise creators (%)'),
('COMMISSION_ENTERPRISE_INCREMENT', '8', 'NUMBER', 'Commission increment for enterprise creators (%)'),
('COMMISSION_ENTERPRISE_MAX', '56', 'NUMBER', 'Maximum commission for enterprise creators (%)'),
('FUND_CLEARING_DAYS', '7', 'NUMBER', 'Days before funds become available for withdrawal'),
('MIN_WITHDRAWAL_AMOUNT', '10.00', 'NUMBER', 'Minimum withdrawal amount in USD'),
('MAX_WITHDRAWAL_AMOUNT', '10000.00', 'NUMBER', 'Maximum withdrawal amount in USD per request'),
('PLATFORM_NAME', 'AIGC Service Hub', 'STRING', 'Platform display name'),
('PLATFORM_EMAIL', '<EMAIL>', 'STRING', 'Platform email address'),
('SUPPORT_EMAIL', '<EMAIL>', 'STRING', 'Support contact email'),
('MAINTENANCE_MODE', 'false', 'BOOLEAN', 'Enable maintenance mode'),
('ASSET_APPROVAL_REQUIRED', 'false', 'BOOLEAN', 'Require admin approval for asset publishing'),
('MAX_ASSETS_PER_USER', '1000', 'NUMBER', 'Maximum assets per user'),
('DEFAULT_ASSET_QUOTA', '50', 'NUMBER', 'Default asset quota for new users'),
('ENABLE_POINTS_SYSTEM', 'true', 'BOOLEAN', 'Enable points system'),
('ENABLE_USD_PAYMENTS', 'true', 'BOOLEAN', 'Enable USD payments via PayPal'),
('PAYPAL_WEBHOOK_ID', '', 'STRING', 'PayPal webhook ID for payment notifications'),
('AWS_S3_REGION', 'us-west-2', 'STRING', 'AWS S3 region for file storage'),
('CDN_BASE_URL', 'https://cdn.aigcservicehub.com', 'STRING', 'CDN base URL for public assets'),
('RATE_LIMIT_WINDOW', '900', 'NUMBER', 'Rate limit window in seconds (15 minutes)'),
('RATE_LIMIT_MAX_REQUESTS', '100', 'NUMBER', 'Maximum requests per window'),
('JWT_EXPIRES_IN', '86400', 'NUMBER', 'JWT token expiration in seconds (24 hours)'),
('REFRESH_TOKEN_EXPIRES_IN', '604800', 'NUMBER', 'Refresh token expiration in seconds (7 days)'),
('FILE_SCAN_ENABLED', 'true', 'BOOLEAN', 'Enable file scanning for malware'),
('MAX_DOWNLOAD_ATTEMPTS', '5', 'NUMBER', 'Maximum download attempts per asset'),
('DOWNLOAD_LINK_EXPIRES_IN', '300', 'NUMBER', 'Download link expiration in seconds (5 minutes)'),
('BACKUP_RETENTION_DAYS', '30', 'NUMBER', 'Database backup retention in days'),
('LOG_RETENTION_DAYS', '90', 'NUMBER', 'Application log retention in days'),
('ANALYTICS_ENABLED', 'true', 'BOOLEAN', 'Enable analytics tracking'),
('NOTIFICATION_EMAIL_ENABLED', 'true', 'BOOLEAN', 'Enable email notifications'),
('NOTIFICATION_WITHDRAWAL_APPROVED', 'true', 'BOOLEAN', 'Send email when withdrawal is approved'),
('NOTIFICATION_SALE_MADE', 'true', 'BOOLEAN', 'Send email when sale is made'),
('NOTIFICATION_ASSET_PUBLISHED', 'true', 'BOOLEAN', 'Send email when asset is published'),
('TERMS_VERSION', '1.0', 'STRING', 'Current terms of service version'),
('PRIVACY_VERSION', '1.0', 'STRING', 'Current privacy policy version'),
('API_VERSION', 'v1', 'STRING', 'Current API version'),
('FEATURED_ASSETS_COUNT', '12', 'NUMBER', 'Number of featured assets on homepage'),
('SEARCH_RESULTS_PER_PAGE', '20', 'NUMBER', 'Default search results per page'),
('MAX_SEARCH_RESULTS', '500', 'NUMBER', 'Maximum search results returned'),
('CACHE_TTL_ASSETS', '300', 'NUMBER', 'Asset cache TTL in seconds (5 minutes)'),
('CACHE_TTL_USER_PROFILE', '600', 'NUMBER', 'User profile cache TTL in seconds (10 minutes)'),
('CACHE_TTL_TRANSACTIONS', '60', 'NUMBER', 'Transaction cache TTL in seconds (1 minute)');

-- 插入默认管理员用户
-- 密码为 'admin123'，生产环境中应使用更安全的密码
INSERT INTO users (email, password_hash, display_name, user_role, is_active) VALUES
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Admin', 'ADMIN', true);

-- 插入默认标签
INSERT INTO tags (name, type) VALUES
-- 分类标签
('视频生成', 'CATEGORY'),
('图像生成', 'CATEGORY'),
('音频生成', 'CATEGORY'),
('文本生成', 'CATEGORY'),
('3D模型', 'CATEGORY'),
('工作流', 'CATEGORY'),
('提示词', 'CATEGORY'),
('工具插件', 'CATEGORY'),
('训练模型', 'CATEGORY'),
('预处理', 'CATEGORY'),
('后处理', 'CATEGORY'),
('批处理', 'CATEGORY'),

-- 风格标签
('写实风格', 'STYLE'),
('动漫风格', 'STYLE'),
('卡通风格', 'STYLE'),
('艺术风格', 'STYLE'),
('科幻风格', 'STYLE'),
('古典风格', 'STYLE'),
('现代风格', 'STYLE'),
('抽象风格', 'STYLE'),
('商业用途', 'STYLE'),
('个人用途', 'STYLE'),
('教育用途', 'STYLE'),
('创意设计', 'STYLE'),
('建筑设计', 'STYLE'),
('游戏开发', 'STYLE'),
('电影制作', 'STYLE'),
('营销广告', 'STYLE'),
('社交媒体', 'STYLE'),
('电商产品', 'STYLE'),
('UI设计', 'STYLE'),
('品牌设计', 'STYLE');

-- 创建示例创作者用户（测试用）
INSERT INTO users (email, password_hash, display_name, user_role, points_balance, is_active) VALUES
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John Creator', 'PERSONAL_CREATOR', 1000, true),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane Enterprise', 'ENTERPRISE_CREATOR', 2000, true),
('<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Alice Buyer', 'PERSONAL_CREATOR', 5000, true);

-- 创建示例资产（测试用）
INSERT INTO assets (creator_id, title, description, asset_type, price_usd, price_points, s3_file_key, status, published_at) VALUES
(2, 'AI Portrait Generator Model', 'High-quality portrait generation model trained on diverse datasets', 'MODEL', 29.99, 2999, 'uploads/2/portrait-model-v1.zip', 'PUBLISHED', CURRENT_TIMESTAMP),
(2, 'Anime Style LoRA', 'LoRA adapter for anime-style character generation', 'LORA', 9.99, 999, 'uploads/2/anime-lora-v2.safetensors', 'PUBLISHED', CURRENT_TIMESTAMP),
(3, 'ComfyUI Workflow Bundle', 'Complete workflow collection for various AI tasks', 'WORKFLOW', 19.99, 1999, 'uploads/3/workflow-bundle.json', 'PUBLISHED', CURRENT_TIMESTAMP),
(3, 'Marketing Prompts Collection', 'Professional marketing prompt templates', 'PROMPT', 4.99, 499, 'uploads/3/marketing-prompts.txt', 'PUBLISHED', CURRENT_TIMESTAMP),
(2, 'Batch Processing Tool', 'Automated batch processing utility', 'TOOL', 39.99, 3999, 'uploads/2/batch-tool.zip', 'DRAFT', NULL);

-- 为示例资产添加标签
INSERT INTO asset_tags (asset_id, tag_id) VALUES
(1, 2), (1, 13), (1, 19), -- AI Portrait Generator: 图像生成, 写实风格, 商业用途
(2, 2), (2, 14), (2, 18), -- Anime Style LoRA: 图像生成, 动漫风格, 游戏开发
(3, 6), (3, 17), (3, 21), -- ComfyUI Workflow: 工作流, 现代风格, 教育用途
(4, 4), (4, 16), (4, 22), -- Marketing Prompts: 文本生成, 营销广告, 创意设计
(5, 8), (5, 12), (5, 19); -- Batch Processing Tool: 工具插件, 批处理, 商业用途

-- 创建示例交易记录
INSERT INTO transactions (buyer_id, asset_id, currency, amount_usd, amount_points, paypal_transaction_id, status, completed_at) VALUES
(4, 1, 'USD', 29.99, NULL, 'PAYID-TEST-001', 'COMPLETED', CURRENT_TIMESTAMP - INTERVAL '1 day'),
(4, 2, 'POINTS', NULL, 999, NULL, 'COMPLETED', CURRENT_TIMESTAMP - INTERVAL '2 days'),
(4, 3, 'USD', 19.99, NULL, 'PAYID-TEST-002', 'COMPLETED', CURRENT_TIMESTAMP - INTERVAL '3 days');

-- 手动创建账本条目（通常由触发器自动创建）
-- 为USD交易创建分佣记录
INSERT INTO ledger_entries (transaction_id, user_id, amount, entry_type, status, created_at) VALUES
-- 第一笔交易：个人创作者，第一次销售，平台费用5%
(1, 2, 28.49, 'SALE_CREDIT', 'PENDING', CURRENT_TIMESTAMP - INTERVAL '1 day'),
(1, 2, 1.50, 'PLATFORM_FEE', 'AVAILABLE', CURRENT_TIMESTAMP - INTERVAL '1 day'),
-- 第三笔交易：企业创作者，第一次销售，平台费用8%
(3, 3, 18.39, 'SALE_CREDIT', 'PENDING', CURRENT_TIMESTAMP - INTERVAL '3 days'),
(3, 3, 1.60, 'PLATFORM_FEE', 'AVAILABLE', CURRENT_TIMESTAMP - INTERVAL '3 days');

-- 为积分交易创建记录
INSERT INTO ledger_entries (transaction_id, user_id, amount, entry_type, status, created_at) VALUES
(2, 4, 999, 'POINTS_DEBIT', 'AVAILABLE', CURRENT_TIMESTAMP - INTERVAL '2 days');

-- 更新用户积分余额（减去消费的积分）
UPDATE users SET points_balance = points_balance - 999 WHERE id = 4;

-- 更新资产下载次数
UPDATE assets SET download_count = 1 WHERE id IN (1, 2, 3);

-- 创建应用用户权限（生产环境）
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO aigc_app;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO aigc_app;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO aigc_app;

-- 创建只读用户（用于报表和分析）
-- CREATE USER aigc_readonly WITH PASSWORD 'readonly_password';
-- GRANT CONNECT ON DATABASE aigc_service_hub TO aigc_readonly;
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO aigc_readonly;
-- GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO aigc_readonly;

-- 设置连接限制
-- ALTER USER aigc_app CONNECTION LIMIT 50;
-- ALTER USER aigc_readonly CONNECTION LIMIT 10;

-- 创建数据库级别的配置
-- ALTER DATABASE aigc_service_hub SET timezone TO 'UTC';
-- ALTER DATABASE aigc_service_hub SET log_statement TO 'mod';
-- ALTER DATABASE aigc_service_hub SET log_min_duration_statement TO 1000;

-- 分析表统计信息
ANALYZE users;
ANALYZE assets;
ANALYZE transactions;
ANALYZE ledger_entries;
ANALYZE withdrawal_requests;
ANALYZE system_configs;
ANALYZE tags;
ANALYZE asset_tags;

-- 显示初始化完成信息
SELECT 
    'Database initialization completed successfully' as status,
    (SELECT COUNT(*) FROM users) as total_users,
    (SELECT COUNT(*) FROM assets) as total_assets,
    (SELECT COUNT(*) FROM transactions) as total_transactions,
    (SELECT COUNT(*) FROM system_configs) as total_configs,
    (SELECT COUNT(*) FROM tags) as total_tags;

-- 显示系统配置概览
SELECT 
    config_key,
    config_value,
    config_type,
    description
FROM system_configs 
WHERE config_key IN (
    'POINTS_RATE',
    'MAX_FILE_SIZE',
    'COMMISSION_INDIVIDUAL_BASE',
    'COMMISSION_ENTERPRISE_BASE',
    'FUND_CLEARING_DAYS',
    'MIN_WITHDRAWAL_AMOUNT'
)
ORDER BY config_key;