# 多阶段构建
FROM node:18-alpine AS builder

# 安装构建工具
RUN apk add --no-cache python3 make g++

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装所有依赖（包括 devDependencies 用于构建）
RUN npm install

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产环境镜像
FROM node:18-alpine AS production

# 安装构建工具和 curl（为了重新编译 bcrypt 和健康检查）
RUN apk add --no-cache python3 make g++ curl

# 设置工作目录
WORKDIR /app

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 复制构建后的文件和包文件
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/tsconfig.json ./

# 只安装生产依赖并强制重新编译 bcrypt
RUN npm install --production && \
    npm rebuild bcrypt --build-from-source

# 创建日志目录并设置权限
RUN mkdir -p logs && \
    chown -R nodejs:nodejs /app

# 切换到非root用户
USER nodejs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 启动应用
CMD ["node", "-r", "tsconfig-paths/register", "dist/main.js"]