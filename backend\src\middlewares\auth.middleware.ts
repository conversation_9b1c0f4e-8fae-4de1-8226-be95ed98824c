import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import config from '@/config';
import { AuthService } from '@/services/auth.service';
import {
  AuthenticationError,
  AuthorizationError,
  InvalidTokenError,
  TokenExpiredError,
  ValidationError
} from '@/utils/errors';
import { User, UserRole, JwtPayload, Permission } from '@/types';
import { logger, logSecurityEvent } from '@/utils/logger';

// 权限矩阵
const ROLE_PERMISSIONS = {
  [UserRole.PERSONAL_CREATOR]: [
    Permission.READ_PUBLIC,
    Permission.READ_PRIVATE,
    Permission.WRITE_ASSET,
    Permission.FINANCE_WITHDRAW,
  ],
  [UserRole.ENTERPRISE_CREATOR]: [
    Permission.READ_PUBLIC,
    Permission.READ_PRIVATE,
    Permission.WRITE_ASSET,
    Permission.FINANCE_WITHDRAW,
  ],
  [UserRole.ADMIN]: [
    Permission.READ_PUBLIC,
    Permission.READ_PRIVATE,
    Permission.WRITE_ASSET,
    Permission.ADMIN_PANEL,
  ],
};

// 扩展Request类型
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

class AuthMiddleware {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  // JWT认证中间件
  authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new AuthenticationError('No token provided');
      }

      const token = authHeader.substring(7);
      
      if (!token) {
        throw new AuthenticationError('No token provided');
      }

      // 验证token
      const user = await this.authService.verifyAccessToken(token);
      
      if (!user) {
        throw new AuthenticationError('Invalid token');
      }

      // 将用户信息附加到请求对象
      req.user = user;
      next();
    } catch (error) {
      logSecurityEvent('AUTH_FAILED', undefined, { 
        ip: req.ip, 
        userAgent: req.get('User-Agent'),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      if (error instanceof TokenExpiredError || error instanceof InvalidTokenError) {
        next(error);
      } else {
        next(new AuthenticationError('Authentication failed'));
      }
    }
  };

  // 可选认证中间件（不要求必须登录）
  optionalAuthenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        
        if (token) {
          const user = await this.authService.verifyAccessToken(token);
          if (user) {
            req.user = user;
          }
        }
      }
      
      next();
    } catch (error) {
      // 可选认证失败时不抛出错误，继续处理请求
      next();
    }
  };

  // 权限验证中间件
  authorize = (requiredPermissions: Permission[]): ((req: Request, res: Response, next: NextFunction) => void) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        if (!req.user) {
          throw new AuthenticationError('User not authenticated');
        }

        const userPermissions = ROLE_PERMISSIONS[req.user.userRole] || [];
        
        // 检查用户是否有所需的权限
        const hasPermission = requiredPermissions.every(permission => 
          userPermissions.includes(permission)
        );

        if (!hasPermission) {
          logSecurityEvent('AUTHORIZATION_FAILED', req.user.id, {
            requiredPermissions,
            userRole: req.user.userRole,
            ip: req.ip,
          });
          throw new AuthorizationError('Insufficient permissions');
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  };

  // 角色验证中间件
  requireRole = (allowedRoles: UserRole[]): ((req: Request, res: Response, next: NextFunction) => void) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        if (!req.user) {
          throw new AuthenticationError('User not authenticated');
        }

        if (!allowedRoles.includes(req.user.userRole)) {
          logSecurityEvent('ROLE_ACCESS_DENIED', req.user.id, {
            userRole: req.user.userRole,
            allowedRoles,
            ip: req.ip,
          });
          throw new AuthorizationError(`Access denied. Required roles: ${allowedRoles.join(', ')}`);
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  };

  // 管理员权限验证
  requireAdmin = (req: Request, res: Response, next: NextFunction): void => {
    this.requireRole([UserRole.ADMIN])(req, res, next);
  };

  // 创作者权限验证
  requireCreator = (req: Request, res: Response, next: NextFunction): void => {
    this.requireRole([UserRole.PERSONAL_CREATOR, UserRole.ENTERPRISE_CREATOR])(req, res, next);
  };

  // 资源所有者验证中间件
  requireResourceOwner = (getResourceId: (req: Request) => number, getResourceOwnerId: (req: Request) => Promise<number>): ((req: Request, res: Response, next: NextFunction) => Promise<void>) => {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        if (!req.user) {
          throw new AuthenticationError('User not authenticated');
        }

        // 管理员可以访问所有资源
        if (req.user.userRole === UserRole.ADMIN) {
          return next();
        }

        const resourceId = getResourceId(req);
        const resourceOwnerId = await getResourceOwnerId(req);

        if (req.user.id !== resourceOwnerId) {
          logSecurityEvent('RESOURCE_ACCESS_DENIED', req.user.id, {
            resourceId,
            resourceOwnerId,
            ip: req.ip,
          });
          throw new AuthorizationError('You can only access your own resources');
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  };

  // 用户状态验证中间件
  requireActiveUser = (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      if (!req.user.isActive) {
        logSecurityEvent('INACTIVE_USER_ACCESS', req.user.id, { ip: req.ip });
        throw new AuthorizationError('Account is deactivated');
      }

      next();
    } catch (error) {
      next(error);
    }
  };

  // 速率限制中间件（敏感操作）
  sensitiveOperationRateLimit = (req: Request, res: Response, next: NextFunction): void => {
    // 这里可以实现基于用户的速率限制
    // 暂时使用简单的实现
    next();
  };

  // 验证用户对资产的访问权限
  verifyAssetAccess = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      const assetId = parseInt(req.params.id);
      
      if (isNaN(assetId)) {
        throw new ValidationError('Invalid asset ID');
      }

      // 这里应该调用AssetService来验证访问权限
      // 暂时允许所有已认证用户访问
      next();
    } catch (error) {
      next(error);
    }
  };

  // 验证用户是否购买了资产
  verifyAssetPurchase = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      const assetId = parseInt(req.params.id);
      
      if (isNaN(assetId)) {
        throw new ValidationError('Invalid asset ID');
      }

      // 这里应该调用UserService来验证购买记录
      // 暂时允许所有已认证用户下载
      next();
    } catch (error) {
      next(error);
    }
  };

  // 验证API密钥（如果需要）
  verifyApiKey = (req: Request, res: Response, next: NextFunction): void => {
    try {
      const apiKey = req.headers['x-api-key'];
      
      if (!apiKey) {
        throw new AuthenticationError('API key required');
      }

      // 这里应该验证API密钥
      // 暂时跳过验证
      next();
    } catch (error) {
      next(error);
    }
  };

  // 检查用户是否有特定权限
  hasPermission = (user: User, permission: Permission): boolean => {
    const userPermissions = ROLE_PERMISSIONS[user.userRole] || [];
    return userPermissions.includes(permission);
  };

  // 检查用户是否有特定角色
  hasRole = (user: User, role: UserRole): boolean => {
    return user.userRole === role;
  };

  // 检查用户是否是管理员
  isAdmin = (user: User): boolean => {
    return user.userRole === UserRole.ADMIN;
  };

  // 检查用户是否是创作者
  isCreator = (user: User): boolean => {
    return user.userRole === UserRole.PERSONAL_CREATOR || user.userRole === UserRole.ENTERPRISE_CREATOR;
  };
}

// 导出中间件实例
const authMiddleware = new AuthMiddleware();

export default authMiddleware;
export { AuthMiddleware };