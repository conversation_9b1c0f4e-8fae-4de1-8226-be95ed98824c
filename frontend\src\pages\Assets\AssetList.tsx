import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  IconButton,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Menu,
  ListItemIcon,
  ListItemText,
  Pagination,
  InputAdornment,
  Rating,
  Tooltip,
  Fab,
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  ViewModule as ViewModuleIcon,
  ViewList as ViewListIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Download as DownloadIcon,
  Add as AddIcon,
  CloudUpload as CloudUploadIcon,
  Category as CategoryIcon,
  Star as StarIcon,
  Share as ShareIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface Asset {
  id: string;
  title: string;
  description: string;
  category: string;
  type: string;
  price: number;
  thumbnailUrl: string;
  downloads: number;
  rating: number;
  reviews: number;
  createdAt: string;
  creator: {
    name: string;
    avatar: string;
  };
  tags: string[];
  isPremium: boolean;
  isFree: boolean;
}

const AssetList: React.FC = () => {
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [page, setPage] = useState(1);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [actionMenuAnchor, setActionMenuAnchor] = useState<null | HTMLElement>(null);
  const [actionMenuAsset, setActionMenuAsset] = useState<Asset | null>(null);
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);

  // 模拟数据
  const assets: Asset[] = [
    {
      id: '1',
      title: 'AI生成的现代建筑渲染图',
      description: '高质量的现代建筑3D渲染图，适用于建筑设计和展示',
      category: '3D模型',
      type: 'image',
      price: 299,
      thumbnailUrl: '/assets/thumbnails/building.jpg',
      downloads: 1248,
      rating: 4.8,
      reviews: 156,
      createdAt: '2024-01-15',
      creator: {
        name: '张设计师',
        avatar: '/avatars/creator1.jpg',
      },
      tags: ['建筑', '3D', '现代', '渲染'],
      isPremium: true,
      isFree: false,
    },
    {
      id: '2',
      title: '商业摄影背景音乐包',
      description: '适合商业视频和广告的背景音乐合集',
      category: '音频',
      type: 'audio',
      price: 0,
      thumbnailUrl: '/assets/thumbnails/music.jpg',
      downloads: 2156,
      rating: 4.9,
      reviews: 284,
      createdAt: '2024-01-20',
      creator: {
        name: '音乐制作人',
        avatar: '/avatars/creator2.jpg',
      },
      tags: ['音乐', '商业', '背景', '广告'],
      isPremium: false,
      isFree: true,
    },
    {
      id: '3',
      title: '科技感UI图标集',
      description: '包含200+科技风格图标，矢量格式',
      category: '图标',
      type: 'vector',
      price: 149,
      thumbnailUrl: '/assets/thumbnails/icons.jpg',
      downloads: 892,
      rating: 4.6,
      reviews: 98,
      createdAt: '2024-01-25',
      creator: {
        name: 'UI设计师',
        avatar: '/avatars/creator3.jpg',
      },
      tags: ['图标', '科技', '矢量', 'UI'],
      isPremium: false,
      isFree: false,
    },
    {
      id: '4',
      title: '电影级视频转场特效',
      description: '专业级视频转场效果，支持多种视频编辑软件',
      category: '视频特效',
      type: 'video',
      price: 399,
      thumbnailUrl: '/assets/thumbnails/transitions.jpg',
      downloads: 567,
      rating: 4.7,
      reviews: 67,
      createdAt: '2024-01-30',
      creator: {
        name: '视频制作人',
        avatar: '/avatars/creator4.jpg',
      },
      tags: ['视频', '特效', '转场', '电影'],
      isPremium: true,
      isFree: false,
    },
  ];

  const categories = [
    { value: 'all', label: '全部分类' },
    { value: '3D模型', label: '3D模型' },
    { value: '音频', label: '音频' },
    { value: '图标', label: '图标' },
    { value: '视频特效', label: '视频特效' },
    { value: '图像', label: '图像' },
    { value: '字体', label: '字体' },
    { value: '模板', label: '模板' },
  ];

  const handleActionMenuOpen = (event: React.MouseEvent<HTMLElement>, asset: Asset) => {
    setActionMenuAnchor(event.currentTarget);
    setActionMenuAsset(asset);
  };

  const handleActionMenuClose = () => {
    setActionMenuAnchor(null);
    setActionMenuAsset(null);
  };

  const handleAssetClick = (asset: Asset) => {
    setSelectedAsset(asset);
  };

  const handleUploadClick = () => {
    navigate('/assets/upload');
  };

  const renderAssetCard = (asset: Asset) => (
    <Card
      key={asset.id}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        cursor: 'pointer',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: 4,
        },
      }}
      onClick={() => handleAssetClick(asset)}
    >
      <CardMedia
        component="div"
        sx={{
          height: 200,
          backgroundColor: 'grey.200',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: 8,
            left: 8,
            display: 'flex',
            gap: 1,
          }}
        >
          {asset.isPremium && (
            <Chip
              label="Premium"
              size="small"
              color="warning"
              sx={{ fontSize: '0.7rem' }}
            />
          )}
          {asset.isFree && (
            <Chip
              label="免费"
              size="small"
              color="success"
              sx={{ fontSize: '0.7rem' }}
            />
          )}
        </Box>
        <IconButton
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
            },
          }}
          onClick={(e) => {
            e.stopPropagation();
            handleActionMenuOpen(e, asset);
          }}
        >
          <MoreVertIcon />
        </IconButton>
        <Typography variant="h6" color="text.secondary">
          {asset.title}
        </Typography>
      </CardMedia>
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography variant="h6" gutterBottom noWrap>
          {asset.title}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          {asset.description}
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
          {asset.tags.slice(0, 3).map((tag) => (
            <Chip
              key={tag}
              label={tag}
              size="small"
              variant="outlined"
              sx={{ fontSize: '0.7rem' }}
            />
          ))}
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Rating value={asset.rating} readOnly size="small" />
          <Typography variant="caption" sx={{ ml: 1 }}>
            ({asset.reviews})
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar sx={{ width: 24, height: 24, mr: 1 }}>
              {asset.creator.name[0]}
            </Avatar>
            <Typography variant="caption" color="text.secondary">
              {asset.creator.name}
            </Typography>
          </Box>
          <Typography variant="h6" color="primary" fontWeight="bold">
            {asset.price === 0 ? '免费' : `¥${asset.price}`}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          资产管理
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleUploadClick}
        >
          上传资产
        </Button>
      </Box>

      {/* 搜索和筛选栏 */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, alignItems: 'center' }}>
        <TextField
          placeholder="搜索资产..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{ flex: 1 }}
        />
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>分类</InputLabel>
          <Select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            label="分类"
          >
            {categories.map((category) => (
              <MenuItem key={category.value} value={category.value}>
                {category.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl sx={{ minWidth: 120 }}>
          <InputLabel>排序</InputLabel>
          <Select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            label="排序"
          >
            <MenuItem value="newest">最新</MenuItem>
            <MenuItem value="oldest">最旧</MenuItem>
            <MenuItem value="popular">最受欢迎</MenuItem>
            <MenuItem value="price_low">价格从低到高</MenuItem>
            <MenuItem value="price_high">价格从高到低</MenuItem>
          </Select>
        </FormControl>
        <Button
          variant="outlined"
          startIcon={<FilterIcon />}
          onClick={() => setFilterDialogOpen(true)}
        >
          筛选
        </Button>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton
            color={viewMode === 'grid' ? 'primary' : 'default'}
            onClick={() => setViewMode('grid')}
          >
            <ViewModuleIcon />
          </IconButton>
          <IconButton
            color={viewMode === 'list' ? 'primary' : 'default'}
            onClick={() => setViewMode('list')}
          >
            <ViewListIcon />
          </IconButton>
        </Box>
      </Box>

      {/* 资产列表 */}
      <Grid container spacing={3}>
        {assets.map((asset) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={asset.id}>
            {renderAssetCard(asset)}
          </Grid>
        ))}
      </Grid>

      {/* 分页 */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <Pagination
          count={10}
          page={page}
          onChange={(event, value) => setPage(value)}
          color="primary"
        />
      </Box>

      {/* 操作菜单 */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionMenuClose}
      >
        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <VisibilityIcon />
          </ListItemIcon>
          <ListItemText>查看详情</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <EditIcon />
          </ListItemIcon>
          <ListItemText>编辑</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <ShareIcon />
          </ListItemIcon>
          <ListItemText>分享</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleActionMenuClose}>
          <ListItemIcon>
            <DeleteIcon />
          </ListItemIcon>
          <ListItemText>删除</ListItemText>
        </MenuItem>
      </Menu>

      {/* 上传按钮 */}
      <Fab
        color="primary"
        aria-label="upload"
        sx={{
          position: 'fixed',
          bottom: 24,
          right: 24,
        }}
        onClick={handleUploadClick}
      >
        <CloudUploadIcon />
      </Fab>

      {/* 资产详情对话框 */}
      <Dialog
        open={Boolean(selectedAsset)}
        onClose={() => setSelectedAsset(null)}
        maxWidth="md"
        fullWidth
      >
        {selectedAsset && (
          <>
            <DialogTitle>
              {selectedAsset.title}
            </DialogTitle>
            <DialogContent>
              <Box sx={{ display: 'flex', gap: 3 }}>
                <Box
                  sx={{
                    width: 300,
                    height: 200,
                    backgroundColor: 'grey.200',
                    borderRadius: 1,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="h6" color="text.secondary">
                    {selectedAsset.title}
                  </Typography>
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body1" paragraph>
                    {selectedAsset.description}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    分类: {selectedAsset.category}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    下载量: {selectedAsset.downloads}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Rating value={selectedAsset.rating} readOnly size="small" />
                    <Typography variant="body2" sx={{ ml: 1 }}>
                      {selectedAsset.rating} ({selectedAsset.reviews} 评价)
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                    {selectedAsset.tags.map((tag) => (
                      <Chip
                        key={tag}
                        label={tag}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                  <Typography variant="h5" color="primary" fontWeight="bold">
                    {selectedAsset.price === 0 ? '免费' : `¥${selectedAsset.price}`}
                  </Typography>
                </Box>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setSelectedAsset(null)}>
                关闭
              </Button>
              <Button variant="contained" startIcon={<DownloadIcon />}>
                下载
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default AssetList;