import Joi from 'joi';
export declare const validationRules: {
    email: Joi.StringSchema<string>;
    password: Joi.StringSchema<string>;
    displayName: Joi.StringSchema<string>;
    userRole: Joi.StringSchema<string>;
    assetTitle: Joi.StringSchema<string>;
    assetDescription: Joi.StringSchema<string>;
    assetType: Joi.StringSchema<string>;
    priceUsd: Joi.NumberSchema<number>;
    pricePoints: Joi.NumberSchema<number>;
    tags: Joi.ArraySchema<any[]>;
    fileKey: Joi.StringSchema<string>;
    fileSize: Joi.NumberSchema<number>;
    fileName: Joi.StringSchema<string>;
    currency: Joi.StringSchema<string>;
    withdrawalAmount: Joi.NumberSchema<number>;
    paypalEmail: Joi.StringSchema<string>;
    page: Joi.NumberSchema<number>;
    limit: Joi.NumberSchema<number>;
    sortBy: Joi.StringSchema<string>;
    sortOrder: Jo<PERSON>.StringSchema<string>;
    id: Joi.NumberSchema<number>;
    optionalId: Joi.NumberSchema<number>;
};
export declare const validationSchemas: {
    register: Joi.ObjectSchema<any>;
    login: Joi.ObjectSchema<any>;
    refreshToken: Joi.ObjectSchema<any>;
    createAsset: Joi.ObjectSchema<any>;
    updateAsset: Joi.ObjectSchema<any>;
    confirmUpload: Joi.ObjectSchema<any>;
    purchase: Joi.ObjectSchema<any>;
    withdrawal: Joi.ObjectSchema<any>;
    pagination: Joi.ObjectSchema<any>;
    assetQuery: Joi.ObjectSchema<any>;
    transactionQuery: Joi.ObjectSchema<any>;
    pathId: Joi.ObjectSchema<any>;
    updateProfile: Joi.ObjectSchema<any>;
    adminUpdateUser: Joi.ObjectSchema<any>;
    processWithdrawal: Joi.ObjectSchema<any>;
    refund: Joi.ObjectSchema<any>;
};
export declare const validate: (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => void;
export declare const validateQuery: (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => void;
export declare const validateParams: (schema: Joi.ObjectSchema) => (req: any, res: any, next: any) => void;
export declare const validateData: <T>(schema: Joi.ObjectSchema, data: any) => T;
export declare const validateFileUpload: (req: any, res: any, next: any) => void;
export declare const isValidEmail: (email: string) => boolean;
export declare const isStrongPassword: (password: string) => boolean;
export declare const isValidUrl: (url: string) => boolean;
export declare const sanitizeHtml: (html: string) => string;
export declare const validatePoints: (points: number) => boolean;
export declare const validateAmount: (amount: number) => boolean;
//# sourceMappingURL=validation.d.ts.map