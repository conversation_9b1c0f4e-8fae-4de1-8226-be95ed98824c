apiVersion: 1

# 仪表板提供者配置
providers:
  # 系统监控仪表板
  - name: 'system-dashboards'
    orgId: 1
    folder: 'System Monitoring'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/system

  # 应用监控仪表板
  - name: 'application-dashboards'
    orgId: 1
    folder: 'Application Monitoring'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/application

  # 数据库监控仪表板
  - name: 'database-dashboards'
    orgId: 1
    folder: 'Database Monitoring'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/database

  # 网络监控仪表板
  - name: 'network-dashboards'
    orgId: 1
    folder: 'Network Monitoring'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/network

  # 安全监控仪表板
  - name: 'security-dashboards'
    orgId: 1
    folder: 'Security Monitoring'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/security

  # 业务监控仪表板
  - name: 'business-dashboards'
    orgId: 1
    folder: 'Business Monitoring'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/business

  # 日志监控仪表板
  - name: 'logs-dashboards'
    orgId: 1
    folder: 'Logs Monitoring'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/logs

  # 容器监控仪表板
  - name: 'container-dashboards'
    orgId: 1
    folder: 'Container Monitoring'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/container

  # 性能监控仪表板
  - name: 'performance-dashboards'
    orgId: 1
    folder: 'Performance Monitoring'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/performance

  # 告警监控仪表板
  - name: 'alerting-dashboards'
    orgId: 1
    folder: 'Alerting'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/alerting

  # 自定义仪表板
  - name: 'custom-dashboards'
    orgId: 1
    folder: 'Custom'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/custom

  # 实验性仪表板
  - name: 'experimental-dashboards'
    orgId: 1
    folder: 'Experimental'
    type: file
    disableDeletion: true
    editable: false
    updateIntervalSeconds: 60
    allowUiUpdates: false
    options:
      path: /etc/grafana/provisioning/dashboards/experimental

  # 第三方仪表板
  - name: 'third-party-dashboards'
    orgId: 1
    folder: 'Third Party'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 30
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/third-party

  # 开发环境仪表板
  - name: 'dev-dashboards'
    orgId: 1
    folder: 'Development'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/dev

  # 生产环境仪表板
  - name: 'prod-dashboards'
    orgId: 1
    folder: 'Production'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/prod

  # 测试环境仪表板
  - name: 'test-dashboards'
    orgId: 1
    folder: 'Testing'
    type: file
    disableDeletion: false
    editable: true
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards/test