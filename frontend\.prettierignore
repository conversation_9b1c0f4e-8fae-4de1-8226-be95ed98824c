# 依赖目录
node_modules/
.pnpm
.yarn

# 构建输出
dist/
build/
out/

# 缓存目录
.next/
.nuxt/
.vuepress/dist/
.temp/
.cache/
.parcel-cache/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
*.lcov
.nyc_output

# 测试工具
.eslintcache
.stylelintcache

# 编辑器配置
.vscode/
.idea/
*.swp
*.swo

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp

# 包管理器锁文件
package-lock.json
yarn.lock
pnpm-lock.yaml

# TypeScript
*.tsbuildinfo

# 生成的文件
*.generated.*
*.auto.*

# 第三方库
public/libs/
public/vendors/

# 文档生成
docs/
storybook-static/

# 测试快照
**/__snapshots__/

# 本地配置
.local
*.local

# 数据库
*.db
*.sqlite
*.sqlite3

# 备份文件
*.backup
*.bak
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar

# 媒体文件
*.jpg
*.jpeg
*.png
*.gif
*.svg
*.mp4
*.mp3
*.wav

# 证书文件
*.pem
*.key
*.crt
*.p12

# 分析报告
bundle-analyzer-report.html
stats.json