import { Router } from 'express';
import { TransactionController } from '@/controllers/transaction.controller';
import authMiddleware from '@/middlewares/auth.middleware';

const router = Router();
const transactionController = new TransactionController();

// 所有交易路由都需要认证
router.use(authMiddleware.authenticate);

// 购买相关路由
router.post('/purchase', transactionController.createPurchaseTransaction);
router.post('/purchase/confirm', transactionController.confirmPurchase);
router.post('/purchase/cancel', transactionController.cancelPurchase);

// 交易查询路由
router.get('/my', transactionController.getMyTransactions);
router.get('/:id', transactionController.getTransactionById);
router.get('/:id/status', transactionController.getTransactionStatus);

// 管理员路由
router.get('/', authMiddleware.requireAdmin, transactionController.getAllTransactions);
router.put('/:id/status', authMiddleware.requireAdmin, transactionController.updateTransactionStatus);

export default router;