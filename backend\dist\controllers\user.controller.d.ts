export declare class UserController {
    private userService;
    constructor();
    getProfile: (req: any, res: any, next: any) => void;
    updateProfile: (req: any, res: any, next: any) => void;
    getBalance: (req: any, res: any, next: any) => void;
    getEarningsStats: (req: any, res: any, next: any) => void;
    getPurchaseHistory: (req: any, res: any, next: any) => void;
    getSalesHistory: (req: any, res: any, next: any) => void;
    getUserList: (req: any, res: any, next: any) => void;
    getUserById: (req: any, res: any, next: any) => void;
    updateUserStatus: (req: any, res: any, next: any) => void;
    deleteUser: (req: any, res: any, next: any) => void;
}
export default UserController;
//# sourceMappingURL=user.controller.d.ts.map