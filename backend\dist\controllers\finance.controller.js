"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinanceController = void 0;
const finance_service_1 = require("../services/finance.service");
const errors_1 = require("../utils/errors");
const errors_2 = require("../utils/errors");
class FinanceController {
    constructor() {
        this.createWithdrawalRequest = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const { amount, paypalEmail } = req.body;
            if (!amount || !paypalEmail) {
                throw new errors_1.ValidationError('Amount and PayPal email are required');
            }
            const withdrawalRequest = await this.financeService.createWithdrawalRequest(req.user.id, amount, paypalEmail);
            res.status(201).json({
                success: true,
                data: { withdrawalRequest },
                message: 'Withdrawal request created successfully',
            });
        });
        this.getWithdrawalRequests = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 20;
            const result = await this.financeService.getUserWithdrawalRequests(req.user.id, { page, limit });
            res.json({
                success: true,
                data: result,
                message: 'Withdrawal requests retrieved successfully',
            });
        });
        this.getWithdrawalById = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const withdrawalId = parseInt(req.params.id);
            if (isNaN(withdrawalId)) {
                throw new errors_1.ValidationError('Invalid withdrawal ID');
            }
            const withdrawal = await this.financeService.getWithdrawalById(withdrawalId);
            res.json({
                success: true,
                data: { withdrawal },
                message: 'Withdrawal details retrieved successfully',
            });
        });
        this.getEarnings = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const earnings = await this.financeService.getUserEarnings(req.user.id);
            res.json({
                success: true,
                data: earnings,
                message: 'Earnings retrieved successfully',
            });
        });
        this.getBalance = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const balance = await this.financeService.getUserBalance(req.user.id);
            res.json({
                success: true,
                data: balance,
                message: 'Balance retrieved successfully',
            });
        });
        this.getAllWithdrawals = (0, errors_2.asyncHandler)(async (req, res) => {
            const page = parseInt(req.query.page) || 1;
            const limit = parseInt(req.query.limit) || 20;
            const status = req.query.status;
            const result = await this.financeService.getAllWithdrawalRequests({ page, limit, status });
            res.json({
                success: true,
                data: result,
                message: 'All withdrawal requests retrieved successfully',
            });
        });
        this.approveWithdrawal = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const withdrawalId = parseInt(req.params.id);
            if (isNaN(withdrawalId)) {
                throw new errors_1.ValidationError('Invalid withdrawal ID');
            }
            const { adminNotes } = req.body;
            await this.financeService.approveWithdrawal(withdrawalId, req.user.id, adminNotes);
            res.json({
                success: true,
                message: 'Withdrawal request approved successfully',
            });
        });
        this.rejectWithdrawal = (0, errors_2.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new Error('User not authenticated');
            }
            const withdrawalId = parseInt(req.params.id);
            if (isNaN(withdrawalId)) {
                throw new errors_1.ValidationError('Invalid withdrawal ID');
            }
            const { rejectionReason } = req.body;
            if (!rejectionReason) {
                throw new errors_1.ValidationError('Rejection reason is required');
            }
            await this.financeService.rejectWithdrawal(withdrawalId, req.user.id, rejectionReason);
            res.json({
                success: true,
                message: 'Withdrawal request rejected successfully',
            });
        });
        this.getPlatformStats = (0, errors_2.asyncHandler)(async (req, res) => {
            const stats = await this.financeService.getPlatformStats();
            res.json({
                success: true,
                data: stats,
                message: 'Platform statistics retrieved successfully',
            });
        });
        this.financeService = new finance_service_1.FinanceService();
    }
}
exports.FinanceController = FinanceController;
exports.default = FinanceController;
//# sourceMappingURL=finance.controller.js.map