name: Security Scan

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点运行安全扫描
    - cron: '0 2 * * *'

jobs:
  dependency-scan:
    name: Dependency Vulnerability Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci

    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Run npm audit for backend
      working-directory: ./backend
      run: |
        npm audit --audit-level=moderate --json > backend-audit.json || true
        npm audit --audit-level=moderate

    - name: Run npm audit for frontend
      working-directory: ./frontend
      run: |
        npm audit --audit-level=moderate --json > frontend-audit.json || true
        npm audit --audit-level=moderate

    - name: Upload audit results
      uses: actions/upload-artifact@v3
      with:
        name: audit-reports
        path: |
          backend/backend-audit.json
          frontend/frontend-audit.json

  code-scan:
    name: Static Code Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: javascript, typescript

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2

    - name: Run ESLint Security
      run: |
        npm install -g eslint-plugin-security
        cd backend && npx eslint . --ext .js,.ts --format json -o eslint-security-backend.json || true
        cd ../frontend && npx eslint . --ext .js,.ts,.jsx,.tsx --format json -o eslint-security-frontend.json || true

    - name: Upload ESLint results
      uses: actions/upload-artifact@v3
      with:
        name: eslint-security-reports
        path: |
          backend/eslint-security-backend.json
          frontend/eslint-security-frontend.json

  docker-scan:
    name: Docker Image Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Build backend image
      run: |
        docker build -t aigc-hub-backend:latest ./backend

    - name: Build frontend image
      run: |
        docker build -t aigc-hub-frontend:latest ./frontend

    - name: Run Trivy scan on backend image
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'aigc-hub-backend:latest'
        format: 'sarif'
        output: 'trivy-backend-results.sarif'

    - name: Run Trivy scan on frontend image
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'aigc-hub-frontend:latest'
        format: 'sarif'
        output: 'trivy-frontend-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-backend-results.sarif'
        category: 'trivy-backend'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-frontend-results.sarif'
        category: 'trivy-frontend'

    - name: Run Docker Bench Security
      run: |
        git clone https://github.com/docker/docker-bench-security.git
        cd docker-bench-security
        sudo sh docker-bench-security.sh

  secrets-scan:
    name: Secrets Detection
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Run GitLeaks
      uses: zricethezav/gitleaks-action@v2
      with:
        config-path: .gitleaks.toml

    - name: Run TruffleHog
      uses: trufflesecurity/trufflehog@v3.63.2-beta
      with:
        path: ./
        base: main
        head: HEAD
        extra_args: --debug --only-verified

  infrastructure-scan:
    name: Infrastructure Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Checkov
      uses: bridgecrewio/checkov-action@master
      with:
        directory: .
        framework: dockerfile,docker_compose
        output_format: sarif
        output_file_path: checkov-results.sarif

    - name: Upload Checkov results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: checkov-results.sarif

    - name: Scan Kubernetes manifests
      run: |
        # 如果有Kubernetes配置文件，扫描它们
        if [ -d "k8s" ]; then
          docker run --rm -v $(pwd):/workspace \
            aquasec/kube-bench:latest \
            --config-dir /workspace/k8s \
            --json > kube-bench-results.json
        fi

  penetration-test:
    name: Penetration Testing
    runs-on: ubuntu-latest
    needs: [dependency-scan, code-scan, docker-scan]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Start test environment
      run: |
        cp .env.test .env
        docker-compose -f docker-compose.test.yml up -d
        sleep 30

    - name: Wait for services
      run: |
        chmod +x scripts/wait-for-it.sh
        ./scripts/wait-for-it.sh localhost:8080 --timeout=60

    - name: Run OWASP ZAP Baseline Scan
      uses: zaproxy/action-baseline@v0.10.0
      with:
        target: 'http://localhost:8080'
        rules_file_name: '.zap/rules.tsv'
        cmd_options: '-a'

    - name: Run OWASP ZAP Full Scan
      uses: zaproxy/action-full-scan@v0.8.0
      with:
        target: 'http://localhost:8080'
        rules_file_name: '.zap/rules.tsv'
        cmd_options: '-a'

    - name: Run Nikto Web Scanner
      run: |
        docker run --rm --network host \
          sullo/nikto:latest \
          -h http://localhost:8080 \
          -Format json \
          -output nikto-results.json

    - name: Upload penetration test results
      uses: actions/upload-artifact@v3
      with:
        name: pentest-results
        path: |
          nikto-results.json
          report_html.html
          report_json.json

    - name: Cleanup test environment
      if: always()
      run: docker-compose -f docker-compose.test.yml down -v

  compliance-check:
    name: Compliance Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Check GDPR compliance
      run: |
        # 检查是否有隐私政策文件
        if [ ! -f "docs/privacy-policy.md" ]; then
          echo "Warning: No privacy policy found"
        fi
        
        # 检查数据处理相关代码
        grep -r "personal.*data\|user.*data\|privacy" . --include="*.js" --include="*.ts" > gdpr-check.txt || true

    - name: Check security headers
      run: |
        # 检查Nginx配置中的安全头
        if [ -f "nginx/nginx.conf" ]; then
          grep -E "X-Frame-Options|X-Content-Type-Options|X-XSS-Protection|Strict-Transport-Security" nginx/nginx.conf || \
          echo "Warning: Missing security headers in nginx.conf"
        fi

    - name: Check environment variables
      run: |
        # 检查环境变量文件中的敏感信息
        for file in .env.example .env.development .env.production; do
          if [ -f "$file" ]; then
            if grep -i "password\|secret\|key" "$file" | grep -v "CHANGE_ME\|your_\|example"; then
              echo "Warning: Potential hardcoded secrets in $file"
            fi
          fi
        done

    - name: Generate compliance report
      run: |
        cat > compliance-report.md << EOF
        # Security Compliance Report
        
        Generated: $(date)
        
        ## GDPR Compliance
        - Privacy Policy: $([ -f "docs/privacy-policy.md" ] && echo "✅ Found" || echo "❌ Missing")
        - Data Processing Code: $([ -s "gdpr-check.txt" ] && echo "⚠️ Found references" || echo "✅ No references")
        
        ## Security Headers
        - Nginx Security Headers: $(grep -q "X-Frame-Options" nginx/nginx.conf && echo "✅ Configured" || echo "❌ Missing")
        
        ## Environment Security
        - Environment Variables: $(echo "✅ Checked")
        
        EOF

    - name: Upload compliance report
      uses: actions/upload-artifact@v3
      with:
        name: compliance-report
        path: compliance-report.md

  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, code-scan, docker-scan, secrets-scan, infrastructure-scan, penetration-test, compliance-check]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download all artifacts
      uses: actions/download-artifact@v3

    - name: Generate security report
      run: |
        cat > security-report.md << EOF
        # Security Scan Report
        
        Generated: $(date)
        Repository: ${{ github.repository }}
        Commit: ${{ github.sha }}
        
        ## Scan Results Summary
        
        | Scan Type | Status |
        |-----------|--------|
        | Dependency Scan | ${{ needs.dependency-scan.result }} |
        | Code Analysis | ${{ needs.code-scan.result }} |
        | Docker Scan | ${{ needs.docker-scan.result }} |
        | Secrets Detection | ${{ needs.secrets-scan.result }} |
        | Infrastructure Scan | ${{ needs.infrastructure-scan.result }} |
        | Penetration Test | ${{ needs.penetration-test.result }} |
        | Compliance Check | ${{ needs.compliance-check.result }} |
        
        ## Recommendations
        
        1. Regularly update dependencies to patch security vulnerabilities
        2. Implement security headers in web server configuration
        3. Use secrets management for sensitive configuration
        4. Enable container image scanning in CI/CD pipeline
        5. Conduct regular security audits and penetration testing
        
        EOF

    - name: Upload security report
      uses: actions/upload-artifact@v3
      with:
        name: security-report
        path: security-report.md

    - name: Comment on PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('security-report.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## Security Scan Results\n\n${report}`
          });

    - name: Notify security team
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        channel: '#security'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
        text: '🚨 Security scan failed - immediate attention required'