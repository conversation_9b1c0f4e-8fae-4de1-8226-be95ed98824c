# ===========================================
# AIGC Service Hub MVP 1.0 - 环境变量配置
# ===========================================

# 应用环境配置
NODE_ENV=development
APP_NAME=AIGC Service Hub
API_VERSION=v1

# 服务端口配置
BACKEND_PORT=3000
FRONTEND_PORT=3001
HTTP_PORT=80
HTTPS_PORT=443

# 数据库配置
POSTGRES_DB=aigc_service_hub
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_postgres_password
POSTGRES_PORT=5432
DATABASE_POOL_SIZE=20

# Redis配置
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# JWT配置
JWT_SECRET=your_jwt_secret_key_change_this_in_production_minimum_32_characters
JWT_EXPIRES_IN=1d
REFRESH_TOKEN_EXPIRES_IN=7d

# AWS配置
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
S3_PRIVATE_BUCKET=aigc-private-assets
S3_PUBLIC_BUCKET=aigc-public-assets

# PayPal配置
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_SANDBOX=true
PAYPAL_WEBHOOK_ID=your_paypal_webhook_id

# OAuth配置
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# 邮件配置
FROM_EMAIL=<EMAIL>
SMTP_HOST=email-smtp.us-west-2.amazonaws.com
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASSWORD=your_smtp_password
AWS_SES_REGION=us-west-2

# 应用URL配置
FRONTEND_URL=http://localhost:3001
BACKEND_URL=http://localhost:3000
DOMAIN=localhost

# React应用配置
REACT_APP_API_URL=http://localhost:3000/api/v1
REACT_APP_WS_URL=ws://localhost:3000
REACT_APP_PAYPAL_CLIENT_ID=your_paypal_client_id
REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id
REACT_APP_GITHUB_CLIENT_ID=your_github_client_id
REACT_APP_AWS_REGION=us-west-2
REACT_APP_S3_PUBLIC_BUCKET=aigc-public-assets

# 文件上传配置
MAX_FILE_SIZE=32212254720
UPLOAD_URL_EXPIRES_IN=900
DOWNLOAD_URL_EXPIRES_IN=300

# 缓存配置
CACHE_DEFAULT_TTL=300
CACHE_ASSETS_TTL=300
CACHE_USER_PROFILE_TTL=600

# 速率限制配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 监控配置
ENABLE_METRICS=true
METRICS_INTERVAL=60000

# 系统配置
POINTS_RATE=100
COMMISSION_INDIVIDUAL_BASE=5
COMMISSION_INDIVIDUAL_INCREMENT=5
COMMISSION_INDIVIDUAL_MAX=50
COMMISSION_ENTERPRISE_BASE=8
COMMISSION_ENTERPRISE_INCREMENT=8
COMMISSION_ENTERPRISE_MAX=56

# 财务配置
LEDGER_PENDING_DAYS=7
MIN_WITHDRAWAL_AMOUNT=10.00

# 工具服务端口配置
ADMINER_PORT=8080
REDIS_COMMANDER_PORT=8081
MAILHOG_WEB_PORT=8025
MAILHOG_SMTP_PORT=1025
DOCS_PORT=8082
WEBPACK_DEV_PORT=8083

# 测试环境端口配置
POSTGRES_TEST_PORT=5433
REDIS_TEST_PORT=6380
BACKEND_TEST_PORT=3002
FRONTEND_TEST_PORT=3003
MAILHOG_TEST_WEB_PORT=8026
MAILHOG_TEST_SMTP_PORT=1026

# 监控服务配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3004
POSTGRES_EXPORTER_PORT=9187

# 备份配置
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# SSL配置（生产环境使用）
SSL_CERT_PATH=/etc/nginx/ssl/server.crt
SSL_KEY_PATH=/etc/nginx/ssl/server.key
SSL_DHPARAM_PATH=/etc/nginx/ssl/dhparam.pem

# 安全配置
ENABLE_HTTPS=false
FORCE_HTTPS=false
HSTS_MAX_AGE=31536000
SECURITY_HEADERS=true

# 性能配置
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024
POSTGRES_MAX_CONNECTIONS=100
REDIS_MAX_MEMORY=512mb

# 开发工具配置
ENABLE_SWAGGER=true
ENABLE_CORS=true
ENABLE_MORGAN_LOGGING=true
ENABLE_DEBUG_MODE=false