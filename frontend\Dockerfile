# 多阶段构建 - 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖 (构建阶段需要所有依赖)
RUN npm install --legacy-peer-deps && npm install ajv@8.12.0 --save-dev

# 复制源代码
COPY . .

# 构建参数
ARG REACT_APP_API_URL
ARG REACT_APP_WS_URL
ARG REACT_APP_PAYPAL_CLIENT_ID
ARG REACT_APP_GOOGLE_CLIENT_ID
ARG REACT_APP_GITHUB_CLIENT_ID
ARG REACT_APP_AWS_REGION
ARG REACT_APP_S3_PUBLIC_BUCKET

# 设置环境变量
ENV REACT_APP_API_URL=$REACT_APP_API_URL
ENV REACT_APP_WS_URL=$REACT_APP_WS_URL
ENV REACT_APP_PAYPAL_CLIENT_ID=$REACT_APP_PAYPAL_CLIENT_ID
ENV REACT_APP_GOOGLE_CLIENT_ID=$REACT_APP_GOOGLE_CLIENT_ID
ENV REACT_APP_GITHUB_CLIENT_ID=$REACT_APP_GITHUB_CLIENT_ID
ENV REACT_APP_AWS_REGION=$REACT_APP_AWS_REGION
ENV REACT_APP_S3_PUBLIC_BUCKET=$REACT_APP_S3_PUBLIC_BUCKET

# 构建应用 (禁用 ESLint 检查)
ENV ESLINT_NO_DEV_ERRORS=true
ENV DISABLE_ESLINT_PLUGIN=true
ENV TSC_COMPILE_ON_ERROR=true
ENV GENERATE_SOURCEMAP=false
ENV SKIP_PREFLIGHT_CHECK=true
RUN npm run build

# 生产环境镜像
FROM nginx:alpine AS production

# 安装curl用于健康检查
RUN apk add --no-cache curl

# 复制构建后的文件
COPY --from=builder /app/build /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 使用已存在的nginx用户，或创建新的webapp用户
RUN addgroup -g 1001 -S webapp 2>/dev/null || true && \
    adduser -S webapp -u 1001 -G webapp 2>/dev/null || adduser -S webapp -u 1001

# 设置权限
RUN chown -R webapp:webapp /usr/share/nginx/html && \
    chown -R webapp:webapp /var/cache/nginx && \
    chown -R webapp:webapp /var/log/nginx && \
    chown -R webapp:webapp /etc/nginx/conf.d && \
    touch /var/run/nginx.pid && \
    chown -R webapp:webapp /var/run/nginx.pid

# 切换到非root用户
USER webapp

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80 || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]