{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 11:58:26"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 11:58:26"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 11:58:26"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:00:15"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:00:15"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:00:15"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:01:02"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:01:02"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:01:02"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:06:23"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:06:23"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:06:23"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:11:01"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:12:32"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:14:25"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:17:35"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:17:35"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:26"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:27"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:49"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:49"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:50"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:50"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:51"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:51"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:52"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:52"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:52"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:53"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:53"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:54"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:54"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:55"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:55"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:55"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:56"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:56"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:57"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:57"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:57"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:58"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:35:58"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:23"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:23"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:42"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:42"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:43"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:43"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:44"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:44"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:45"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:45"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:46"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:46"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:46"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:47"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:47"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:48"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:48"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:49"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:49"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:50"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:52"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:52"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:53"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:53"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:54"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:54"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:55"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:55"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:56"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:56"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:36:56"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:03"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:03"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:11"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:11"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:17"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:17"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:18"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:18"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:22"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:22"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:23"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:23"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:35"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:35"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:36"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:36"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:37"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:37"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:37"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:38"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-11 12:37:38"}
{"code":"57P03","file":"postmaster.c","length":104,"level":"error","line":"2393","message":"Database connection failed: the database system is starting up","name":"error","routine":"ProcessStartupPacket","service":"aigc-service-hub","severity":"FATAL","stack":"error: the database system is starting up\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async DatabaseManager.connect (/app/dist/database/connection.js:81:28)\n    at async initDatabase (/app/dist/database/connection.js:177:5)\n    at async Application.start (/app/dist/main.js:159:13)","timestamp":"2025-07-14 00:46:26"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:00:24"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:00:24"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:01:13"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:01:13"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:01:18"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:01:18"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:01:22"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:01:22"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:01:29"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:01:29"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:01:30"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (/app/dist/services/asset.service.js:317:49)\n    at async /app/dist/controllers/asset.controller.js:90:28","timestamp":"2025-07-14 01:01:30"}
{"code":"57P03","file":"postmaster.c","length":104,"level":"error","line":"2393","message":"Database connection failed: the database system is starting up","name":"error","routine":"ProcessStartupPacket","service":"aigc-service-hub","severity":"FATAL","stack":"error: the database system is starting up\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async DatabaseManager.connect (/app/dist/database/connection.js:81:28)\n    at async initDatabase (/app/dist/database/connection.js:177:5)\n    at async Application.start (/app/dist/main.js:159:13)","timestamp":"2025-07-14 08:49:44"}
{"code":"57P03","file":"postmaster.c","length":104,"level":"error","line":"2393","message":"Database connection failed: the database system is starting up","name":"error","routine":"ProcessStartupPacket","service":"aigc-service-hub","severity":"FATAL","stack":"error: the database system is starting up\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async DatabaseManager.connect (/app/dist/database/connection.js:81:28)\n    at async initDatabase (/app/dist/database/connection.js:177:5)\n    at async Application.start (/app/dist/main.js:159:13)","timestamp":"2025-07-14 08:49:46"}
{"code":"3D000","file":"postinit.c","length":101,"level":"error","line":"948","message":"Database connection failed: database \"aigc_service_hub\" does not exist","name":"error","routine":"InitPostgres","service":"aigc-service-hub","severity":"FATAL","stack":"error: database \"aigc_service_hub\" does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async DatabaseManager.connect (C:\\aigcshub\\backend\\src\\database\\connection.ts:99:22)\n    at async initDatabase (C:\\aigcshub\\backend\\src\\database\\connection.ts:215:3)\n    at async Application.start (C:\\aigcshub\\backend\\src\\main.ts:182:7)","timestamp":"2025-07-14 18:07:17"}
{"code":"42P01","file":"parse_relation.c","length":106,"level":"error","line":"1392","message":"Failed to get asset list: relation \"assets\" does not exist","name":"error","position":"247","routine":"parserOpenTable","service":"aigc-service-hub","severity":"ERROR","stack":"error: relation \"assets\" does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:10:28"}
{"code":"42P01","file":"parse_relation.c","length":106,"level":"error","line":"1392","message":"Failed to get asset list: relation \"assets\" does not exist","name":"error","position":"247","routine":"parserOpenTable","service":"aigc-service-hub","severity":"ERROR","stack":"error: relation \"assets\" does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:10:28"}
{"code":"42P01","file":"parse_relation.c","length":106,"level":"error","line":"1392","message":"Failed to get asset list: relation \"assets\" does not exist","name":"error","position":"247","routine":"parserOpenTable","service":"aigc-service-hub","severity":"ERROR","stack":"error: relation \"assets\" does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:11:01"}
{"code":"42P01","file":"parse_relation.c","length":106,"level":"error","line":"1392","message":"Failed to get asset list: relation \"assets\" does not exist","name":"error","position":"247","routine":"parserOpenTable","service":"aigc-service-hub","severity":"ERROR","stack":"error: relation \"assets\" does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:11:01"}
{"code":"42P01","file":"parse_relation.c","length":106,"level":"error","line":"1392","message":"Failed to get asset list: relation \"assets\" does not exist","name":"error","position":"247","routine":"parserOpenTable","service":"aigc-service-hub","severity":"ERROR","stack":"error: relation \"assets\" does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:11:56"}
{"code":"42P01","file":"parse_relation.c","length":105,"level":"error","line":"1392","message":"Failed to get asset list: relation \"assets\" does not exist","name":"error","position":"48","routine":"parserOpenTable","service":"aigc-service-hub","severity":"ERROR","stack":"error: relation \"assets\" does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 1)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:11:56"}
{"code":"42P01","file":"parse_relation.c","length":106,"level":"error","line":"1392","message":"Failed to get asset list: relation \"assets\" does not exist","name":"error","position":"247","routine":"parserOpenTable","service":"aigc-service-hub","severity":"ERROR","stack":"error: relation \"assets\" does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:12:17"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:14:36"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:14:36"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:15:38"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:15:39"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:15:41"}
{"code":"42703","file":"parse_relation.c","length":107,"level":"error","line":"3665","message":"Failed to get asset list: column a.newest does not exist","name":"error","position":"354","routine":"errorMissingColumn","service":"aigc-service-hub","severity":"ERROR","stack":"error: column a.newest does not exist\n    at C:\\aigcshub\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Promise.all (index 0)\n    at async AssetService.getAssetList (C:\\aigcshub\\backend\\src\\services\\asset.service.ts:406:43)\n    at async C:\\aigcshub\\backend\\src\\controllers\\asset.controller.ts:125:20","timestamp":"2025-07-14 18:15:42"}
{"code":"57P03","file":"postmaster.c","length":104,"level":"error","line":"2393","message":"Database connection failed: the database system is starting up","name":"error","routine":"ProcessStartupPacket","service":"aigc-service-hub","severity":"FATAL","stack":"error: the database system is starting up\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async DatabaseManager.connect (/app/dist/database/connection.js:81:28)\n    at async initDatabase (/app/dist/database/connection.js:177:5)\n    at async Application.start (/app/dist/main.js:159:13)","timestamp":"2025-07-16 01:33:27"}
{"code":"57P03","file":"postmaster.c","length":104,"level":"error","line":"2393","message":"Database connection failed: the database system is starting up","name":"error","routine":"ProcessStartupPacket","service":"aigc-service-hub","severity":"FATAL","stack":"error: the database system is starting up\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async DatabaseManager.connect (/app/dist/database/connection.js:81:28)\n    at async initDatabase (/app/dist/database/connection.js:177:5)\n    at async Application.start (/app/dist/main.js:159:13)","timestamp":"2025-07-16 01:46:25"}
{"code":"57P03","file":"postmaster.c","length":104,"level":"error","line":"2393","message":"Database connection failed: the database system is starting up","name":"error","routine":"ProcessStartupPacket","service":"aigc-service-hub","severity":"FATAL","stack":"error: the database system is starting up\n    at /app/node_modules/pg-pool/index.js:45:11\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async DatabaseManager.connect (/app/dist/database/connection.js:81:28)\n    at async initDatabase (/app/dist/database/connection.js:177:5)\n    at async Application.start (/app/dist/main.js:159:13)","timestamp":"2025-07-17 01:32:17"}
