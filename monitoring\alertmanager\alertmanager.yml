global:
  # SMTP配置
  smtp_smarthost: '${SMTP_HOST:-localhost:587}'
  smtp_from: '${SMTP_FROM:-<EMAIL>}'
  smtp_auth_username: '${SMTP_USERNAME:-}'
  smtp_auth_password: '${SMTP_PASSWORD:-}'
  smtp_require_tls: true
  
  # Slack配置
  slack_api_url: '${SLACK_API_URL:-}'
  
  # 全局标签
  resolve_timeout: 5m

# 路由配置
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
    # 关键告警路由
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 10s
      group_interval: 5m
      repeat_interval: 30m
      routes:
        # 数据库关键告警
        - match:
            service: database
          receiver: 'database-critical'
        # 应用关键告警
        - match:
            service: application
          receiver: 'application-critical'
        # 系统关键告警
        - match:
            service: system
          receiver: 'system-critical'
    
    # 警告级别告警路由
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 2h
      routes:
        # 数据库警告
        - match:
            service: database
          receiver: 'database-warning'
        # 应用警告
        - match:
            service: application
          receiver: 'application-warning'
        # 系统警告
        - match:
            service: system
          receiver: 'system-warning'
        # 网络警告
        - match:
            service: network
          receiver: 'network-warning'
        # 安全警告
        - match:
            service: security
          receiver: 'security-warning'
    
    # 业务告警路由
    - match:
        service: business
      receiver: 'business-alerts'
      group_wait: 1m
      group_interval: 10m
      repeat_interval: 4h
    
    # 备份告警路由
    - match:
        service: backup
      receiver: 'backup-alerts'
      group_wait: 5m
      group_interval: 1h
      repeat_interval: 24h
    
    # 证书告警路由
    - match:
        service: certificate
      receiver: 'certificate-alerts'
      group_wait: 1h
      group_interval: 24h
      repeat_interval: 168h # 7天
    
    # 测试告警路由
    - match:
        alertname: DeadMansSwitch
      receiver: 'deadmansswitch'
      group_wait: 0s
      group_interval: 30s
      repeat_interval: 30s

# 抑制规则
inhibit_rules:
  # 当应用程序关闭时，抑制所有其他相关告警
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
  
  # 当系统CPU高时，抑制容器CPU高告警
  - source_match:
      alertname: 'HighCpuUsage'
    target_match:
      alertname: 'ContainerCpuUsage'
    equal: ['instance']
  
  # 当系统内存高时，抑制容器内存高告警
  - source_match:
      alertname: 'HighMemoryUsage'
    target_match:
      alertname: 'ContainerMemoryUsage'
    equal: ['instance']

# 接收器配置
receivers:
  # 默认接收器
  - name: 'default'
    email_configs:
      - to: '${DEFAULT_EMAIL:-<EMAIL>}'
        subject: '[AIGC Hub] {{ .GroupLabels.alertname }} - {{ .Status | toUpper }}'
        body: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          状态: {{ .Status }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          标签: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}
        html: |
          <html>
          <head>
            <title>AIGC Hub 告警通知</title>
            <style>
              body { font-family: Arial, sans-serif; }
              .alert { border: 1px solid #ddd; margin: 10px 0; padding: 10px; }
              .critical { background-color: #f8d7da; border-color: #f5c6cb; }
              .warning { background-color: #fff3cd; border-color: #ffeaa7; }
              .resolved { background-color: #d4edda; border-color: #c3e6cb; }
            </style>
          </head>
          <body>
            <h2>AIGC Hub 告警通知</h2>
            {{ range .Alerts }}
            <div class="alert {{ if eq .Status "firing" }}{{ if eq .Labels.severity "critical" }}critical{{ else }}warning{{ end }}{{ else }}resolved{{ end }}">
              <h3>{{ .Annotations.summary }}</h3>
              <p><strong>描述:</strong> {{ .Annotations.description }}</p>
              <p><strong>状态:</strong> {{ .Status }}</p>
              <p><strong>时间:</strong> {{ .StartsAt.Format "2006-01-02 15:04:05" }}</p>
              <p><strong>标签:</strong> {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}</p>
            </div>
            {{ end }}
          </body>
          </html>

  # 关键告警接收器
  - name: 'critical-alerts'
    email_configs:
      - to: '${CRITICAL_EMAIL:-<EMAIL>,<EMAIL>}'
        subject: '[CRITICAL] AIGC Hub - {{ .GroupLabels.alertname }}'
        body: |
          🚨 关键告警 🚨
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          严重程度: {{ .Labels.severity }}
          服务: {{ .Labels.service }}
          实例: {{ .Labels.instance }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          {{ end }}
          
          请立即处理此告警！
    slack_configs:
      - api_url: '${SLACK_API_URL}'
        channel: '#alerts-critical'
        username: 'AlertManager'
        icon_emoji: ':fire:'
        title: '🚨 关键告警 - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          **告警**: {{ .Annotations.summary }}
          **描述**: {{ .Annotations.description }}
          **严重程度**: {{ .Labels.severity }}
          **服务**: {{ .Labels.service }}
          **实例**: {{ .Labels.instance }}
          **时间**: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        color: 'danger'
        send_resolved: true

  # 警告级别告警接收器
  - name: 'warning-alerts'
    email_configs:
      - to: '${WARNING_EMAIL:-<EMAIL>}'
        subject: '[WARNING] AIGC Hub - {{ .GroupLabels.alertname }}'
        body: |
          ⚠️ 警告告警 ⚠️
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          严重程度: {{ .Labels.severity }}
          服务: {{ .Labels.service }}
          实例: {{ .Labels.instance }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          {{ end }}
    slack_configs:
      - api_url: '${SLACK_API_URL}'
        channel: '#alerts-warning'
        username: 'AlertManager'
        icon_emoji: ':warning:'
        title: '⚠️ 警告告警 - {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          **告警**: {{ .Annotations.summary }}
          **描述**: {{ .Annotations.description }}
          **服务**: {{ .Labels.service }}
          **实例**: {{ .Labels.instance }}
          {{ end }}
        color: 'warning'
        send_resolved: true

  # 数据库关键告警
  - name: 'database-critical'
    email_configs:
      - to: '${DBA_EMAIL:-<EMAIL>,<EMAIL>}'
        subject: '[DATABASE CRITICAL] AIGC Hub - {{ .GroupLabels.alertname }}'
        body: |
          🚨 数据库关键告警 🚨
          
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          数据库: {{ .Labels.instance }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          {{ end }}
          
          请立即检查数据库状态！
    webhook_configs:
      - url: '${DATABASE_WEBHOOK:-http://localhost:8080/webhook/database}'
        send_resolved: true

  # 应用关键告警
  - name: 'application-critical'
    email_configs:
      - to: '${DEV_EMAIL:-<EMAIL>,<EMAIL>}'
        subject: '[APPLICATION CRITICAL] AIGC Hub - {{ .GroupLabels.alertname }}'
    pagerduty_configs:
      - service_key: '${PAGERDUTY_SERVICE_KEY}'
        description: 'AIGC Hub Application Critical Alert'

  # 系统关键告警
  - name: 'system-critical'
    email_configs:
      - to: '${SYSADMIN_EMAIL:-<EMAIL>,<EMAIL>}'
        subject: '[SYSTEM CRITICAL] AIGC Hub - {{ .GroupLabels.alertname }}'

  # 数据库警告
  - name: 'database-warning'
    email_configs:
      - to: '${DBA_EMAIL:-<EMAIL>}'
        subject: '[DATABASE WARNING] AIGC Hub - {{ .GroupLabels.alertname }}'

  # 应用警告
  - name: 'application-warning'
    email_configs:
      - to: '${DEV_EMAIL:-<EMAIL>}'
        subject: '[APPLICATION WARNING] AIGC Hub - {{ .GroupLabels.alertname }}'

  # 系统警告
  - name: 'system-warning'
    email_configs:
      - to: '${SYSADMIN_EMAIL:-<EMAIL>}'
        subject: '[SYSTEM WARNING] AIGC Hub - {{ .GroupLabels.alertname }}'

  # 网络警告
  - name: 'network-warning'
    email_configs:
      - to: '${NETWORK_EMAIL:-<EMAIL>}'
        subject: '[NETWORK WARNING] AIGC Hub - {{ .GroupLabels.alertname }}'

  # 安全警告
  - name: 'security-warning'
    email_configs:
      - to: '${SECURITY_EMAIL:-<EMAIL>,<EMAIL>}'
        subject: '[SECURITY WARNING] AIGC Hub - {{ .GroupLabels.alertname }}'
    slack_configs:
      - api_url: '${SLACK_API_URL}'
        channel: '#security-alerts'
        username: 'AlertManager'
        icon_emoji: ':shield:'
        title: '🛡️ 安全警告 - {{ .GroupLabels.alertname }}'
        color: 'warning'

  # 业务告警
  - name: 'business-alerts'
    email_configs:
      - to: '${BUSINESS_EMAIL:-<EMAIL>}'
        subject: '[BUSINESS ALERT] AIGC Hub - {{ .GroupLabels.alertname }}'
    slack_configs:
      - api_url: '${SLACK_API_URL}'
        channel: '#business-alerts'
        username: 'AlertManager'
        icon_emoji: ':chart_with_upwards_trend:'
        title: '📊 业务告警 - {{ .GroupLabels.alertname }}'
        color: 'good'

  # 备份告警
  - name: 'backup-alerts'
    email_configs:
      - to: '${BACKUP_EMAIL:-<EMAIL>,<EMAIL>}'
        subject: '[BACKUP ALERT] AIGC Hub - {{ .GroupLabels.alertname }}'

  # 证书告警
  - name: 'certificate-alerts'
    email_configs:
      - to: '${CERT_EMAIL:-<EMAIL>,<EMAIL>}'
        subject: '[CERTIFICATE ALERT] AIGC Hub - {{ .GroupLabels.alertname }}'

  # 死人开关
  - name: 'deadmansswitch'
    webhook_configs:
      - url: '${DEADMANSSWITCH_URL:-https://deadmansswitch.com/ping/your-uuid}'
        send_resolved: false

# 模板配置
templates:
  - '/etc/alertmanager/templates/*.tmpl'