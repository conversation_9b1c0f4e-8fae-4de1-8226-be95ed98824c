apiVersion: 1

# 数据源配置
datasources:
  # Prometheus数据源
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: GET
      timeInterval: 5s
      queryTimeout: 60s
      manageAlerts: true
      alertmanagerUid: alertmanager
      exemplarTraceIdDestinations:
        - name: trace_id
          datasourceUid: jaeger
      prometheusType: Prometheus
      prometheusVersion: 2.45.0
      cacheLevel: 'High'
      incrementalQueryOverlapWindow: 10m
    secureJsonData:
      httpHeaderValue1: ''
    uid: prometheus

  # Alertmanager数据源
  - name: Alertmanager
    type: alertmanager
    access: proxy
    url: http://alertmanager:9093
    jsonData:
      implementation: prometheus
      handleGrafanaManagedAlerts: true
    uid: alertmanager

  # Jaeger数据源
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    jsonData:
      tracesToLogsV2:
        datasourceUid: 'loki'
        spanStartTimeShift: '1h'
        spanEndTimeShift: '-1h'
        tags: [{ key: 'service.name', value: 'service' }]
        filterByTraceID: false
        filterBySpanID: false
        customQuery: true
        query: '{service="${__data.fields.service}"} |= "${__data.fields.traceID}"'
      tracesToMetrics:
        datasourceUid: 'prometheus'
        spanStartTimeShift: '1h'
        spanEndTimeShift: '-1h'
        tags: [{ key: 'service.name', value: 'service' }]
        queries:
          - name: 'Sample query'
            query: 'sum(rate(traces_spanmetrics_latency_bucket{service="${service}"}[5m]))'
      nodeGraph:
        enabled: true
    uid: jaeger

  # Elasticsearch数据源
  - name: Elasticsearch
    type: elasticsearch
    access: proxy
    url: http://elasticsearch:9200
    database: '[logstash-]YYYY.MM.DD'
    jsonData:
      interval: Daily
      timeField: '@timestamp'
      esVersion: '8.8.0'
      maxConcurrentShardRequests: 5
      logMessageField: 'message'
      logLevelField: 'level'
      includeFrozen: false
    uid: elasticsearch

  # Loki数据源（如果使用Loki替代Elasticsearch）
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: jaeger
          matcherRegex: "traceID=(\\w+)"
          name: TraceID
          url: '$${__value.raw}'
    uid: loki

  # Redis数据源
  - name: Redis
    type: redis-datasource
    access: proxy
    url: redis://redis:6379
    jsonData:
      client: standalone
      poolSize: 5
      timeout: 10
      pingInterval: 0
      pipelineWindow: 0
    uid: redis

  # PostgreSQL数据源
  - name: PostgreSQL
    type: postgres
    access: proxy
    url: postgres:5432
    database: ${POSTGRES_DB:-aigc_hub}
    user: ${POSTGRES_USER:-postgres}
    jsonData:
      sslmode: disable
      maxOpenConns: 100
      maxIdleConns: 100
      maxIdleConnsAuto: true
      connMaxLifetime: 14400
      postgresVersion: 1400
      timescaledb: false
    secureJsonData:
      password: ${POSTGRES_PASSWORD:-password}
    uid: postgres

  # TestData数据源（用于测试）
  - name: TestData
    type: testdata
    access: proxy
    uid: testdata

  # InfluxDB数据源（如果需要）
  # - name: InfluxDB
  #   type: influxdb
  #   access: proxy
  #   url: http://influxdb:8086
  #   database: aigc_hub
  #   user: admin
  #   jsonData:
  #     version: '1.8'
  #     timeInterval: 10s
  #     httpMode: GET
  #   secureJsonData:
  #     password: admin
  #   uid: influxdb

  # CloudWatch数据源（如果部署在AWS）
  # - name: CloudWatch
  #   type: cloudwatch
  #   access: proxy
  #   jsonData:
  #     authType: default
  #     defaultRegion: us-east-1
  #     customMetricsNamespaces: 'CWAgent,AWS/ApplicationELB,AWS/ELB'
  #     assumeRoleArn: ''
  #   uid: cloudwatch

  # Azure Monitor数据源（如果部署在Azure）
  # - name: Azure Monitor
  #   type: grafana-azure-monitor-datasource
  #   access: proxy
  #   jsonData:
  #     subscriptionId: 'your-subscription-id'
  #     tenantId: 'your-tenant-id'
  #     clientId: 'your-client-id'
  #     cloudName: 'azuremonitor'
  #     resourceGroup: 'your-resource-group'
  #   secureJsonData:
  #     clientSecret: 'your-client-secret'
  #   uid: azure-monitor

  # Google Cloud Monitoring数据源（如果部署在GCP）
  # - name: Google Cloud Monitoring
  #   type: stackdriver
  #   access: proxy
  #   jsonData:
  #     authenticationType: gce
  #     defaultProject: 'your-project-id'
  #     gceDefaultServiceAccount: true
  #   uid: stackdriver

# 删除已存在的数据源配置
deleteDatasources:
  - name: Prometheus
    orgId: 1
  - name: Alertmanager
    orgId: 1