import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Link,
  IconButton,
  Divider,
  useTheme
} from '@mui/material';
import {
  Facebook,
  Twitter,
  Instagram,
  LinkedIn,
  YouTube,
  GitHub,
  Email,
  Phone,
  LocationOn
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

interface HomeFooterProps {}

const HomeFooter: React.FC<HomeFooterProps> = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();

  const footerSections = [
    {
      title: t('footer.platform.title'),
      links: [
        { label: t('footer.platform.about'), href: '/about' },
        { label: t('footer.platform.features'), href: '/features' },
        { label: t('footer.platform.pricing'), href: '/pricing' },
        { label: t('footer.platform.blog'), href: '/blog' },
        { label: t('footer.platform.news'), href: '/news' }
      ]
    },
    {
      title: t('footer.creators.title'),
      links: [
        { label: t('footer.creators.guide'), href: '/creators/guide' },
        { label: t('footer.creators.upload'), href: '/assets/upload' },
        { label: t('footer.creators.earnings'), href: '/creators/earnings' },
        { label: t('footer.creators.community'), href: '/community' },
        { label: t('footer.creators.resources'), href: '/resources' }
      ]
    },
    {
      title: t('footer.support.title'),
      links: [
        { label: t('footer.support.help'), href: '/help' },
        { label: t('footer.support.faq'), href: '/faq' },
        { label: t('footer.support.contact'), href: '/contact' },
        { label: t('footer.support.feedback'), href: '/feedback' },
        { label: t('footer.support.status'), href: '/status' }
      ]
    },
    {
      title: t('footer.legal.title'),
      links: [
        { label: t('footer.legal.terms'), href: '/terms' },
        { label: t('footer.legal.privacy'), href: '/privacy' },
        { label: t('footer.legal.cookies'), href: '/cookies' },
        { label: t('footer.legal.dmca'), href: '/dmca' },
        { label: t('footer.legal.licenses'), href: '/licenses' }
      ]
    }
  ];

  const socialLinks = [
    { icon: <Facebook />, href: 'https://facebook.com/aigcservicehub', label: 'Facebook' },
    { icon: <Twitter />, href: 'https://twitter.com/aigcservicehub', label: 'Twitter' },
    { icon: <Instagram />, href: 'https://instagram.com/aigcservicehub', label: 'Instagram' },
    { icon: <LinkedIn />, href: 'https://linkedin.com/company/aigcservicehub', label: 'LinkedIn' },
    { icon: <YouTube />, href: 'https://youtube.com/aigcservicehub', label: 'YouTube' },
    { icon: <GitHub />, href: 'https://github.com/aigcservicehub', label: 'GitHub' }
  ];

  const contactInfo = [
    {
      icon: <Email />,
      label: t('footer.contact.email'),
      value: '<EMAIL>',
      href: 'mailto:<EMAIL>'
    },
    {
      icon: <Phone />,
      label: t('footer.contact.phone'),
      value: '+****************',
      href: 'tel:+15551234567'
    },
    {
      icon: <LocationOn />,
      label: t('footer.contact.address'),
      value: t('footer.contact.address_value'),
      href: null
    }
  ];

  const handleLinkClick = (href: string) => {
    if (href.startsWith('http')) {
      window.open(href, '_blank', 'noopener,noreferrer');
    } else {
      navigate(href);
    }
  };

  return (
    <Box
      component="footer"
      sx={{
        height: 260,
        backgroundColor: 'grey.900',
        color: 'white',
        mt: 'auto'
      }}
    >
      <Container maxWidth="lg" sx={{ height: '100%', py: 3 }}>
        <Grid container spacing={3} sx={{ height: '100%' }}>
          {/* 公司信息和联系方式 */}
          <Grid item xs={12} md={3}>
            <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              {/* Logo和简介 */}
              <Typography
                variant="h6"
                component="div"
                sx={{
                  fontWeight: 'bold',
                  mb: 1,
                  color: 'primary.light'
                }}
              >
                AIGC Service Hub
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  mb: 2,
                  color: 'grey.300',
                  fontSize: '0.875rem',
                  lineHeight: 1.4
                }}
              >
                {t('footer.description')}
              </Typography>

              {/* 联系信息 */}
              <Box sx={{ mb: 2 }}>
                {contactInfo.map((contact, index) => (
                  <Box
                    key={index}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      mb: 0.5,
                      cursor: contact.href ? 'pointer' : 'default'
                    }}
                    onClick={() => contact.href && handleLinkClick(contact.href)}
                  >
                    <Box sx={{ mr: 1, color: 'primary.light' }}>
                      {React.cloneElement(contact.icon, { sx: { fontSize: 16 } })}
                    </Box>
                    <Typography
                      variant="caption"
                      sx={{
                        color: 'grey.300',
                        fontSize: '0.75rem',
                        textDecoration: contact.href ? 'underline' : 'none',
                        '&:hover': contact.href ? { color: 'primary.light' } : {}
                      }}
                    >
                      {contact.value}
                    </Typography>
                  </Box>
                ))}
              </Box>

              {/* 社交媒体链接 */}
              <Box sx={{ mt: 'auto' }}>
                <Typography variant="caption" sx={{ color: 'grey.400', mb: 1, display: 'block' }}>
                  {t('footer.follow_us')}
                </Typography>
                <Box sx={{ display: 'flex', gap: 0.5 }}>
                  {socialLinks.map((social, index) => (
                    <IconButton
                      key={index}
                      size="small"
                      sx={{
                        color: 'grey.400',
                        '&:hover': {
                          color: 'primary.light',
                          backgroundColor: 'rgba(255,255,255,0.1)'
                        }
                      }}
                      onClick={() => handleLinkClick(social.href)}
                      aria-label={social.label}
                    >
                      {React.cloneElement(social.icon, { sx: { fontSize: 18 } })}
                    </IconButton>
                  ))}
                </Box>
              </Box>
            </Box>
          </Grid>

          {/* 链接分组 */}
          {footerSections.map((section, sectionIndex) => (
            <Grid item xs={6} md={2.25} key={sectionIndex}>
              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 'bold',
                    mb: 1.5,
                    color: 'white',
                    fontSize: '0.875rem'
                  }}
                >
                  {section.title}
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.75 }}>
                  {section.links.map((link, linkIndex) => (
                    <Link
                      key={linkIndex}
                      component="button"
                      variant="caption"
                      sx={{
                        color: 'grey.300',
                        textDecoration: 'none',
                        textAlign: 'left',
                        fontSize: '0.75rem',
                        cursor: 'pointer',
                        border: 'none',
                        background: 'none',
                        padding: 0,
                        '&:hover': {
                          color: 'primary.light',
                          textDecoration: 'underline'
                        }
                      }}
                      onClick={() => handleLinkClick(link.href)}
                    >
                      {link.label}
                    </Link>
                  ))}
                </Box>
              </Box>
            </Grid>
          ))}
        </Grid>

        {/* 分割线 */}
        <Divider sx={{ my: 2, borderColor: 'grey.700' }} />

        {/* 底部版权信息 */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 1
          }}
        >
          <Typography
            variant="caption"
            sx={{
              color: 'grey.400',
              fontSize: '0.75rem'
            }}
          >
            © {new Date().getFullYear()} AIGC Service Hub. {t('footer.copyright')}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography
              variant="caption"
              sx={{
                color: 'grey.400',
                fontSize: '0.75rem'
              }}
            >
              {t('footer.version')} 1.0.0
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Link
                component="button"
                variant="caption"
                sx={{
                  color: 'grey.400',
                  textDecoration: 'none',
                  fontSize: '0.75rem',
                  cursor: 'pointer',
                  border: 'none',
                  background: 'none',
                  padding: 0,
                  '&:hover': {
                    color: 'primary.light'
                  }
                }}
                onClick={() => handleLinkClick('/sitemap')}
              >
                {t('footer.sitemap')}
              </Link>
              
              <Typography variant="caption" sx={{ color: 'grey.600' }}>
                |
              </Typography>
              
              <Link
                component="button"
                variant="caption"
                sx={{
                  color: 'grey.400',
                  textDecoration: 'none',
                  fontSize: '0.75rem',
                  cursor: 'pointer',
                  border: 'none',
                  background: 'none',
                  padding: 0,
                  '&:hover': {
                    color: 'primary.light'
                  }
                }}
                onClick={() => handleLinkClick('/accessibility')}
              >
                {t('footer.accessibility')}
              </Link>
            </Box>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default HomeFooter;
