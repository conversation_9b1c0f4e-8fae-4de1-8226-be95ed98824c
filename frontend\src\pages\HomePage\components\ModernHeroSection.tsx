import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Container,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Avatar,
  Chip,
  useTheme,
  useMediaQuery,
  Fade,
  Slide,
  IconButton,
  Paper,
  Stack,
  Skeleton,
  Rating,
  Divider,
  LinearProgress,
} from '@mui/material';
import {
  PlayArrow,
  TrendingUp,
  Star,
  Download,
  Visibility,
  ArrowForward,
  AutoAwesome,
  Palette,
  Code,
  Camera,
  MusicNote,
  VideoLibrary,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

interface ModernHeroSectionProps {}

interface TrendingAsset {
  id: string;
  title: string;
  creator: string;
  avatar: string;
  thumbnail: string;
  category: string;
  downloads: number;
  rating: number;
  price: number;
  tags: string[];
}

interface CategoryStats {
  name: string;
  icon: React.ReactNode;
  count: number;
  growth: number;
  color: string;
}

const ModernHeroSection: React.FC<ModernHeroSectionProps> = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [currentSlide, setCurrentSlide] = useState(0);
  const [loading, setLoading] = useState(true);
  const [trendingAssets, setTrendingAssets] = useState<TrendingAsset[]>([]);

  // 模拟数据
  const heroSlides = [
    {
      title: '探索无限创意可能',
      subtitle: '发现最新AI生成内容，释放你的创造力',
      image: 'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=1200&h=600&fit=crop',
      cta: '开始探索',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    },
    {
      title: '顶级创作者作品集',
      subtitle: '来自全球优秀创作者的精选作品',
      image: 'https://images.unsplash.com/photo-1561736778-92e52a7769ef?w=1200&h=600&fit=crop',
      cta: '查看作品',
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    },
    {
      title: '智能工具与模型',
      subtitle: '最新AI模型和创作工具，提升你的工作效率',
      image: 'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=1200&h=600&fit=crop',
      cta: '立即使用',
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    },
  ];

  const categoryStats: CategoryStats[] = [
    {
      name: '图像生成',
      icon: <Camera />,
      count: 15420,
      growth: 23.5,
      color: '#FF6B6B',
    },
    {
      name: '视频制作',
      icon: <VideoLibrary />,
      count: 8930,
      growth: 18.2,
      color: '#4ECDC4',
    },
    {
      name: '音频处理',
      icon: <MusicNote />,
      count: 6750,
      growth: 31.7,
      color: '#45B7D1',
    },
    {
      name: '代码生成',
      icon: <Code />,
      count: 12340,
      growth: 42.1,
      color: '#96CEB4',
    },
    {
      name: '设计工具',
      icon: <Palette />,
      count: 9870,
      growth: 15.8,
      color: '#FFEAA7',
    },
    {
      name: 'AI模型',
      icon: <AutoAwesome />,
      count: 5620,
      growth: 67.3,
      color: '#DDA0DD',
    },
  ];

  useEffect(() => {
    // 模拟加载数据
    const loadData = async () => {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟热门资源数据
      const mockTrendingAssets: TrendingAsset[] = [
        {
          id: '1',
          title: 'AI Portrait Generator',
          creator: 'Alex Chen',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
          thumbnail: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop',
          category: '图像生成',
          downloads: 12450,
          rating: 4.8,
          price: 29.99,
          tags: ['AI', 'Portrait', 'Generator'],
        },
        {
          id: '2',
          title: 'Video Style Transfer',
          creator: 'Sarah Kim',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face',
          thumbnail: 'https://images.unsplash.com/photo-1536240478700-b869070f9279?w=300&h=200&fit=crop',
          category: '视频制作',
          downloads: 8930,
          rating: 4.9,
          price: 49.99,
          tags: ['Video', 'Style', 'Transfer'],
        },
        {
          id: '3',
          title: 'Music Composition AI',
          creator: 'David Park',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
          thumbnail: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=200&fit=crop',
          category: '音频处理',
          downloads: 6750,
          rating: 4.7,
          price: 39.99,
          tags: ['Music', 'AI', 'Composition'],
        },
      ];
      
      setTrendingAssets(mockTrendingAssets);
      setLoading(false);
    };

    loadData();

    // 自动轮播
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [heroSlides.length]);

  const renderHeroCarousel = () => (
    <Box
      sx={{
        position: 'relative',
        height: { xs: 400, md: 500 },
        overflow: 'hidden',
        borderRadius: 3,
        mb: 4,
      }}
    >
      {heroSlides.map((slide, index) => (
        <Fade key={index} in={currentSlide === index} timeout={1000}>
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: slide.gradient,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              opacity: currentSlide === index ? 1 : 0,
            }}
          >
            <Container maxWidth="lg">
              <Grid container spacing={4} alignItems="center">
                <Grid item xs={12} md={6}>
                  <Slide direction="right" in={currentSlide === index} timeout={800}>
                    <Box>
                      <Typography
                        variant="h2"
                        component="h1"
                        sx={{
                          fontWeight: 'bold',
                          color: 'white',
                          mb: 2,
                          fontSize: { xs: '2rem', md: '3rem' },
                        }}
                      >
                        {slide.title}
                      </Typography>
                      <Typography
                        variant="h6"
                        sx={{
                          color: 'rgba(255, 255, 255, 0.9)',
                          mb: 4,
                          fontSize: { xs: '1rem', md: '1.25rem' },
                        }}
                      >
                        {slide.subtitle}
                      </Typography>
                      <Button
                        variant="contained"
                        size="large"
                        endIcon={<ArrowForward />}
                        sx={{
                          bgcolor: 'white',
                          color: 'primary.main',
                          px: 4,
                          py: 1.5,
                          fontSize: '1.1rem',
                          '&:hover': {
                            bgcolor: 'rgba(255, 255, 255, 0.9)',
                            transform: 'translateY(-2px)',
                          },
                          transition: 'all 0.3s ease',
                        }}
                      >
                        {slide.cta}
                      </Button>
                    </Box>
                  </Slide>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Slide direction="left" in={currentSlide === index} timeout={800}>
                    <Box
                      component="img"
                      src={slide.image}
                      alt={slide.title}
                      sx={{
                        width: '100%',
                        height: { xs: 200, md: 300 },
                        objectFit: 'cover',
                        borderRadius: 2,
                        boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
                      }}
                    />
                  </Slide>
                </Grid>
              </Grid>
            </Container>
          </Box>
        </Fade>
      ))}
      
      {/* 轮播指示器 */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 20,
          left: '50%',
          transform: 'translateX(-50%)',
          display: 'flex',
          gap: 1,
        }}
      >
        {heroSlides.map((_, index) => (
          <Box
            key={index}
            onClick={() => setCurrentSlide(index)}
            sx={{
              width: 12,
              height: 12,
              borderRadius: '50%',
              bgcolor: currentSlide === index ? 'white' : 'rgba(255, 255, 255, 0.5)',
              cursor: 'pointer',
              transition: 'all 0.3s ease',
              '&:hover': {
                bgcolor: 'white',
              },
            }}
          />
        ))}
      </Box>
    </Box>
  );

  return (
    <Box
      sx={{
        py: { xs: 4, md: 6 },
        bgcolor: 'background.default',
      }}
    >
      <Container maxWidth="xl">
        {/* 主要轮播区域 */}
        {renderHeroCarousel()}

        {/* 分类统计和热门资源 */}
        <Grid container spacing={4}>
          {/* 分类统计 */}
          <Grid item xs={12} lg={8}>
            <Typography
              variant="h4"
              component="h2"
              sx={{
                fontWeight: 'bold',
                mb: 3,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <TrendingUp color="primary" />
              热门分类
            </Typography>
            <Grid container spacing={2}>
              {categoryStats.map((category, index) => (
                <Grid item xs={6} sm={4} md={2} key={category.name}>
                  <Fade in timeout={500 + index * 100}>
                    <Card
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: 4,
                        },
                      }}
                    >
                      <Box
                        sx={{
                          width: 48,
                          height: 48,
                          borderRadius: '50%',
                          bgcolor: category.color,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mx: 'auto',
                          mb: 1,
                          color: 'white',
                        }}
                      >
                        {category.icon}
                      </Box>
                      <Typography variant="body2" fontWeight="medium" gutterBottom>
                        {category.name}
                      </Typography>
                      <Typography variant="h6" fontWeight="bold" color="primary">
                        {category.count.toLocaleString()}
                      </Typography>
                      <Chip
                        label={`+${category.growth}%`}
                        size="small"
                        color="success"
                        sx={{ mt: 0.5 }}
                      />
                    </Card>
                  </Fade>
                </Grid>
              ))}
            </Grid>
          </Grid>

          {/* 热门资源 */}
          <Grid item xs={12} lg={4}>
            <Typography
              variant="h4"
              component="h2"
              sx={{
                fontWeight: 'bold',
                mb: 3,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <Star color="primary" />
              热门资源
            </Typography>
            <Stack spacing={2}>
              {loading ? (
                Array.from({ length: 3 }).map((_, index) => (
                  <Card key={index} sx={{ p: 2 }}>
                    <Stack direction="row" spacing={2}>
                      <Skeleton variant="rectangular" width={80} height={60} />
                      <Box sx={{ flex: 1 }}>
                        <Skeleton variant="text" width="80%" />
                        <Skeleton variant="text" width="60%" />
                        <Skeleton variant="text" width="40%" />
                      </Box>
                    </Stack>
                  </Card>
                ))
              ) : (
                trendingAssets.map((asset, index) => (
                  <Fade key={asset.id} in timeout={500 + index * 200}>
                    <Card
                      sx={{
                        p: 2,
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateX(4px)',
                          boxShadow: 2,
                        },
                      }}
                    >
                      <Stack direction="row" spacing={2} alignItems="center">
                        <CardMedia
                          component="img"
                          image={asset.thumbnail}
                          alt={asset.title}
                          sx={{
                            width: 80,
                            height: 60,
                            borderRadius: 1,
                            objectFit: 'cover',
                          }}
                        />
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                          <Typography
                            variant="subtitle1"
                            fontWeight="medium"
                            noWrap
                            gutterBottom
                          >
                            {asset.title}
                          </Typography>
                          <Stack direction="row" spacing={1} alignItems="center" mb={1}>
                            <Avatar src={asset.avatar} sx={{ width: 20, height: 20 }} />
                            <Typography variant="body2" color="text.secondary" noWrap>
                              {asset.creator}
                            </Typography>
                          </Stack>
                          <Stack direction="row" spacing={1} alignItems="center">
                            <Rating value={asset.rating} size="small" readOnly />
                            <Typography variant="body2" color="text.secondary">
                              <Download sx={{ fontSize: 14, mr: 0.5 }} />
                              {asset.downloads.toLocaleString()}
                            </Typography>
                          </Stack>
                        </Box>
                        <Typography
                          variant="h6"
                          fontWeight="bold"
                          color="primary"
                        >
                          ${asset.price}
                        </Typography>
                      </Stack>
                    </Card>
                  </Fade>
                ))
              )}
            </Stack>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default ModernHeroSection;
