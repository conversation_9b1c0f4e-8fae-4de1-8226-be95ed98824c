# AIGC Service Hub MVP 1.0 - 技术架构文档

## 1. 架构概述

### 1.1 技术栈总览

**前端架构**
- React 18 + TypeScript
- Material-UI (MUI) v5
- Axios for HTTP requests
- Uppy.js for file uploads
- React Router for navigation
- React Hook Form for form handling

**后端架构**
- Node.js 18 + Express + TypeScript
- PostgreSQL (Amazon RDS)
- AWS SDK v3 (S3, SES)
- JWT for authentication
- bcrypt for password hashing
- node-cron for scheduled tasks

**基础设施**
- AWS us-west-2 region
- Amazon ECS + Fargate
- Amazon RDS for PostgreSQL
- Amazon S3 for file storage
- Amazon CloudFront for CDN
- AWS Application Load Balancer

### 1.2 系统架构图

```mermaid
graph TB
    subgraph "Client Layer"
        A[Web Browser] --> B[React App]
        B --> C[Material-UI Components]
    end
    
    subgraph "Load Balancer"
        D[AWS Application Load Balancer]
    end
    
    subgraph "Application Layer"
        E[ECS Service - API Server]
        F[ECS Service - Worker Service]
    end
    
    subgraph "Storage Layer"
        G[Amazon RDS PostgreSQL]
        H[Amazon S3 Private Bucket]
        I[Amazon S3 Public Bucket]
    end
    
    subgraph "External Services"
        J[PayPal API]
        K[Google OAuth]
        L[GitHub OAuth]
        M[AWS SES]
    end
    
    A --> D
    D --> E
    E --> F
    E --> G
    E --> H
    E --> I
    E --> J
    E --> K
    E --> L
    E --> M
    
    F --> G
    F --> H
    F --> M
```

## 2. 系统组件分解

### 2.1 前端组件架构

```
src/
├── components/           # 可复用UI组件
│   ├── common/          # 通用组件
│   ├── layout/          # 布局组件
│   ├── forms/           # 表单组件
│   └── resource/        # 资源相关组件
├── pages/               # 页面组件
│   ├── home/           # 首页
│   ├── auth/           # 认证相关
│   ├── profile/        # 用户中心
│   ├── creator/        # 创作者仪表盘
│   └── admin/          # 管理员后台
├── hooks/               # 自定义Hook
├── services/            # API服务
├── utils/               # 工具函数
├── types/               # TypeScript类型定义
└── constants/           # 常量定义
```

### 2.2 后端服务架构

```
src/
├── controllers/         # 控制器层
│   ├── auth.controller.ts
│   ├── user.controller.ts
│   ├── asset.controller.ts
│   ├── transaction.controller.ts
│   └── admin.controller.ts
├── services/            # 业务逻辑层
│   ├── auth.service.ts
│   ├── file.service.ts
│   ├── payment.service.ts
│   ├── commission.service.ts
│   └── notification.service.ts
├── models/              # 数据模型
├── middlewares/         # 中间件
├── routes/              # 路由定义
├── utils/               # 工具函数
├── config/              # 配置文件
└── workers/             # 后台任务
```

## 3. 数据库设计

### 3.1 核心数据表结构

基于PRD第10章的数据模型，以下是详细的数据库设计：

```sql
-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    display_name VARCHAR(255) NOT NULL,
    user_role VARCHAR(20) NOT NULL CHECK (user_role IN ('PERSONAL_CREATOR', 'ENTERPRISE_CREATOR', 'ADMIN')),
    points_balance INTEGER DEFAULT 0,
    oauth_provider VARCHAR(50),
    oauth_id VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(oauth_provider, oauth_id)
);

-- 资源表
CREATE TABLE assets (
    id SERIAL PRIMARY KEY,
    creator_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    asset_type VARCHAR(20) NOT NULL CHECK (asset_type IN ('MODEL', 'LORA', 'WORKFLOW', 'PROMPT', 'TOOL')),
    price_usd DECIMAL(10,2),
    price_points INTEGER,
    s3_file_key VARCHAR(500) NOT NULL,
    cover_image_url VARCHAR(500),
    status VARCHAR(20) DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'PUBLISHED', 'ARCHIVED')),
    download_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    published_at TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT price_check CHECK (price_usd > 0 OR price_points > 0)
);

-- 标签表
CREATE TABLE tags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('CATEGORY', 'STYLE')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 资源标签关联表
CREATE TABLE asset_tags (
    asset_id INTEGER REFERENCES assets(id) ON DELETE CASCADE,
    tag_id INTEGER REFERENCES tags(id) ON DELETE CASCADE,
    PRIMARY KEY (asset_id, tag_id)
);

-- 交易表
CREATE TABLE transactions (
    id SERIAL PRIMARY KEY,
    buyer_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    asset_id INTEGER REFERENCES assets(id) ON DELETE CASCADE,
    currency VARCHAR(10) NOT NULL CHECK (currency IN ('USD', 'POINTS')),
    amount_usd DECIMAL(10,2),
    amount_points INTEGER,
    paypal_transaction_id VARCHAR(255) UNIQUE,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    CONSTRAINT amount_check CHECK (
        (currency = 'USD' AND amount_usd > 0) OR 
        (currency = 'POINTS' AND amount_points > 0)
    )
);

-- 账本条目表（财务系统核心）
CREATE TABLE ledger_entries (
    id SERIAL PRIMARY KEY,
    transaction_id INTEGER REFERENCES transactions(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    entry_type VARCHAR(20) NOT NULL CHECK (entry_type IN ('SALE_CREDIT', 'PLATFORM_FEE', 'POINTS_PURCHASE', 'POINTS_DEBIT')),
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'AVAILABLE', 'WITHDRAWN', 'REFUNDED')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cleared_at TIMESTAMP,
    withdrawn_at TIMESTAMP
);

-- 提现请求表
CREATE TABLE withdrawal_requests (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    paypal_email VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED', 'COMPLETED')),
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    processed_by INTEGER REFERENCES users(id)
);

-- 系统配置表
CREATE TABLE system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3.2 数据库索引策略

```sql
-- 性能优化索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_oauth ON users(oauth_provider, oauth_id);
CREATE INDEX idx_assets_creator ON assets(creator_id);
CREATE INDEX idx_assets_status ON assets(status);
CREATE INDEX idx_assets_type ON assets(asset_type);
CREATE INDEX idx_transactions_buyer ON transactions(buyer_id);
CREATE INDEX idx_transactions_asset ON transactions(asset_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_ledger_user ON ledger_entries(user_id);
CREATE INDEX idx_ledger_status ON ledger_entries(status);
CREATE INDEX idx_ledger_created ON ledger_entries(created_at);
```

## 4. API设计规范

### 4.1 RESTful API设计原则

**基础URL结构**
- 生产环境: `https://api.aigcservicehub.com`
- 开发环境: `http://localhost:3000`

**通用响应格式**
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

### 4.2 核心API接口

#### 4.2.1 用户认证相关

```typescript
// POST /api/auth/register
interface RegisterRequest {
  email: string;
  password: string;
  displayName: string;
  userRole: 'PERSONAL_CREATOR' | 'ENTERPRISE_CREATOR';
}

// POST /api/auth/login
interface LoginRequest {
  email: string;
  password: string;
}

// GET /api/auth/oauth/google
// GET /api/auth/oauth/github
// OAuth重定向端点

// POST /api/auth/refresh
interface RefreshRequest {
  refreshToken: string;
}
```

#### 4.2.2 资源管理相关

```typescript
// GET /api/assets
interface GetAssetsQuery {
  page?: number;
  limit?: number;
  category?: string;
  style?: string;
  assetType?: string;
  sortBy?: 'created_at' | 'price_usd' | 'download_count';
  sortOrder?: 'asc' | 'desc';
}

// POST /api/assets
interface CreateAssetRequest {
  title: string;
  description: string;
  assetType: 'MODEL' | 'LORA' | 'WORKFLOW' | 'PROMPT' | 'TOOL';
  priceUsd?: number;
  pricePoints?: number;
  categories: string[];
  styles: string[];
  coverImageUrl?: string;
}

// GET /api/assets/:id/upload-url
interface GetUploadUrlResponse {
  uploadUrl: string;
  fileKey: string;
  expiresAt: string;
}

// POST /api/assets/:id/confirm-upload
interface ConfirmUploadRequest {
  fileKey: string;
  fileSize: number;
  fileName: string;
}
```

#### 4.2.3 交易相关

```typescript
// POST /api/transactions/purchase
interface PurchaseRequest {
  assetId: number;
  currency: 'USD' | 'POINTS';
}

// GET /api/transactions/my-purchases
interface MyPurchasesResponse {
  purchases: Array<{
    id: number;
    asset: AssetSummary;
    amount: number;
    currency: string;
    purchasedAt: string;
    downloadUrl?: string;
  }>;
}

// GET /api/assets/:id/download
// 返回预签名下载URL
```

#### 4.2.4 财务相关

```typescript
// GET /api/finance/balance
interface BalanceResponse {
  pointsBalance: number;
  availableBalance: number; // 可提现余额
  pendingBalance: number;   // 待清算余额
}

// POST /api/finance/withdrawal
interface WithdrawalRequest {
  amount: number;
  paypalEmail: string;
}

// GET /api/finance/earnings
interface EarningsResponse {
  totalEarnings: number;
  monthlyEarnings: Array<{
    month: string;
    amount: number;
  }>;
  recentTransactions: Array<{
    id: number;
    amount: number;
    asset: AssetSummary;
    createdAt: string;
  }>;
}
```

### 4.3 权限控制中间件

```typescript
// JWT认证中间件
interface JwtPayload {
  userId: number;
  email: string;
  userRole: string;
  iat: number;
  exp: number;
}

// 权限验证装饰器
enum Permission {
  READ_PUBLIC = 'read:public',
  READ_PRIVATE = 'read:private',
  WRITE_ASSET = 'write:asset',
  ADMIN_PANEL = 'admin:panel',
  FINANCE_WITHDRAW = 'finance:withdraw'
}

// 权限矩阵实现
const ROLE_PERMISSIONS = {
  PERSONAL_CREATOR: [
    Permission.READ_PUBLIC,
    Permission.READ_PRIVATE,
    Permission.WRITE_ASSET,
    Permission.FINANCE_WITHDRAW
  ],
  ENTERPRISE_CREATOR: [
    Permission.READ_PUBLIC,
    Permission.READ_PRIVATE,
    Permission.WRITE_ASSET,
    Permission.FINANCE_WITHDRAW
  ],
  ADMIN: [
    Permission.READ_PUBLIC,
    Permission.READ_PRIVATE,
    Permission.WRITE_ASSET,
    Permission.ADMIN_PANEL
  ]
};
```

## 5. 安全架构

### 5.1 文件上传安全机制

#### 5.1.1 S3预签名URL流程

```typescript
// S3预签名上传服务
class S3Service {
  async generateUploadUrl(
    userId: number,
    fileSize: number,
    fileName: string
  ): Promise<{ uploadUrl: string; fileKey: string }> {
    // 1. 验证文件大小（30GB限制）
    if (fileSize > 30 * 1024 * 1024 * 1024) {
      throw new Error('File size exceeds 30GB limit');
    }
    
    // 2. 生成唯一文件键
    const fileKey = `uploads/${userId}/${Date.now()}-${fileName}`;
    
    // 3. 创建预签名URL（15分钟有效期）
    const uploadUrl = await this.s3Client.getSignedUrl(
      'putObject',
      {
        Bucket: process.env.S3_PRIVATE_BUCKET,
        Key: fileKey,
        Expires: 15 * 60, // 15分钟
        ContentType: 'application/octet-stream'
      }
    );
    
    return { uploadUrl, fileKey };
  }
}
```

#### 5.1.2 S3存储桶安全配置

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "DenyDirectPublicAccess",
      "Effect": "Deny",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::aigc-private-assets/*",
      "Condition": {
        "Bool": {
          "aws:SecureTransport": "false"
        }
      }
    }
  ]
}
```

### 5.2 文件下载安全机制

```typescript
// 安全下载服务
class DownloadService {
  async generateDownloadUrl(
    userId: number,
    assetId: number
  ): Promise<string> {
    // 1. 验证购买记录
    const purchase = await this.transactionService.verifyPurchase(userId, assetId);
    if (!purchase) {
      throw new UnauthorizedException('Asset not purchased');
    }
    
    // 2. 获取资源文件键
    const asset = await this.assetService.findById(assetId);
    
    // 3. 生成临时下载URL（5分钟有效期）
    const downloadUrl = await this.s3Client.getSignedUrl(
      'getObject',
      {
        Bucket: process.env.S3_PRIVATE_BUCKET,
        Key: asset.s3FileKey,
        Expires: 5 * 60, // 5分钟
        ResponseContentDisposition: `attachment; filename="${asset.title}"`
      }
    );
    
    return downloadUrl;
  }
}
```

### 5.3 API安全策略

```typescript
// API速率限制配置
const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 100次请求
  message: 'Too many requests from this IP'
};

// 敏感操作额外限制
const sensitiveOperationsLimit = {
  windowMs: 60 * 60 * 1000, // 1小时
  max: 10, // 限制每个用户10次敏感操作
  keyGenerator: (req) => req.user.id,
  skipSuccessfulRequests: true
};
```

## 6. 财务系统架构

### 6.1 分佣引擎实现

```typescript
// 分佣计算服务
class CommissionService {
  calculateCommission(transaction: Transaction): CommissionResult {
    // 仅处理美元交易
    if (transaction.currency !== 'USD') {
      return null;
    }
    
    const asset = transaction.asset;
    const creator = asset.creator;
    
    // 计算历史销售次数（不包含当前交易）
    const salesCount = this.countPreviousSales(asset.id, 'USD');
    
    let platformSharePercent: number;
    
    if (creator.userRole === 'PERSONAL_CREATOR') {
      // 个人创作者: 5% + (销售次数 * 5%), 最高50%
      platformSharePercent = Math.min(5 + (salesCount * 5), 50);
    } else if (creator.userRole === 'ENTERPRISE_CREATOR') {
      // 企业创作者: 8% + (销售次数 * 8%), 最高56%
      platformSharePercent = Math.min(8 + (salesCount * 8), 56);
    }
    
    const creatorSharePercent = 100 - platformSharePercent;
    const platformAmount = transaction.amountUsd * (platformSharePercent / 100);
    const creatorAmount = transaction.amountUsd * (creatorSharePercent / 100);
    
    return {
      platformAmount,
      creatorAmount,
      platformSharePercent,
      creatorSharePercent
    };
  }
}
```

### 6.2 资金状态管理

```typescript
// 资金状态机
enum LedgerStatus {
  PENDING = 'PENDING',       // 7天保护期
  AVAILABLE = 'AVAILABLE',   // 可提现
  WITHDRAWN = 'WITHDRAWN',   // 已提现
  REFUNDED = 'REFUNDED'      // 已退款
}

// 定时任务：处理资金状态转换
class FinanceWorker {
  @Cron('0 0 * * *') // 每天凌晨执行
  async processLedgerEntries() {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    // 将7天前的PENDING状态转为AVAILABLE
    await this.ledgerService.updateStatus(
      { status: LedgerStatus.PENDING, createdAt: { $lt: sevenDaysAgo } },
      { status: LedgerStatus.AVAILABLE, clearedAt: new Date() }
    );
  }
}
```

### 6.3 积分系统实现

```typescript
// 积分服务
class PointsService {
  async purchasePoints(userId: number, usdAmount: number): Promise<void> {
    // 从系统配置获取积分汇率
    const pointsRate = await this.configService.get('POINTS_RATE'); // 例如: 100 points = $1
    const pointsAmount = usdAmount * pointsRate;
    
    // 创建积分购买交易记录
    const transaction = await this.transactionService.create({
      buyerId: userId,
      currency: 'USD',
      amountUsd: usdAmount,
      type: 'POINTS_PURCHASE'
    });
    
    // 创建积分账本条目
    await this.ledgerService.create({
      transactionId: transaction.id,
      userId,
      amount: pointsAmount,
      entryType: 'POINTS_PURCHASE',
      status: 'AVAILABLE'
    });
    
    // 更新用户积分余额
    await this.userService.updatePointsBalance(userId, pointsAmount);
  }
  
  async consumePoints(userId: number, pointsAmount: number): Promise<void> {
    const user = await this.userService.findById(userId);
    
    if (user.pointsBalance < pointsAmount) {
      throw new InsufficientPointsException();
    }
    
    // 扣除积分
    await this.userService.updatePointsBalance(userId, -pointsAmount);
    
    // 创建积分消费记录
    await this.ledgerService.create({
      userId,
      amount: pointsAmount,
      entryType: 'POINTS_DEBIT',
      status: 'AVAILABLE'
    });
  }
}
```

## 7. 部署架构

### 7.1 容器化配置

#### 7.1.1 Dockerfile（后端）

```dockerfile
# 多阶段构建
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
RUN npm run build

# 运行时环境
FROM node:18-alpine
WORKDIR /app
COPY --from=runtime /app/dist ./dist
COPY --from=runtime /app/node_modules ./node_modules
COPY package*.json ./

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001
USER nodejs

EXPOSE 3000
CMD ["node", "dist/main.js"]
```

#### 7.1.2 Dockerfile（前端）

```dockerfile
# 构建阶段
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

# 运行时环境
FROM nginx:alpine
COPY --from=builder /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 7.2 Docker Compose（开发环境）

```yaml
version: '3.8'
services:
  # 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: aigc_service_hub
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  # 后端服务
  api:
    build: .
    environment:
      NODE_ENV: development
      DATABASE_URL: ********************************************/aigc_service_hub
      REDIS_URL: redis://redis:6379
      AWS_REGION: us-west-2
      S3_PRIVATE_BUCKET: aigc-private-assets
      S3_PUBLIC_BUCKET: aigc-public-assets
      JWT_SECRET: your-jwt-secret
      PAYPAL_CLIENT_ID: your-paypal-client-id
      PAYPAL_CLIENT_SECRET: your-paypal-client-secret
    depends_on:
      - postgres
      - redis
    ports:
      - "3000:3000"
    volumes:
      - ./src:/app/src
      - ./package.json:/app/package.json

  # 前端服务
  web:
    build: ./frontend
    environment:
      REACT_APP_API_URL: http://localhost:3000
    ports:
      - "3001:80"
    depends_on:
      - api

volumes:
  postgres_data:
```

### 7.3 AWS ECS配置

#### 7.3.1 任务定义（Task Definition）

```json
{
  "family": "aigc-service-hub",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "api-server",
      "image": "your-ecr-repo/aigc-api:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:us-west-2:account:secret:db-connection"
        },
        {
          "name": "JWT_SECRET",
          "valueFrom": "arn:aws:secretsmanager:us-west-2:account:secret:jwt-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/aigc-service-hub",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### 7.3.2 服务配置（Service Definition）

```json
{
  "serviceName": "aigc-api-service",
  "cluster": "aigc-cluster",
  "taskDefinition": "aigc-service-hub:latest",
  "desiredCount": 2,
  "launchType": "FARGATE",
  "networkConfiguration": {
    "awsvpcConfiguration": {
      "subnets": [
        "subnet-********",
        "subnet-********"
      ],
      "securityGroups": [
        "sg-********"
      ],
      "assignPublicIp": "ENABLED"
    }
  },
  "loadBalancers": [
    {
      "targetGroupArn": "arn:aws:elasticloadbalancing:us-west-2:account:targetgroup/aigc-tg",
      "containerName": "api-server",
      "containerPort": 3000
    }
  ]
}
```

### 7.4 CI/CD流水线

#### 7.4.1 GitHub Actions配置

```yaml
name: Deploy to AWS ECS

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: aigc-service-hub
          IMAGE_TAG: ${{ github.sha }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

      - name: Deploy to ECS
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: task-definition.json
          service: aigc-api-service
          cluster: aigc-cluster
          wait-for-service-stability: true
```

## 8. 开发环境配置

### 8.1 环境变量配置

#### 8.1.1 后端环境变量（`.env`）

```bash
# 应用配置
NODE_ENV=development
PORT=3000
API_VERSION=v1

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/aigc_service_hub
DATABASE_POOL_SIZE=20

# Redis配置
REDIS_URL=redis://localhost:6379

# AWS配置
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
S3_PRIVATE_BUCKET=aigc-private-assets-dev
S3_PUBLIC_BUCKET=aigc-public-assets-dev

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=1d
REFRESH_TOKEN_EXPIRES_IN=7d

# PayPal配置
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_SANDBOX=true

# OAuth配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# 邮件配置
AWS_SES_REGION=us-west-2
FROM_EMAIL=<EMAIL>

# 应用URL
FRONTEND_URL=http://localhost:3001
BACKEND_URL=http://localhost:3000
```

#### 8.1.2 前端环境变量（`.env`）

```bash
# API配置
REACT_APP_API_URL=http://localhost:3000
REACT_APP_API_VERSION=v1

# OAuth配置
REACT_APP_GOOGLE_CLIENT_ID=your-google-client-id
REACT_APP_GITHUB_CLIENT_ID=your-github-client-id

# PayPal配置
REACT_APP_PAYPAL_CLIENT_ID=your-paypal-client-id

# 功能开关
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_SENTRY=false

# 文件上传配置
REACT_APP_MAX_FILE_SIZE=32212254720  # 30GB in bytes
REACT_APP_SUPPORTED_FILE_TYPES=.zip,.rar,.7z,.tar,.gz
```

### 8.2 本地开发启动脚本

#### 8.2.1 `package.json`脚本

```json
{
  "scripts": {
    "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"",
    "dev:backend": "nodemon --exec ts-node src/main.ts",
    "dev:frontend": "cd frontend && npm start",
    "build": "npm run build:backend && npm run build:frontend",
    "build:backend": "tsc",
    "build:frontend": "cd frontend && npm run build",
    "test": "jest",
    "test:e2e": "playwright test",
    "docker:dev": "docker-compose up -d",
    "docker:prod": "docker-compose -f docker-compose.prod.yml up -d",
    "db:migrate": "typeorm migration:run",
    "db:seed": "ts-node scripts/seed.ts",
    "lint": "eslint . --ext .ts,.tsx",
    "format": "prettier --write ."
  }
}
```

#### 8.2.2 数据库初始化脚本

```sql
-- init.sql
-- 创建数据库
CREATE DATABASE aigc_service_hub;

-- 切换到数据库
\c aigc_service_hub;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
('POINTS_RATE', '100', 'Points per USD (100 points = $1)'),
('MAX_FILE_SIZE', '32212254720', 'Maximum file size in bytes (30GB)'),
('COMMISSION_INDIVIDUAL_BASE', '5', 'Base commission for individual creators (%)'),
('COMMISSION_INDIVIDUAL_INCREMENT', '5', 'Commission increment for individual creators (%)'),
('COMMISSION_INDIVIDUAL_MAX', '50', 'Maximum commission for individual creators (%)'),
('COMMISSION_ENTERPRISE_BASE', '8', 'Base commission for enterprise creators (%)'),
('COMMISSION_ENTERPRISE_INCREMENT', '8', 'Commission increment for enterprise creators (%)'),
('COMMISSION_ENTERPRISE_MAX', '56', 'Maximum commission for enterprise creators (%)');

-- 插入默认管理员用户
INSERT INTO users (email, password_hash, display_name, user_role) VALUES
('<EMAIL>', '$2b$10$encrypted_password_hash', 'System Admin', 'ADMIN');

-- 插入默认标签
INSERT INTO tags (name, type) VALUES
('视频', 'CATEGORY'),
('音频', 'CATEGORY'),
('图片', 'CATEGORY'),
('文本', 'CATEGORY'),
('电商', 'STYLE'),
('动漫', 'STYLE'),
('建筑', 'STYLE'),
('摄影', 'STYLE');
```

### 8.3 开发工具配置

#### 8.3.1 TypeScript配置（`tsconfig.json`）

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "resolveJsonModule": true,
    "types": ["node", "jest"]
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "test"]
}
```

#### 8.3.2 ESLint配置（`.eslintrc.js`）

```javascript
module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint/eslint-plugin'],
  extends: [
    '@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js'],
  rules: {
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
  },
};
```

#### 8.3.3 Prettier配置（`.prettierrc`）

```json
{
  "singleQuote": true,
  "trailingComma": "es5",
  "tabWidth": 2,
  "semi": true,
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "avoid"
}
```

### 8.4 测试配置

#### 8.4.1 Jest配置（`jest.config.js`）

```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/main.ts',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
};
```

#### 8.4.2 Playwright配置（`playwright.config.ts`）

```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3001',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3001',
    reuseExistingServer: !process.env.CI,
  },
});
```

## 9. 监控与日志

### 9.1 应用监控

```typescript
// 健康检查端点
class HealthController {
  @Get('/health')
  async healthCheck() {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: await this.checkDatabase(),
        redis: await this.checkRedis(),
        s3: await this.checkS3(),
      }
    };
  }
}

// 指标收集
class MetricsService {
  private metrics = {
    totalTransactions: 0,
    totalRevenue: 0,
    activeUsers: 0,
    uploadedFiles: 0,
  };

  recordTransaction(amount: number) {
    this.metrics.totalTransactions++;
    this.metrics.totalRevenue += amount;
  }

  recordFileUpload() {
    this.metrics.uploadedFiles++;
  }
}
```

### 9.2 日志配置

```typescript
// Winston日志配置
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}
```

## 10. 安全检查清单

### 10.1 数据保护

- [ ] 所有敏感数据加密存储
- [ ] 密码使用bcrypt加盐哈希
- [ ] JWT令牌安全配置
- [ ] 环境变量保护
- [ ] 数据库连接加密

### 10.2 API安全

- [ ] 输入验证和清理
- [ ] SQL注入防护
- [ ] XSS防护
- [ ] CSRF防护
- [ ] 速率限制
- [ ] CORS配置

### 10.3 文件安全

- [ ] 文件类型验证
- [ ] 文件大小限制
- [ ] 恶意文件扫描
- [ ] 安全的文件存储
- [ ] 访问权限控制

### 10.4 基础设施安全

- [ ] HTTPS强制使用
- [ ] 安全头配置
- [ ] 网络访问控制
- [ ] 日志记录和监控
- [ ] 定期安全更新

## 11. 性能优化策略

### 11.1 数据库优化

```sql
-- 分区表策略（大交易量时）
CREATE TABLE transactions_partitioned (
    LIKE transactions INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- 索引优化
CREATE INDEX CONCURRENTLY idx_transactions_buyer_created 
ON transactions (buyer_id, created_at DESC);

-- 查询优化
EXPLAIN ANALYZE SELECT * FROM transactions 
WHERE buyer_id = $1 AND created_at > $2 
ORDER BY created_at DESC LIMIT 10;
```

### 11.2 缓存策略

```typescript
// Redis缓存配置
class CacheService {
  private redis = new Redis(process.env.REDIS_URL);

  async cacheAssetList(key: string, data: any, ttl: number = 300) {
    await this.redis.setex(key, ttl, JSON.stringify(data));
  }

  async getCachedAssetList(key: string) {
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  async invalidateAssetCache(assetId: number) {
    const patterns = [
      `assets:list:*`,
      `asset:${assetId}:*`,
      `creator:*:assets`
    ];
    
    for (const pattern of patterns) {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    }
  }
}
```

### 11.3 CDN配置

```typescript
// CloudFront配置
const cloudfrontConfig = {
  distribution: {
    Origins: [
      {
        Id: 'api-origin',
        DomainName: 'api.aigcservicehub.com',
        CustomOriginConfig: {
          HTTPPort: 443,
          HTTPSPort: 443,
          OriginProtocolPolicy: 'https-only'
        }
      }
    ],
    DefaultCacheBehavior: {
      TargetOriginId: 'api-origin',
      ViewerProtocolPolicy: 'redirect-to-https',
      CachePolicyId: 'managed-caching-optimized',
      TTL: 86400
    }
  }
};
```

## 12. 总结

本技术架构文档为AIGC Service Hub MVP 1.0提供了完整的技术实现方案。主要特点包括：

1. **现代化技术栈**: Node.js + TypeScript + React + PostgreSQL
2. **云原生架构**: 基于AWS的容器化部署
3. **安全设计**: 多层次的安全防护机制
4. **可扩展性**: 支持未来功能扩展的模块化设计
5. **高可用性**: 负载均衡、容错和监控机制

该架构充分考虑了PRD中的所有业务需求，特别是大文件上传、复杂财务分佣、安全交付等核心功能的技术实现。通过严格的权限控制、完善的错误处理和全面的测试策略，确保平台的稳定性和安全性。

接下来的开发阶段可以基于此架构文档进行详细的编码实现。