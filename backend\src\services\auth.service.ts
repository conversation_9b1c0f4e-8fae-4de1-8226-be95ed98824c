import { Pool } from 'pg';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { logger } from '../utils/logger';
import { ValidationError, AuthenticationError, ConflictError } from '../utils/errors';
import { getDb } from '../database/connection';

interface JwtPayload {
  userId: number;
  email: string;
  userRole: string;
  iat?: number;
  exp?: number;
}

interface RegisterRequest {
  email: string;
  password: string;
  displayName: string;
  userRole: string;
}

interface LoginRequest {
  email: string;
  password: string;
}

interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    id: number;
    email: string;
    displayName: string;
    userRole: string;
    pointsBalance: number;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

export class AuthService {
  private db: Pool;

  constructor() {
    this.db = getDb();
  }

  // 用户注册
  async register(registerData: RegisterRequest): Promise<AuthResponse> {
    const { email, password, displayName, userRole } = registerData;

    try {
      // 检查用户是否已存在
      const existingUser = await this.findUserByEmail(email);
      if (existingUser) {
        throw new ConflictError('User with this email already exists');
      }

      // 密码加密
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // 创建用户
      const query = `
        INSERT INTO users (email, password_hash, display_name, user_role)
        VALUES ($1, $2, $3, $4)
        RETURNING id, email, display_name, user_role, points_balance, is_active, created_at, updated_at
      `;
      
      const result = await this.db.query(query, [email, passwordHash, displayName, userRole]);
      const user = result.rows[0];

      // 生成JWT令牌
      const tokens = this.generateTokens(user);

      // 记录业务日志
      logger.info('User registered', { userId: user.id, email, userRole });

      return {
        ...tokens,
        user: {
          id: user.id,
          email: user.email,
          displayName: user.display_name,
          userRole: user.user_role,
          pointsBalance: user.points_balance,
          isActive: user.is_active,
          createdAt: user.created_at,
          updatedAt: user.updated_at,
        },
      };
    } catch (error) {
      logger.error('Registration failed:', error);
      throw error;
    }
  }

  // 用户登录
  async login(loginData: LoginRequest): Promise<AuthResponse> {
    const { email, password } = loginData;

    try {
      // 查找用户
      const user = await this.findUserByEmail(email);
      if (!user) {
        throw new AuthenticationError('Invalid credentials');
      }

      // 检查用户是否激活
      if (!user.is_active) {
        throw new AuthenticationError('Account is not active');
      }

      // 验证密码
      const isPasswordValid = await bcrypt.compare(password, user.password_hash);
      if (!isPasswordValid) {
        throw new AuthenticationError('Invalid credentials');
      }

      // 生成JWT令牌
      const tokens = this.generateTokens(user);

      // 记录业务日志
      logger.info('User logged in', { userId: user.id, email });

      return {
        ...tokens,
        user: {
          id: user.id,
          email: user.email,
          displayName: user.display_name,
          userRole: user.user_role,
          pointsBalance: user.points_balance,
          isActive: user.is_active,
          createdAt: user.created_at,
          updatedAt: user.updated_at,
        },
      };
    } catch (error) {
      logger.error('Login failed:', error);
      throw error;
    }
  }

  // 刷新令牌
  async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // 验证刷新令牌
      const decoded = jwt.verify(refreshToken, config.jwt.secret) as JwtPayload;
      
      // 查找用户
      const user = await this.findUserById(decoded.userId);
      if (!user || !user.is_active) {
        throw new AuthenticationError('Invalid token');
      }

      // 生成新的令牌
      const tokens = this.generateTokens(user);

      // 记录业务日志
      logger.info('Token refreshed', { userId: user.id });

      return tokens;
    } catch (error) {
      logger.error('Token refresh failed:', error);
      throw new AuthenticationError('Invalid refresh token');
    }
  }

  // 验证访问令牌
  async verifyAccessToken(token: string): Promise<any> {
    try {
      const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;
      
      const user = await this.findUserById(decoded.userId);
      if (!user || !user.is_active) {
        throw new AuthenticationError('Invalid token');
      }

      return user;
    } catch (error) {
      throw new AuthenticationError('Invalid access token');
    }
  }

  // 修改密码
  async changePassword(userId: number, currentPassword: string, newPassword: string): Promise<void> {
    try {
      const user = await this.findUserById(userId);
      if (!user) {
        throw new ValidationError('User not found');
      }

      // 验证当前密码
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
      if (!isCurrentPasswordValid) {
        throw new AuthenticationError('Current password is incorrect');
      }

      // 加密新密码
      const saltRounds = 12;
      const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

      // 更新密码
      const query = `
        UPDATE users 
        SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `;
      
      await this.db.query(query, [newPasswordHash, userId]);

      // 记录业务日志
      logger.info('Password changed', { userId });
    } catch (error) {
      logger.error('Password change failed:', error);
      throw error;
    }
  }

  // 私有方法：生成JWT令牌
  private generateTokens(user: any): { accessToken: string; refreshToken: string } {
    const payload = {
      userId: user.id,
      email: user.email,
      userRole: user.user_role,
    };

    // @ts-ignore - JWT类型定义问题，暂时忽略
    const accessToken = jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
    });

    // @ts-ignore - JWT类型定义问题，暂时忽略
    const refreshToken = jwt.sign(
      { userId: user.id },
      config.jwt.secret,
      { expiresIn: config.jwt.refreshExpiresIn }
    );

    return { accessToken, refreshToken };
  }

  // 重置密码请求
  async requestPasswordReset(email: string): Promise<void> {
    try {
      const user = await this.findUserByEmail(email);
      if (!user) {
        // 出于安全考虑，即使用户不存在也不抛出错误
        return;
      }

      // 生成重置令牌
      // @ts-ignore - JWT类型定义问题，暂时忽略
      const resetToken = jwt.sign(
        { userId: user.id, type: 'password_reset' },
        config.jwt.secret,
        { expiresIn: '1h' }
      );

      // 这里应该发送邮件，暂时记录日志
      logger.info('Password reset requested', { userId: user.id, email });

      // TODO: 发送重置邮件
    } catch (error) {
      logger.error('Password reset request failed:', error);
      throw error;
    }
  }

  // 重置密码
  async resetPassword(resetToken: string, newPassword: string): Promise<void> {
    try {
      // 验证重置令牌
      const decoded = jwt.verify(resetToken, config.jwt.secret) as any;
      
      if (decoded.type !== 'password_reset') {
        throw new AuthenticationError('Invalid reset token');
      }

      // 加密新密码
      const saltRounds = 12;
      const passwordHash = await bcrypt.hash(newPassword, saltRounds);

      // 更新密码
      const query = `
        UPDATE users
        SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `;
      
      await this.db.query(query, [passwordHash, decoded.userId]);

      logger.info('Password reset completed', { userId: decoded.userId });
    } catch (error) {
      logger.error('Password reset failed:', error);
      throw new AuthenticationError('Invalid or expired reset token');
    }
  }

  // 检查邮箱是否可用
  async checkEmailAvailability(email: string): Promise<{ available: boolean }> {
    try {
      const existingUser = await this.findUserByEmail(email);
      return { available: !existingUser };
    } catch (error) {
      logger.error('Check email availability failed:', error);
      throw error;
    }
  }

  // 私有方法：根据邮箱查找用户
  private async findUserByEmail(email: string): Promise<any> {
    const query = `
      SELECT id, email, password_hash, display_name, user_role, points_balance, 
             is_active, created_at, updated_at
      FROM users
      WHERE email = $1
    `;
    
    const result = await this.db.query(query, [email]);
    return result.rows[0];
  }

  // 私有方法：根据ID查找用户
  private async findUserById(id: number): Promise<any> {
    const query = `
      SELECT id, email, password_hash, display_name, user_role, points_balance, 
             is_active, created_at, updated_at
      FROM users
      WHERE id = $1
    `;
    
    const result = await this.db.query(query, [id]);
    return result.rows[0];
  }
}

export default AuthService;