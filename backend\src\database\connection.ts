import { Pool, PoolClient } from 'pg';
import Redis from 'ioredis';
import config from '@/config';
import { logger } from '@/utils/logger';

// PostgreSQL连接池
let pgPool: Pool | null = null;

export const createPostgresPool = (): Pool => {
  if (pgPool) {
    return pgPool;
  }

  const poolConfig = {
    connectionString: config.database.url,
    max: config.database.poolSize,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
    ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
  };

  pgPool = new Pool(poolConfig);

  // 连接事件监听
  pgPool.on('connect', (client: PoolClient) => {
    logger.info('PostgreSQL client connected');
  });

  pgPool.on('error', (err: Error) => {
    logger.error('PostgreSQL pool error:', err);
  });

  pgPool.on('remove', () => {
    logger.info('PostgreSQL client removed');
  });

  return pgPool;
};

// Redis连接
let redisClient: Redis | null = null;

export const createRedisClient = (): Redis => {
  if (redisClient) {
    return redisClient;
  }

  const redisConfig = {
    host: config.redis.url.replace('redis://', '').split(':')[0],
    port: parseInt(config.redis.url.split(':')[2] || '6379'),
    password: config.redis.password || undefined,
    db: config.redis.db,
    connectTimeout: 10000,
    commandTimeout: 5000,
    lazyConnect: true,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    keyPrefix: 'aigc:',
  };

  redisClient = new Redis(redisConfig);

  // Redis事件监听
  redisClient.on('connect', () => {
    logger.info('Redis client connected');
  });

  redisClient.on('ready', () => {
    logger.info('Redis client ready');
  });

  redisClient.on('error', (err: Error) => {
    logger.error('Redis client error:', err);
  });

  redisClient.on('close', () => {
    logger.info('Redis client closed');
  });

  redisClient.on('reconnecting', () => {
    logger.info('Redis client reconnecting');
  });

  return redisClient;
};

// 数据库管理器类
export class DatabaseManager {
  private pgPool: Pool | null = null;
  private redisClient: Redis | null = null;
  private isConnected: boolean = false;

  async connect(): Promise<void> {
    try {
      // 创建PostgreSQL连接池
      this.pgPool = createPostgresPool();
      
      // 测试PostgreSQL连接
      const client = await this.pgPool.connect();
      const result = await client.query('SELECT NOW()');
      client.release();
      logger.info('PostgreSQL connected successfully at:', result.rows[0].now);

      // 创建Redis连接
      this.redisClient = createRedisClient();
      
      // 测试Redis连接
      await this.redisClient.ping();
      logger.info('Redis connected successfully');

      this.isConnected = true;
    } catch (error) {
      logger.error('Database connection failed:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.pgPool) {
        await this.pgPool.end();
        this.pgPool = null;
      }

      if (this.redisClient) {
        await this.redisClient.quit();
        this.redisClient = null;
      }

      this.isConnected = false;
      logger.info('Database connections closed');
    } catch (error) {
      logger.error('Error closing database connections:', error);
      throw error;
    }
  }

  getPostgresPool(): Pool {
    if (!this.pgPool) {
      throw new Error('PostgreSQL pool not initialized');
    }
    return this.pgPool;
  }

  getRedisClient(): Redis {
    if (!this.redisClient) {
      throw new Error('Redis client not initialized');
    }
    return this.redisClient;
  }

  async healthCheck(): Promise<{ postgres: boolean; redis: boolean; timestamp: string }> {
    const health = {
      postgres: false,
      redis: false,
      timestamp: new Date().toISOString(),
    };

    try {
      // 检查PostgreSQL
      if (this.pgPool) {
        const client = await this.pgPool.connect();
        await client.query('SELECT 1');
        client.release();
        health.postgres = true;
      }

      // 检查Redis
      if (this.redisClient) {
        await this.redisClient.ping();
        health.redis = true;
      }
    } catch (error) {
      logger.error('Health check failed:', error);
    }

    return health;
  }

  getConnectionStats() {
    const stats = {
      postgres: {
        totalCount: 0,
        idleCount: 0,
        waitingCount: 0,
      },
      redis: {
        status: 'disconnected',
      },
    };

    if (this.pgPool) {
      stats.postgres.totalCount = this.pgPool.totalCount;
      stats.postgres.idleCount = this.pgPool.idleCount;
      stats.postgres.waitingCount = this.pgPool.waitingCount;
    }

    if (this.redisClient) {
      stats.redis.status = this.redisClient.status;
    }

    return stats;
  }
}

// 全局数据库管理器实例
export const dbManager = new DatabaseManager();

// 便捷方法
export const getDb = (): Pool => dbManager.getPostgresPool();
export const getRedis = (): Redis => dbManager.getRedisClient();

// 初始化数据库连接
export const initDatabase = async (): Promise<void> => {
  await dbManager.connect();
};

// 关闭数据库连接
export const closeDatabase = async (): Promise<void> => {
  await dbManager.disconnect();
};

// 优雅关闭处理
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, closing database connections...');
  await closeDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, closing database connections...');
  await closeDatabase();
  process.exit(0);
});