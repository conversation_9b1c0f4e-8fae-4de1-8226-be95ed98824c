import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Stack,
  Typography,
  Divider,
  Fade,
  Popper,
  ClickAwayListener,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Search,
  Clear,
  TrendingUp,
  History,
  FilterList,
  Tune,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

interface SearchSuggestion {
  id: string;
  text: string;
  type: 'recent' | 'trending' | 'suggestion';
  category?: string;
  count?: number;
}

interface ModernSearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: (query: string) => void;
  onFilterClick?: () => void;
  placeholder?: string;
  suggestions?: SearchSuggestion[];
  recentSearches?: string[];
  trendingSearches?: string[];
  loading?: boolean;
}

const ModernSearchBar: React.FC<ModernSearchBarProps> = ({
  value,
  onChange,
  onSearch,
  onFilterClick,
  placeholder = "搜索AI生成内容、工具、模型...",
  suggestions = [],
  recentSearches = [],
  trendingSearches = [],
  loading = false,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [open, setOpen] = useState(false);
  const [focused, setFocused] = useState(false);
  const anchorRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 默认建议数据
  const defaultSuggestions: SearchSuggestion[] = [
    { id: '1', text: 'AI头像生成器', type: 'trending', category: '图像', count: 1250 },
    { id: '2', text: '视频风格转换', type: 'trending', category: '视频', count: 890 },
    { id: '3', text: '音乐创作AI', type: 'trending', category: '音频', count: 650 },
    { id: '4', text: 'ChatGPT提示词', type: 'trending', category: '文本', count: 2100 },
    { id: '5', text: 'Stable Diffusion模型', type: 'trending', category: '模型', count: 1800 },
  ];

  const defaultRecentSearches = [
    'AI绘画工具',
    '视频剪辑模板',
    '3D模型生成',
    '语音合成',
  ];

  const allSuggestions = suggestions.length > 0 ? suggestions : defaultSuggestions;
  const allRecentSearches = recentSearches.length > 0 ? recentSearches : defaultRecentSearches;

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setOpen(false);
        inputRef.current?.blur();
      }
      if (event.key === '/' && !focused) {
        event.preventDefault();
        inputRef.current?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [focused]);

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    onChange(newValue);
    setOpen(newValue.length > 0 || focused);
  };

  const handleInputFocus = () => {
    setFocused(true);
    setOpen(true);
  };

  const handleInputBlur = () => {
    setFocused(false);
    // 延迟关闭以允许点击建议项
    setTimeout(() => setOpen(false), 200);
  };

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion);
    onSearch(suggestion);
    setOpen(false);
    inputRef.current?.blur();
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    if (value.trim()) {
      onSearch(value.trim());
      setOpen(false);
      inputRef.current?.blur();
    }
  };

  const handleClear = () => {
    onChange('');
    inputRef.current?.focus();
  };

  const renderSuggestions = () => {
    if (!open) return null;

    return (
      <Popper
        open={open}
        anchorEl={anchorRef.current}
        placement="bottom-start"
        style={{ width: anchorRef.current?.offsetWidth, zIndex: 1300 }}
      >
        <Fade in={open}>
          <Paper
            elevation={8}
            sx={{
              mt: 1,
              maxHeight: 400,
              overflow: 'auto',
              border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
            }}
          >
            {/* 搜索建议 */}
            {value && (
              <Box sx={{ p: 2 }}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  搜索建议
                </Typography>
                <List dense>
                  {allSuggestions
                    .filter(s => s.text.toLowerCase().includes(value.toLowerCase()))
                    .slice(0, 5)
                    .map((suggestion) => (
                      <ListItem
                        key={suggestion.id}
                        button
                        onClick={() => handleSuggestionClick(suggestion.text)}
                        sx={{
                          borderRadius: 1,
                          '&:hover': {
                            bgcolor: alpha(theme.palette.primary.main, 0.08),
                          },
                        }}
                      >
                        <ListItemIcon>
                          <Search sx={{ fontSize: 20 }} />
                        </ListItemIcon>
                        <ListItemText
                          primary={suggestion.text}
                          secondary={suggestion.category}
                        />
                        {suggestion.count && (
                          <Typography variant="body2" color="text.secondary">
                            {suggestion.count}+
                          </Typography>
                        )}
                      </ListItem>
                    ))}
                </List>
              </Box>
            )}

            {/* 热门搜索 */}
            {!value && (
              <Box sx={{ p: 2 }}>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  gutterBottom
                  sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                >
                  <TrendingUp sx={{ fontSize: 16 }} />
                  热门搜索
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" gap={1}>
                  {allSuggestions.slice(0, 8).map((suggestion) => (
                    <Chip
                      key={suggestion.id}
                      label={suggestion.text}
                      size="small"
                      variant="outlined"
                      clickable
                      onClick={() => handleSuggestionClick(suggestion.text)}
                      sx={{
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.08),
                          borderColor: 'primary.main',
                        },
                      }}
                    />
                  ))}
                </Stack>
              </Box>
            )}

            {/* 最近搜索 */}
            {!value && allRecentSearches.length > 0 && (
              <>
                <Divider />
                <Box sx={{ p: 2 }}>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    gutterBottom
                    sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                  >
                    <History sx={{ fontSize: 16 }} />
                    最近搜索
                  </Typography>
                  <List dense>
                    {allRecentSearches.slice(0, 4).map((search, index) => (
                      <ListItem
                        key={index}
                        button
                        onClick={() => handleSuggestionClick(search)}
                        sx={{
                          borderRadius: 1,
                          '&:hover': {
                            bgcolor: alpha(theme.palette.primary.main, 0.08),
                          },
                        }}
                      >
                        <ListItemIcon>
                          <History sx={{ fontSize: 20, color: 'text.secondary' }} />
                        </ListItemIcon>
                        <ListItemText primary={search} />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              </>
            )}

            {/* 快捷提示 */}
            {!value && (
              <>
                <Divider />
                <Box sx={{ p: 2, bgcolor: alpha(theme.palette.primary.main, 0.04) }}>
                  <Typography variant="body2" color="text.secondary">
                    💡 提示：按 <kbd>/</kbd> 快速搜索，支持中英文关键词
                  </Typography>
                </Box>
              </>
            )}
          </Paper>
        </Fade>
      </Popper>
    );
  };

  return (
    <ClickAwayListener onClickAway={() => setOpen(false)}>
      <Box ref={anchorRef} sx={{ position: 'relative', width: '100%' }}>
        <Box
          component="form"
          onSubmit={handleSubmit}
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          }}
        >
          <TextField
            ref={inputRef}
            fullWidth
            value={value}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            placeholder={placeholder}
            variant="outlined"
            size="medium"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search sx={{ color: 'text.secondary' }} />
                </InputAdornment>
              ),
              endAdornment: value && (
                <InputAdornment position="end">
                  <IconButton
                    onClick={handleClear}
                    edge="end"
                    size="small"
                    sx={{ mr: -0.5 }}
                  >
                    <Clear />
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                borderRadius: 3,
                bgcolor: 'background.paper',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: alpha(theme.palette.divider, 0.12),
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: alpha(theme.palette.primary.main, 0.25),
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main',
                  borderWidth: 2,
                },
                transition: 'all 0.2s ease',
              },
            }}
          />
          
          {onFilterClick && (
            <IconButton
              onClick={onFilterClick}
              sx={{
                bgcolor: 'background.paper',
                border: `1px solid ${alpha(theme.palette.divider, 0.12)}`,
                borderRadius: 2,
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.08),
                  borderColor: 'primary.main',
                },
              }}
            >
              <Tune />
            </IconButton>
          )}
        </Box>

        {renderSuggestions()}
      </Box>
    </ClickAwayListener>
  );
};

export default ModernSearchBar;
