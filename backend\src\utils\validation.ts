import Joi from 'joi';
import { ValidationError } from './errors';

// 基础验证规则
export const validationRules = {
  // 用户相关
  email: Joi.string().email().required().messages({
    'string.email': 'Invalid email format',
    'any.required': 'Email is required',
  }),
  
  password: Joi.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/).required().messages({
    'string.min': 'Password must be at least 8 characters long',
    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
    'any.required': 'Password is required',
  }),
  
  displayName: Joi.string().min(2).max(50).required().messages({
    'string.min': 'Display name must be at least 2 characters long',
    'string.max': 'Display name must not exceed 50 characters',
    'any.required': 'Display name is required',
  }),
  
  userRole: Joi.string().valid('PERSONAL_CREATOR', 'ENTERPRISE_CREATOR').required().messages({
    'any.only': 'User role must be either PERSONAL_CREATOR or ENTERPRISE_CREATOR',
    'any.required': 'User role is required',
  }),

  // 资源相关
  assetTitle: Joi.string().min(1).max(255).required().messages({
    'string.min': 'Asset title cannot be empty',
    'string.max': 'Asset title must not exceed 255 characters',
    'any.required': 'Asset title is required',
  }),
  
  assetDescription: Joi.string().max(5000).optional().messages({
    'string.max': 'Asset description must not exceed 5000 characters',
  }),
  
  assetType: Joi.string().valid('MODEL', 'LORA', 'WORKFLOW', 'PROMPT', 'TOOL').required().messages({
    'any.only': 'Asset type must be one of: MODEL, LORA, WORKFLOW, PROMPT, TOOL',
    'any.required': 'Asset type is required',
  }),
  
  priceUsd: Joi.number().positive().precision(2).optional().messages({
    'number.positive': 'USD price must be positive',
    'number.precision': 'USD price can have at most 2 decimal places',
  }),
  
  pricePoints: Joi.number().integer().positive().optional().messages({
    'number.integer': 'Points price must be an integer',
    'number.positive': 'Points price must be positive',
  }),
  
  tags: Joi.array().items(Joi.string().min(1).max(50)).max(20).optional().messages({
    'array.max': 'Maximum 20 tags allowed',
    'string.min': 'Tag cannot be empty',
    'string.max': 'Tag must not exceed 50 characters',
  }),

  // 文件相关
  fileKey: Joi.string().required().messages({
    'any.required': 'File key is required',
  }),
  
  fileSize: Joi.number().integer().positive().max(32212254720).required().messages({
    'number.integer': 'File size must be an integer',
    'number.positive': 'File size must be positive',
    'number.max': 'File size must not exceed 30GB',
    'any.required': 'File size is required',
  }),
  
  fileName: Joi.string().max(255).required().messages({
    'string.max': 'File name must not exceed 255 characters',
    'any.required': 'File name is required',
  }),

  // 交易相关
  currency: Joi.string().valid('USD', 'POINTS').required().messages({
    'any.only': 'Currency must be either USD or POINTS',
    'any.required': 'Currency is required',
  }),

  // 财务相关
  withdrawalAmount: Joi.number().positive().min(10).precision(2).required().messages({
    'number.positive': 'Withdrawal amount must be positive',
    'number.min': 'Minimum withdrawal amount is $10.00',
    'number.precision': 'Withdrawal amount can have at most 2 decimal places',
    'any.required': 'Withdrawal amount is required',
  }),
  
  paypalEmail: Joi.string().email().required().messages({
    'string.email': 'Invalid PayPal email format',
    'any.required': 'PayPal email is required',
  }),

  // 查询参数
  page: Joi.number().integer().min(1).default(1).messages({
    'number.integer': 'Page must be an integer',
    'number.min': 'Page must be at least 1',
  }),
  
  limit: Joi.number().integer().min(1).max(100).default(20).messages({
    'number.integer': 'Limit must be an integer',
    'number.min': 'Limit must be at least 1',
    'number.max': 'Limit must not exceed 100',
  }),
  
  sortBy: Joi.string().valid('created_at', 'updated_at', 'price_usd', 'download_count').default('created_at').messages({
    'any.only': 'Sort by must be one of: created_at, updated_at, price_usd, download_count',
  }),
  
  sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
    'any.only': 'Sort order must be either asc or desc',
  }),

  // ID相关
  id: Joi.number().integer().positive().required().messages({
    'number.integer': 'ID must be an integer',
    'number.positive': 'ID must be positive',
    'any.required': 'ID is required',
  }),
  
  optionalId: Joi.number().integer().positive().optional().messages({
    'number.integer': 'ID must be an integer',
    'number.positive': 'ID must be positive',
  }),
};

// 组合验证schema
export const validationSchemas = {
  // 用户注册
  register: Joi.object({
    email: validationRules.email,
    password: validationRules.password,
    displayName: validationRules.displayName,
    userRole: validationRules.userRole,
  }),

  // 用户登录
  login: Joi.object({
    email: validationRules.email,
    password: Joi.string().required().messages({
      'any.required': 'Password is required',
    }),
  }),

  // 刷新令牌
  refreshToken: Joi.object({
    refreshToken: Joi.string().required().messages({
      'any.required': 'Refresh token is required',
    }),
  }),

  // 创建资源
  createAsset: Joi.object({
    title: validationRules.assetTitle,
    description: validationRules.assetDescription,
    assetType: validationRules.assetType,
    priceUsd: validationRules.priceUsd,
    pricePoints: validationRules.pricePoints,
    categories: validationRules.tags,
    styles: validationRules.tags,
    coverImageUrl: Joi.string().uri().optional().messages({
      'string.uri': 'Cover image URL must be a valid URI',
    }),
  }).custom((value, helpers) => {
    // 至少要有一个价格
    if (!value.priceUsd && !value.pricePoints) {
      return helpers.error('any.custom', { message: 'At least one price (USD or Points) is required' });
    }
    return value;
  }),

  // 更新资源
  updateAsset: Joi.object({
    title: validationRules.assetTitle.optional(),
    description: validationRules.assetDescription,
    priceUsd: validationRules.priceUsd,
    pricePoints: validationRules.pricePoints,
    categories: validationRules.tags,
    styles: validationRules.tags,
    coverImageUrl: Joi.string().uri().optional().messages({
      'string.uri': 'Cover image URL must be a valid URI',
    }),
    status: Joi.string().valid('DRAFT', 'PUBLISHED', 'ARCHIVED').optional().messages({
      'any.only': 'Status must be one of: DRAFT, PUBLISHED, ARCHIVED',
    }),
  }),

  // 文件上传确认
  confirmUpload: Joi.object({
    fileKey: validationRules.fileKey,
    fileSize: validationRules.fileSize,
    fileName: validationRules.fileName,
  }),

  // 购买请求
  purchase: Joi.object({
    assetId: validationRules.id,
    currency: validationRules.currency,
  }),

  // 提现请求
  withdrawal: Joi.object({
    amount: validationRules.withdrawalAmount,
    paypalEmail: validationRules.paypalEmail,
  }),

  // 查询参数
  pagination: Joi.object({
    page: validationRules.page,
    limit: validationRules.limit,
  }),

  // 资源查询
  assetQuery: Joi.object({
    page: validationRules.page,
    limit: validationRules.limit,
    category: Joi.string().optional(),
    style: Joi.string().optional(),
    assetType: validationRules.assetType.optional(),
    sortBy: validationRules.sortBy,
    sortOrder: validationRules.sortOrder,
    search: Joi.string().max(100).optional().messages({
      'string.max': 'Search term must not exceed 100 characters',
    }),
  }),

  // 交易查询
  transactionQuery: Joi.object({
    page: validationRules.page,
    limit: validationRules.limit,
    currency: validationRules.currency.optional(),
    status: Joi.string().valid('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED').optional().messages({
      'any.only': 'Status must be one of: PENDING, COMPLETED, FAILED, REFUNDED',
    }),
    startDate: Joi.date().optional(),
    endDate: Joi.date().optional(),
  }),

  // 路径参数
  pathId: Joi.object({
    id: validationRules.id,
  }),

  // 更新用户资料
  updateProfile: Joi.object({
    displayName: validationRules.displayName.optional(),
    currentPassword: Joi.string().when('newPassword', {
      is: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional(),
    }).messages({
      'any.required': 'Current password is required when changing password',
    }),
    newPassword: validationRules.password.optional(),
  }),

  // 管理员操作
  adminUpdateUser: Joi.object({
    displayName: validationRules.displayName.optional(),
    isActive: Joi.boolean().optional(),
    userRole: Joi.string().valid('PERSONAL_CREATOR', 'ENTERPRISE_CREATOR', 'ADMIN').optional().messages({
      'any.only': 'User role must be one of: PERSONAL_CREATOR, ENTERPRISE_CREATOR, ADMIN',
    }),
  }),

  // 处理提现请求
  processWithdrawal: Joi.object({
    action: Joi.string().valid('APPROVE', 'REJECT').required().messages({
      'any.only': 'Action must be either APPROVE or REJECT',
      'any.required': 'Action is required',
    }),
    adminNotes: Joi.string().max(500).optional().messages({
      'string.max': 'Admin notes must not exceed 500 characters',
    }),
    rejectionReason: Joi.string().max(500).when('action', {
      is: 'REJECT',
      then: Joi.required(),
      otherwise: Joi.optional(),
    }).messages({
      'string.max': 'Rejection reason must not exceed 500 characters',
      'any.required': 'Rejection reason is required when rejecting withdrawal',
    }),
  }),

  // 退款请求
  refund: Joi.object({
    transactionId: validationRules.id,
    reason: Joi.string().max(500).required().messages({
      'string.max': 'Refund reason must not exceed 500 characters',
      'any.required': 'Refund reason is required',
    }),
  }),
};

// 验证中间件
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));
      
      throw new ValidationError('Validation failed', details);
    }
    
    // 将验证后的值赋给req.body
    req.body = value;
    next();
  };
};

// 验证查询参数
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.query, { abortEarly: false });
    
    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));
      
      throw new ValidationError('Query validation failed', details);
    }
    
    // 将验证后的值赋给req.query
    req.query = value;
    next();
  };
};

// 验证路径参数
export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.params, { abortEarly: false });
    
    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));
      
      throw new ValidationError('Parameter validation failed', details);
    }
    
    // 将验证后的值赋给req.params
    req.params = value;
    next();
  };
};

// 通用验证函数
export const validateData = <T>(schema: Joi.ObjectSchema, data: any): T => {
  const { error, value } = schema.validate(data, { abortEarly: false });
  
  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message,
    }));
    
    throw new ValidationError('Data validation failed', details);
  }
  
  return value;
};

// 验证文件上传
export const validateFileUpload = (req: any, res: any, next: any) => {
  if (!req.file) {
    throw new ValidationError('No file uploaded');
  }
  
  const allowedMimeTypes = [
    'application/zip',
    'application/x-zip-compressed',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
    'application/gzip',
    'application/x-tar',
    'application/octet-stream',
  ];
  
  if (!allowedMimeTypes.includes(req.file.mimetype)) {
    throw new ValidationError('Invalid file type. Only compressed files are allowed.');
  }
  
  // 30GB限制
  if (req.file.size > 32212254720) {
    throw new ValidationError('File size too large. Maximum size is 30GB.');
  }
  
  next();
};

// 验证邮箱格式
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// 验证密码强度
export const isStrongPassword = (password: string): boolean => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  
  return password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers;
};

// 验证URL格式
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// 清理HTML标签
export const sanitizeHtml = (html: string): string => {
  // 简单的HTML清理，实际项目中可能需要使用专门的库如DOMPurify
  return html.replace(/<script[^>]*>.*?<\/script>/gi, '')
             .replace(/<[^>]*>/g, '')
             .trim();
};

// 验证积分数量
export const validatePoints = (points: number): boolean => {
  return Number.isInteger(points) && points > 0;
};

// 验证金额
export const validateAmount = (amount: number): boolean => {
  return Number.isFinite(amount) && amount > 0 && Number((amount * 100).toFixed(0)) / 100 === amount;
};