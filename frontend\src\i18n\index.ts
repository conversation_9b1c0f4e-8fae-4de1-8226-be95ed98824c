import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

const resources = {
  zh: {
    translation: {
      // 通用
      'common.loading': '加载中...',
      'common.error': '错误',
      'common.success': '成功',
      'common.cancel': '取消',
      'common.confirm': '确认',
      'common.save': '保存',
      'common.edit': '编辑',
      'common.delete': '删除',
      'common.add': '添加',
      'common.search': '搜索',
      'common.filter': '筛选',
      'common.export': '导出',
      'common.import': '导入',
      'common.upload': '上传',
      'common.download': '下载',
      'common.view': '查看',
      'common.details': '详情',
      'common.back': '返回',
      'common.next': '下一步',
      'common.previous': '上一步',
      'common.close': '关闭',
      'common.open': '打开',
      'common.expand': '展开',
      'common.collapse': '收起',
      'common.select': '选择',
      'common.selectAll': '全选',
      'common.clear': '清空',
      'common.reset': '重置',
      'common.refresh': '刷新',
      'common.settings': '设置',
      'common.profile': '个人资料',
      'common.logout': '退出登录',
      'common.login': '登录',
      'common.register': '注册',
      'common.forgotPassword': '忘记密码',
      'common.changePassword': '修改密码',
      'common.dashboard': '仪表板',
      'common.assets': '资产管理',
      'common.trading': '交易中心',
      'common.users': '用户管理',
      'common.files': '文件管理',
      'common.reports': '报表统计',
      'common.notifications': '通知管理',
      'common.security': '安全设置',
      'common.system': '系统设置',
      'common.free': '免费',
      'common.purchase': '购买',
      'common.sort.label': '排序',
      'common.sort.newest': '最新',
      'common.sort.oldest': '最旧',
      'common.sort.popular': '最受欢迎',
      'common.sort.trending': '趋势',
      'common.sort.price_asc': '价格从低到高',
      'common.sort.price_desc': '价格从高到低',
      'common.sort.rating': '评分',
    },
    auth: {
      'login.title': '登录',
      'login.username': '用户名',
      'login.password': '密码',
      'login.remember': '记住我',
      'login.forgot': '忘记密码？',
      'login.submit': '登录',
      'login.register': '注册新账号',
    },
    dashboard: {
      'title': '仪表板',
      'stats.users': '总用户数',
      'stats.revenue': '总收入',
      'stats.assets': '资产数量',
      'stats.orders': '订单数量',
      'quickActions.title': '快速操作',
      'quickActions.upload': '上传资产',
      'quickActions.addUser': '添加用户',
      'quickActions.reports': '查看报表',
      'recentActivity.title': '最近活动',
      'popularAssets.title': '热门资产',
    },
    home: {
      'search.placeholder': '搜索AI模型、LoRA、工作流...',
      'header.publish': '发布',
      'header.login': '登录',
      'header.profile': '个人资料',
      'menu.home': '首页',
      'menu.models': '微调模型',
      'menu.lora': 'LoRA',
      'menu.workflows': '工作流',
      'menu.prompts': '提示词',
      'menu.tools': '工具',
      'menu.challenges': '挑战',
      'menu.bounties': '悬赏',
      'filters.categories': '分类',
      'filters.styles': '风格',
      'hero.carousel.item1.title': 'AI模型创作大赛',
      'hero.carousel.item1.desc': '参与创作，赢取丰厚奖品',
      'hero.carousel.item1.action': '立即参与',
      'hero.carousel.item2.title': '精选LoRA模型',
      'hero.carousel.item2.desc': '发现最新最热门的LoRA模型',
      'hero.carousel.item2.action': '查看更多',
      'hero.carousel.item3.title': '平台功能更新',
      'hero.carousel.item3.desc': '新增批量上传和高级搜索功能',
      'hero.carousel.item3.action': '了解详情',
      'hero.leaderboard.downloads': '下载榜',
      'hero.leaderboard.assets': '作品榜',
      'hero.leaderboard.likes': '点赞榜',
      'hero.leaderboard.works': '作品',
      'featured.title': '精选作品',
      'featured.subtitle': '管理员精心挑选的优质AI资源',
      'featured.empty': '暂无精选作品',
      'assets.title': '最新资源',
      'assets.search_results': '搜索结果: {{query}}',
      'assets.count': '共 {{count}} 个资源',
      'assets.empty': '暂无资源',
    },
    footer: {
      'description': '专业的AI生成内容服务交易平台，连接AI创作者和使用者，提供安全可靠的数字资产交易服务。',
      'platform.title': '平台',
      'platform.about': '关于我们',
      'platform.features': '功能特色',
      'platform.pricing': '价格方案',
      'platform.blog': '博客',
      'platform.news': '新闻',
      'creators.title': '创作者',
      'creators.guide': '创作者指南',
      'creators.upload': '上传资源',
      'creators.earnings': '收益管理',
      'creators.community': '创作者社区',
      'creators.resources': '创作资源',
      'support.title': '支持',
      'support.help': '帮助中心',
      'support.faq': '常见问题',
      'support.contact': '联系我们',
      'support.feedback': '意见反馈',
      'support.status': '服务状态',
      'legal.title': '法律',
      'legal.terms': '服务条款',
      'legal.privacy': '隐私政策',
      'legal.cookies': 'Cookie政策',
      'legal.dmca': 'DMCA',
      'legal.licenses': '许可证',
      'contact.email': '邮箱',
      'contact.phone': '电话',
      'contact.address': '地址',
      'contact.address_value': '北京市朝阳区AI科技园区',
      'follow_us': '关注我们',
      'copyright': '版权所有',
      'version': '版本',
      'sitemap': '网站地图',
      'accessibility': '无障碍访问',
    },
  },
  en: {
    translation: {
      // Common
      'common.loading': 'Loading...',
      'common.error': 'Error',
      'common.success': 'Success',
      'common.cancel': 'Cancel',
      'common.confirm': 'Confirm',
      'common.save': 'Save',
      'common.edit': 'Edit',
      'common.delete': 'Delete',
      'common.add': 'Add',
      'common.search': 'Search',
      'common.filter': 'Filter',
      'common.export': 'Export',
      'common.import': 'Import',
      'common.upload': 'Upload',
      'common.download': 'Download',
      'common.view': 'View',
      'common.details': 'Details',
      'common.back': 'Back',
      'common.next': 'Next',
      'common.previous': 'Previous',
      'common.close': 'Close',
      'common.open': 'Open',
      'common.expand': 'Expand',
      'common.collapse': 'Collapse',
      'common.select': 'Select',
      'common.selectAll': 'Select All',
      'common.clear': 'Clear',
      'common.reset': 'Reset',
      'common.refresh': 'Refresh',
      'common.settings': 'Settings',
      'common.profile': 'Profile',
      'common.logout': 'Logout',
      'common.login': 'Login',
      'common.register': 'Register',
      'common.forgotPassword': 'Forgot Password',
      'common.changePassword': 'Change Password',
      'common.dashboard': 'Dashboard',
      'common.assets': 'Assets',
      'common.trading': 'Trading',
      'common.users': 'Users',
      'common.files': 'Files',
      'common.reports': 'Reports',
      'common.notifications': 'Notifications',
      'common.security': 'Security',
      'common.system': 'System',
      'common.free': 'Free',
      'common.purchase': 'Purchase',
      'common.sort.label': 'Sort',
      'common.sort.newest': 'Newest',
      'common.sort.oldest': 'Oldest',
      'common.sort.popular': 'Popular',
      'common.sort.trending': 'Trending',
      'common.sort.price_asc': 'Price: Low to High',
      'common.sort.price_desc': 'Price: High to Low',
      'common.sort.rating': 'Rating',
    },
    auth: {
      'login.title': 'Login',
      'login.username': 'Username',
      'login.password': 'Password',
      'login.remember': 'Remember me',
      'login.forgot': 'Forgot password?',
      'login.submit': 'Login',
      'login.register': 'Register new account',
    },
    dashboard: {
      'title': 'Dashboard',
      'stats.users': 'Total Users',
      'stats.revenue': 'Total Revenue',
      'stats.assets': 'Assets Count',
      'stats.orders': 'Orders Count',
      'quickActions.title': 'Quick Actions',
      'quickActions.upload': 'Upload Asset',
      'quickActions.addUser': 'Add User',
      'quickActions.reports': 'View Reports',
      'recentActivity.title': 'Recent Activity',
      'popularAssets.title': 'Popular Assets',
    },
    home: {
      'search.placeholder': 'Search AI models, LoRA, workflows...',
      'header.publish': 'Publish',
      'header.login': 'Login',
      'header.profile': 'Profile',
      'menu.home': 'Home',
      'menu.models': 'Fine-tuned Models',
      'menu.lora': 'LoRA',
      'menu.workflows': 'Workflows',
      'menu.prompts': 'Prompts',
      'menu.tools': 'Tools',
      'menu.challenges': 'Challenges',
      'menu.bounties': 'Bounties',
      'filters.categories': 'Categories',
      'filters.styles': 'Styles',
      'hero.carousel.item1.title': 'AI Model Creation Contest',
      'hero.carousel.item1.desc': 'Participate and win amazing prizes',
      'hero.carousel.item1.action': 'Join Now',
      'hero.carousel.item2.title': 'Featured LoRA Models',
      'hero.carousel.item2.desc': 'Discover the latest and hottest LoRA models',
      'hero.carousel.item2.action': 'View More',
      'hero.carousel.item3.title': 'Platform Updates',
      'hero.carousel.item3.desc': 'New batch upload and advanced search features',
      'hero.carousel.item3.action': 'Learn More',
      'hero.leaderboard.downloads': 'Downloads',
      'hero.leaderboard.assets': 'Assets',
      'hero.leaderboard.likes': 'Likes',
      'hero.leaderboard.works': 'works',
      'featured.title': 'Featured Assets',
      'featured.subtitle': 'Carefully selected quality AI resources by administrators',
      'featured.empty': 'No featured assets',
      'assets.title': 'Latest Resources',
      'assets.search_results': 'Search results: {{query}}',
      'assets.count': '{{count}} resources',
      'assets.empty': 'No resources',
    },
    footer: {
      'description': 'Professional AI-generated content service trading platform, connecting AI creators and users, providing secure and reliable digital asset trading services.',
      'platform.title': 'Platform',
      'platform.about': 'About Us',
      'platform.features': 'Features',
      'platform.pricing': 'Pricing',
      'platform.blog': 'Blog',
      'platform.news': 'News',
      'creators.title': 'Creators',
      'creators.guide': 'Creator Guide',
      'creators.upload': 'Upload Assets',
      'creators.earnings': 'Earnings',
      'creators.community': 'Creator Community',
      'creators.resources': 'Resources',
      'support.title': 'Support',
      'support.help': 'Help Center',
      'support.faq': 'FAQ',
      'support.contact': 'Contact Us',
      'support.feedback': 'Feedback',
      'support.status': 'Service Status',
      'legal.title': 'Legal',
      'legal.terms': 'Terms of Service',
      'legal.privacy': 'Privacy Policy',
      'legal.cookies': 'Cookie Policy',
      'legal.dmca': 'DMCA',
      'legal.licenses': 'Licenses',
      'contact.email': 'Email',
      'contact.phone': 'Phone',
      'contact.address': 'Address',
      'contact.address_value': 'AI Technology Park, Chaoyang District, Beijing',
      'follow_us': 'Follow Us',
      'copyright': 'All rights reserved',
      'version': 'Version',
      'sitemap': 'Sitemap',
      'accessibility': 'Accessibility',
    },
  },
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'zh',
    fallbackLng: 'zh',
    debug: false,
    interpolation: {
      escapeValue: false,
    },
  });

export default i18n;