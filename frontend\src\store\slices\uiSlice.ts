import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 侧边栏状态
interface SidebarState {
  isOpen: boolean;
  isPinned: boolean;
  activeSection: string | null;
}

// 模态框状态
interface ModalState {
  isOpen: boolean;
  type: 'login' | 'register' | 'upload' | 'preview' | 'payment' | 'profile' | 'confirm' | null;
  data?: any;
  props?: Record<string, any>;
}

// 加载状态
interface LoadingState {
  global: boolean;
  page: boolean;
  component: Record<string, boolean>;
}

// 面包屑项目
interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

// 通知状态
interface NotificationState {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration: number;
  isVisible: boolean;
  id: string;
}

// 搜索状态
interface SearchState {
  query: string;
  filters: Record<string, any>;
  isActive: boolean;
  suggestions: string[];
  recentSearches: string[];
}

// 布局状态
interface LayoutState {
  headerHeight: number;
  footerHeight: number;
  sidebarWidth: number;
  containerMaxWidth: number;
  isFullscreen: boolean;
}

// 主题状态
interface ThemeState {
  mode: 'light' | 'dark' | 'system';
  primaryColor: string;
  fontSize: 'small' | 'medium' | 'large';
  fontFamily: string;
  borderRadius: number;
  animations: boolean;
}

// UI状态接口
interface UIState {
  sidebar: SidebarState;
  modal: ModalState;
  loading: LoadingState;
  breadcrumbs: BreadcrumbItem[];
  notifications: NotificationState[];
  search: SearchState;
  layout: LayoutState;
  theme: ThemeState;
  activeTab: string;
  scrollPosition: number;
  isOnline: boolean;
  deviceType: 'mobile' | 'tablet' | 'desktop';
  orientation: 'portrait' | 'landscape';
  pageTransition: boolean;
  dragAndDrop: {
    isDragging: boolean;
    dragType: string | null;
    dragData: any;
  };
  contextMenu: {
    isOpen: boolean;
    x: number;
    y: number;
    items: Array<{
      label: string;
      action: string;
      icon?: string;
      disabled?: boolean;
      separator?: boolean;
    }>;
  };
}

// 初始状态
const initialState: UIState = {
  sidebar: {
    isOpen: false,
    isPinned: false,
    activeSection: null,
  },
  modal: {
    isOpen: false,
    type: null,
    data: undefined,
    props: {},
  },
  loading: {
    global: false,
    page: false,
    component: {},
  },
  breadcrumbs: [],
  notifications: [],
  search: {
    query: '',
    filters: {},
    isActive: false,
    suggestions: [],
    recentSearches: JSON.parse(localStorage.getItem('recentSearches') || '[]'),
  },
  layout: {
    headerHeight: 64,
    footerHeight: 60,
    sidebarWidth: 280,
    containerMaxWidth: 1200,
    isFullscreen: false,
  },
  theme: {
    mode: (localStorage.getItem('theme') as 'light' | 'dark' | 'system') || 'system',
    primaryColor: '#1976d2',
    fontSize: 'medium',
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
    borderRadius: 8,
    animations: true,
  },
  activeTab: '',
  scrollPosition: 0,
  isOnline: navigator.onLine,
  deviceType: 'desktop',
  orientation: 'landscape',
  pageTransition: false,
  dragAndDrop: {
    isDragging: false,
    dragType: null,
    dragData: null,
  },
  contextMenu: {
    isOpen: false,
    x: 0,
    y: 0,
    items: [],
  },
};

// 创建slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // 侧边栏操作
    toggleSidebar: (state) => {
      state.sidebar.isOpen = !state.sidebar.isOpen;
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebar.isOpen = action.payload;
    },
    setSidebarPinned: (state, action: PayloadAction<boolean>) => {
      state.sidebar.isPinned = action.payload;
    },
    setSidebarActiveSection: (state, action: PayloadAction<string | null>) => {
      state.sidebar.activeSection = action.payload;
    },

    // 模态框操作
    openModal: (state, action: PayloadAction<{
      type: ModalState['type'];
      data?: any;
      props?: Record<string, any>;
    }>) => {
      state.modal.isOpen = true;
      state.modal.type = action.payload.type;
      state.modal.data = action.payload.data;
      state.modal.props = action.payload.props || {};
    },
    closeModal: (state) => {
      state.modal.isOpen = false;
      state.modal.type = null;
      state.modal.data = undefined;
      state.modal.props = {};
    },
    setModalData: (state, action: PayloadAction<any>) => {
      state.modal.data = action.payload;
    },

    // 加载状态
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload;
    },
    setPageLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.page = action.payload;
    },
    setComponentLoading: (state, action: PayloadAction<{
      component: string;
      loading: boolean;
    }>) => {
      state.loading.component[action.payload.component] = action.payload.loading;
    },

    // 面包屑操作
    setBreadcrumbs: (state, action: PayloadAction<BreadcrumbItem[]>) => {
      state.breadcrumbs = action.payload;
    },
    addBreadcrumb: (state, action: PayloadAction<BreadcrumbItem>) => {
      state.breadcrumbs.push(action.payload);
    },
    removeBreadcrumb: (state, action: PayloadAction<number>) => {
      state.breadcrumbs.splice(action.payload, 1);
    },

    // 通知操作
    addNotification: (state, action: PayloadAction<Omit<NotificationState, 'id' | 'isVisible'>>) => {
      const notification: NotificationState = {
        ...action.payload,
        id: Math.random().toString(36).substr(2, 9),
        isVisible: true,
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },

    // 搜索操作
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.search.query = action.payload;
    },
    setSearchFilters: (state, action: PayloadAction<Record<string, any>>) => {
      state.search.filters = action.payload;
    },
    setSearchActive: (state, action: PayloadAction<boolean>) => {
      state.search.isActive = action.payload;
    },
    setSearchSuggestions: (state, action: PayloadAction<string[]>) => {
      state.search.suggestions = action.payload;
    },
    addRecentSearch: (state, action: PayloadAction<string>) => {
      const query = action.payload.trim();
      if (query && !state.search.recentSearches.includes(query)) {
        state.search.recentSearches.unshift(query);
        // 限制最多保存10个搜索历史
        if (state.search.recentSearches.length > 10) {
          state.search.recentSearches.pop();
        }
        localStorage.setItem('recentSearches', JSON.stringify(state.search.recentSearches));
      }
    },
    clearRecentSearches: (state) => {
      state.search.recentSearches = [];
      localStorage.removeItem('recentSearches');
    },

    // 布局操作
    setLayoutDimensions: (state, action: PayloadAction<Partial<LayoutState>>) => {
      state.layout = { ...state.layout, ...action.payload };
    },
    toggleFullscreen: (state) => {
      state.layout.isFullscreen = !state.layout.isFullscreen;
    },

    // 主题操作
    setTheme: (state, action: PayloadAction<Partial<ThemeState>>) => {
      state.theme = { ...state.theme, ...action.payload };
      localStorage.setItem('theme', state.theme.mode);
    },

    // 标签页操作
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTab = action.payload;
    },

    // 滚动位置
    setScrollPosition: (state, action: PayloadAction<number>) => {
      state.scrollPosition = action.payload;
    },

    // 网络状态
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },

    // 设备信息
    setDeviceInfo: (state, action: PayloadAction<{
      deviceType: UIState['deviceType'];
      orientation: UIState['orientation'];
    }>) => {
      state.deviceType = action.payload.deviceType;
      state.orientation = action.payload.orientation;
    },

    // 页面过渡
    setPageTransition: (state, action: PayloadAction<boolean>) => {
      state.pageTransition = action.payload;
    },

    // 拖拽操作
    setDragAndDrop: (state, action: PayloadAction<Partial<UIState['dragAndDrop']>>) => {
      state.dragAndDrop = { ...state.dragAndDrop, ...action.payload };
    },

    // 右键菜单
    openContextMenu: (state, action: PayloadAction<{
      x: number;
      y: number;
      items: UIState['contextMenu']['items'];
    }>) => {
      state.contextMenu.isOpen = true;
      state.contextMenu.x = action.payload.x;
      state.contextMenu.y = action.payload.y;
      state.contextMenu.items = action.payload.items;
    },
    closeContextMenu: (state) => {
      state.contextMenu.isOpen = false;
      state.contextMenu.items = [];
    },

    // 重置UI状态
    resetUI: (state) => {
      Object.assign(state, initialState);
    },
  },
});

// 导出actions
export const {
  toggleSidebar,
  setSidebarOpen,
  setSidebarPinned,
  setSidebarActiveSection,
  openModal,
  closeModal,
  setModalData,
  setGlobalLoading,
  setPageLoading,
  setComponentLoading,
  setBreadcrumbs,
  addBreadcrumb,
  removeBreadcrumb,
  addNotification,
  removeNotification,
  clearNotifications,
  setSearchQuery,
  setSearchFilters,
  setSearchActive,
  setSearchSuggestions,
  addRecentSearch,
  clearRecentSearches,
  setLayoutDimensions,
  toggleFullscreen,
  setTheme,
  setActiveTab,
  setScrollPosition,
  setOnlineStatus,
  setDeviceInfo,
  setPageTransition,
  setDragAndDrop,
  openContextMenu,
  closeContextMenu,
  resetUI,
} = uiSlice.actions;

// 选择器
export const selectUI = (state: { ui: UIState }) => state.ui;
export const selectSidebar = (state: { ui: UIState }) => state.ui.sidebar;
export const selectModal = (state: { ui: UIState }) => state.ui.modal;
export const selectLoading = (state: { ui: UIState }) => state.ui.loading;
export const selectBreadcrumbs = (state: { ui: UIState }) => state.ui.breadcrumbs;
export const selectNotifications = (state: { ui: UIState }) => state.ui.notifications;
export const selectSearch = (state: { ui: UIState }) => state.ui.search;
export const selectLayout = (state: { ui: UIState }) => state.ui.layout;
export const selectTheme = (state: { ui: UIState }) => state.ui.theme;
export const selectIsOnline = (state: { ui: UIState }) => state.ui.isOnline;
export const selectDeviceType = (state: { ui: UIState }) => state.ui.deviceType;
export const selectContextMenu = (state: { ui: UIState }) => state.ui.contextMenu;

// 导出reducer
export default uiSlice.reducer;