import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { 
  User, 
  Asset, 
  Transaction, 
  UserProfile, 
  UserSettings, 
  LoginForm, 
  RegisterForm, 
  PaginationParams,
  AssetSearchParams,
  TransactionStatus,
  AssetStatus
} from '../types';
import { STORAGE_KEYS } from '../constants';

// API基础配置
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api';

// 创建axios实例
const createAxiosInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // 请求拦截器 - 添加认证token
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // 响应拦截器 - 处理token刷新
  instance.interceptors.response.use(
    (response) => {
      return response;
    },
    async (error) => {
      const originalRequest = error.config;

      if (error.response?.status === 401 && !originalRequest._retry) {
        originalRequest._retry = true;

        try {
          const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
          if (refreshToken) {
            const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
              refreshToken,
            });

            const { accessToken, refreshToken: newRefreshToken } = response.data;
            localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, accessToken);
            localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, newRefreshToken);

            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            return instance(originalRequest);
          }
        } catch (refreshError) {
          // 刷新失败，清除tokens并重定向到登录页
          localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
          localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
          localStorage.removeItem(STORAGE_KEYS.USER_INFO);
          window.location.href = '/login';
        }
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// 创建API实例
const api = createAxiosInstance();

// 通用响应接口
interface ApiResponse<T = any> {
  data: T;
  message: string;
  success: boolean;
  timestamp: string;
}

// 分页响应接口
interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

// 认证API
export const authAPI = {
  // 登录
  login: (credentials: LoginForm): Promise<AxiosResponse<ApiResponse<{
    user: User;
    accessToken: string;
    refreshToken: string;
  }>>> => {
    return api.post('/auth/login', credentials);
  },

  // 注册
  register: (userData: RegisterForm): Promise<AxiosResponse<ApiResponse<{
    user: User;
    accessToken: string;
    refreshToken: string;
  }>>> => {
    return api.post('/auth/register', userData);
  },

  // 刷新token
  refreshToken: (params: { refreshToken: string }): Promise<AxiosResponse<ApiResponse<{
    accessToken: string;
    refreshToken: string;
  }>>> => {
    return api.post('/auth/refresh', params);
  },

  // 退出登录
  logout: (): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post('/auth/logout');
  },

  // 获取当前用户信息
  getCurrentUser: (): Promise<AxiosResponse<ApiResponse<User>>> => {
    return api.get('/auth/me');
  },

  // OAuth登录
  oauthLogin: (provider: string, code: string): Promise<AxiosResponse<ApiResponse<{
    user: User;
    accessToken: string;
    refreshToken: string;
  }>>> => {
    return api.post(`/auth/oauth/${provider}`, { code });
  },

  // 忘记密码
  forgotPassword: (email: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post('/auth/forgot-password', { email });
  },

  // 重置密码
  resetPassword: (params: {
    token: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post('/auth/reset-password', params);
  },

  // 验证邮箱
  verifyEmail: (token: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post('/auth/verify-email', { token });
  },

  // 重新发送验证邮件
  resendVerification: (email: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post('/auth/resend-verification', { email });
  },
};

// 资产API
export const assetsAPI = {
  // 获取资产列表
  getAssets: (params: AssetSearchParams & PaginationParams): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Asset>>>> => {
    return api.get('/assets', { params });
  },

  // 获取资产详情
  getAssetById: (id: string): Promise<AxiosResponse<ApiResponse<Asset>>> => {
    return api.get(`/assets/${id}`);
  },

  // 获取我的资产
  getMyAssets: (params: PaginationParams & { status?: AssetStatus }): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Asset>>>> => {
    return api.get('/assets/my', { params });
  },

  // 获取已购买的资产
  getPurchasedAssets: (params: PaginationParams): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Asset>>>> => {
    return api.get('/assets/purchased', { params });
  },

  // 创建资产
  createAsset: (assetData: FormData): Promise<AxiosResponse<ApiResponse<Asset>>> => {
    return api.post('/assets', assetData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 更新资产
  updateAsset: (id: string, data: Partial<Asset>): Promise<AxiosResponse<ApiResponse<Asset>>> => {
    return api.put(`/assets/${id}`, data);
  },

  // 删除资产
  deleteAsset: (id: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.delete(`/assets/${id}`);
  },

  // 购买资产
  purchaseAsset: (assetId: string): Promise<AxiosResponse<ApiResponse<Asset>>> => {
    return api.post(`/assets/${assetId}/purchase`);
  },

  // 切换收藏状态
  toggleLike: (assetId: string): Promise<AxiosResponse<ApiResponse<{ isLiked: boolean }>>> => {
    return api.post(`/assets/${assetId}/like`);
  },

  // 添加浏览记录
  addView: (assetId: string): Promise<AxiosResponse<ApiResponse<{ views: number }>>> => {
    return api.post(`/assets/${assetId}/view`);
  },

  // 下载资产
  downloadAsset: (assetId: string): Promise<AxiosResponse<ApiResponse<{ downloadUrl: string }>>> => {
    return api.post(`/assets/${assetId}/download`);
  },

  // 获取资产分类
  getCategories: (): Promise<AxiosResponse<ApiResponse<any[]>>> => {
    return api.get('/assets/categories');
  },

  // 获取精选资产
  getFeaturedAssets: (params: { limit?: number; category?: string }): Promise<AxiosResponse<ApiResponse<Asset[]>>> => {
    return api.get('/assets/featured', { params });
  },

  // 获取热门资产
  getTrendingAssets: (params: { limit?: number; timeRange?: string }): Promise<AxiosResponse<ApiResponse<Asset[]>>> => {
    return api.get('/assets/trending', { params });
  },

  // 获取推荐资产
  getRecommendedAssets: (params: { limit?: number; userId?: string }): Promise<AxiosResponse<ApiResponse<Asset[]>>> => {
    return api.get('/assets/recommended', { params });
  },

  // 搜索资产
  searchAssets: (params: AssetSearchParams & PaginationParams): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Asset>>>> => {
    return api.get('/assets/search', { params });
  },

  // 获取资产评论
  getAssetComments: (assetId: string, params: PaginationParams): Promise<AxiosResponse<ApiResponse<PaginatedResponse<any>>>> => {
    return api.get(`/assets/${assetId}/comments`, { params });
  },

  // 添加评论
  addComment: (assetId: string, comment: string): Promise<AxiosResponse<ApiResponse<any>>> => {
    return api.post(`/assets/${assetId}/comments`, { comment });
  },

  // 举报资产
  reportAsset: (assetId: string, reason: string, description?: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post(`/assets/${assetId}/report`, { reason, description });
  },
};

// 交易API
export const transactionsAPI = {
  // 获取交易列表
  getTransactions: (params: PaginationParams & {
    status?: TransactionStatus;
    type?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Transaction>>>> => {
    return api.get('/transactions', { params });
  },

  // 获取交易详情
  getTransactionById: (id: string): Promise<AxiosResponse<ApiResponse<Transaction>>> => {
    return api.get(`/transactions/${id}`);
  },

  // 创建支付意图
  createPaymentIntent: (params: {
    assetIds: string[];
    paymentMethod: 'paypal' | 'stripe';
    currency: string;
  }): Promise<AxiosResponse<ApiResponse<any>>> => {
    return api.post('/transactions/payment-intent', params);
  },

  // 确认支付
  confirmPayment: (params: {
    paymentIntentId: string;
    paymentMethodId: string;
    metadata?: Record<string, any>;
  }): Promise<AxiosResponse<ApiResponse<Transaction>>> => {
    return api.post('/transactions/confirm-payment', params);
  },

  // 取消支付
  cancelPayment: (paymentIntentId: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post(`/transactions/cancel-payment/${paymentIntentId}`);
  },

  // 退款
  refundTransaction: (params: {
    transactionId: string;
    amount?: number;
    reason?: string;
  }): Promise<AxiosResponse<ApiResponse<Transaction>>> => {
    return api.post('/transactions/refund', params);
  },

  // 创建提现申请
  createWithdrawalRequest: (params: {
    amount: number;
    method: 'paypal' | 'bank_transfer';
    details: Record<string, any>;
  }): Promise<AxiosResponse<ApiResponse<any>>> => {
    return api.post('/transactions/withdrawal', params);
  },

  // 获取提现申请列表
  getWithdrawalRequests: (params: PaginationParams & { status?: string }): Promise<AxiosResponse<ApiResponse<PaginatedResponse<any>>>> => {
    return api.get('/transactions/withdrawals', { params });
  },

  // 获取收益统计
  getEarningsStats: (params: {
    startDate?: string;
    endDate?: string;
    groupBy?: 'day' | 'week' | 'month';
  }): Promise<AxiosResponse<ApiResponse<any>>> => {
    return api.get('/transactions/earnings', { params });
  },

  // 获取支付方式
  getPaymentMethods: (): Promise<AxiosResponse<ApiResponse<any[]>>> => {
    return api.get('/transactions/payment-methods');
  },

  // 获取发票
  getInvoice: (transactionId: string): Promise<AxiosResponse<ApiResponse<{ invoiceUrl: string }>>> => {
    return api.get(`/transactions/${transactionId}/invoice`);
  },
};

// 用户API
export const userAPI = {
  // 获取用户资料
  getUserProfile: (userId?: string): Promise<AxiosResponse<ApiResponse<UserProfile>>> => {
    const url = userId ? `/users/${userId}/profile` : '/users/profile';
    return api.get(url);
  },

  // 更新用户资料
  updateUserProfile: (profileData: Partial<UserProfile>): Promise<AxiosResponse<ApiResponse<UserProfile>>> => {
    return api.put('/users/profile', profileData);
  },

  // 更新用户设置
  updateUserSettings: (settings: Partial<UserSettings>): Promise<AxiosResponse<ApiResponse<UserSettings>>> => {
    return api.put('/users/settings', settings);
  },

  // 上传头像
  uploadAvatar: (formData: FormData): Promise<AxiosResponse<ApiResponse<{ avatarUrl: string }>>> => {
    return api.post('/users/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 修改密码
  changePassword: (params: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post('/users/change-password', params);
  },

  // 删除账户
  deleteAccount: (params: { password: string; reason?: string }): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post('/users/delete-account', params);
  },

  // 获取用户统计
  getUserStats: (userId?: string): Promise<AxiosResponse<ApiResponse<any>>> => {
    const url = userId ? `/users/${userId}/stats` : '/users/stats';
    return api.get(url);
  },

  // 获取关注者列表
  getFollowers: (params: PaginationParams & { userId?: string }): Promise<AxiosResponse<ApiResponse<PaginatedResponse<any>>>> => {
    return api.get('/users/followers', { params });
  },

  // 获取关注列表
  getFollowing: (params: PaginationParams & { userId?: string }): Promise<AxiosResponse<ApiResponse<PaginatedResponse<any>>>> => {
    return api.get('/users/following', { params });
  },

  // 关注用户
  followUser: (userId: string): Promise<AxiosResponse<ApiResponse<{ isFollowing: boolean }>>> => {
    return api.post(`/users/${userId}/follow`);
  },

  // 取消关注用户
  unfollowUser: (userId: string): Promise<AxiosResponse<ApiResponse<{ isFollowing: boolean }>>> => {
    return api.delete(`/users/${userId}/follow`);
  },

  // 举报用户
  reportUser: (params: {
    userId: string;
    reason: string;
    description?: string;
  }): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post('/users/report', params);
  },

  // 屏蔽用户
  blockUser: (userId: string): Promise<AxiosResponse<ApiResponse<{ isBlocked: boolean }>>> => {
    return api.post(`/users/${userId}/block`);
  },

  // 取消屏蔽用户
  unblockUser: (userId: string): Promise<AxiosResponse<ApiResponse<{ isBlocked: boolean }>>> => {
    return api.delete(`/users/${userId}/block`);
  },

  // 启用两步验证
  enableTwoFactor: (): Promise<AxiosResponse<ApiResponse<{ secret: string; qrCode: string }>>> => {
    return api.post('/users/2fa/enable');
  },

  // 禁用两步验证
  disableTwoFactor: (params: { password: string; code: string }): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post('/users/2fa/disable', params);
  },

  // 验证两步验证码
  verifyTwoFactor: (code: string): Promise<AxiosResponse<ApiResponse<{ verified: boolean }>>> => {
    return api.post('/users/2fa/verify', { code });
  },

  // 获取用户通知设置
  getNotificationSettings: (): Promise<AxiosResponse<ApiResponse<any>>> => {
    return api.get('/users/notification-settings');
  },

  // 更新用户通知设置
  updateNotificationSettings: (settings: any): Promise<AxiosResponse<ApiResponse<any>>> => {
    return api.put('/users/notification-settings', settings);
  },
};

// 上传API
export const uploadAPI = {
  // 创建上传会话
  createUploadSession: (params: {
    fileName: string;
    fileSize: number;
    mimeType: string;
    [key: string]: any;
  }): Promise<AxiosResponse<ApiResponse<{
    fileId: string;
    uploadId: string;
    bucket: string;
    key: string;
    expiresAt: string;
  }>>> => {
    return api.post('/uploads/create-session', params);
  },

  // 获取上传URL
  getUploadUrl: (params: {
    uploadId: string;
    partNumber: number;
  }): Promise<AxiosResponse<ApiResponse<{ uploadUrl: string }>>> => {
    return api.post('/uploads/get-url', params);
  },

  // 完成上传
  completeUpload: (params: {
    uploadId: string;
    parts: Array<{
      partNumber: number;
      etag: string;
    }>;
  }): Promise<AxiosResponse<ApiResponse<{ fileUrl: string }>>> => {
    return api.post('/uploads/complete', params);
  },

  // 取消上传
  cancelUpload: (fileId: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post(`/uploads/${fileId}/cancel`);
  },

  // 暂停上传
  pauseUpload: (fileId: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post(`/uploads/${fileId}/pause`);
  },

  // 恢复上传
  resumeUpload: (fileId: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post(`/uploads/${fileId}/resume`);
  },

  // 重试上传
  retryUpload: (fileId: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post(`/uploads/${fileId}/retry`);
  },

  // 分块上传文件
  uploadFileChunks: async (params: {
    file: File;
    session: any;
    onProgress?: (progress: number) => void;
    onChunkComplete?: (chunkIndex: number, etag: string) => void;
    onChunkError?: (chunkIndex: number, error: Error) => void;
  }): Promise<AxiosResponse<ApiResponse<any>>> => {
    const { file, session, onProgress, onChunkComplete, onChunkError } = params;
    const chunkSize = 5 * 1024 * 1024; // 5MB
    const totalChunks = Math.ceil(file.size / chunkSize);
    const uploadedParts: Array<{ partNumber: number; etag: string }> = [];

    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);
      const partNumber = i + 1;

      try {
        // 获取上传URL
        const urlResponse = await uploadAPI.getUploadUrl({
          uploadId: session.uploadId,
          partNumber,
        });

        const uploadUrl = urlResponse.data.data.uploadUrl;

        // 上传块
        const uploadResponse = await axios.put(uploadUrl, chunk, {
          headers: {
            'Content-Type': 'application/octet-stream',
          },
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const chunkProgress = (progressEvent.loaded / progressEvent.total) * 100;
              const totalProgress = ((i + chunkProgress / 100) / totalChunks) * 100;
              onProgress?.(totalProgress);
            }
          },
        });

        const etag = uploadResponse.headers.etag?.replace(/"/g, '');
        uploadedParts.push({ partNumber, etag });

        onChunkComplete?.(i, etag);
      } catch (error) {
        onChunkError?.(i, error as Error);
        throw error;
      }
    }

    // 完成上传
    return uploadAPI.completeUpload({
      uploadId: session.uploadId,
      parts: uploadedParts,
    });
  },
};

// 管理员API
export const adminAPI = {
  // 获取用户列表
  getUsers: (params: PaginationParams & {
    role?: string;
    status?: string;
    search?: string;
  }): Promise<AxiosResponse<ApiResponse<PaginatedResponse<User>>>> => {
    return api.get('/admin/users', { params });
  },

  // 获取用户详情
  getUserDetails: (userId: string): Promise<AxiosResponse<ApiResponse<User>>> => {
    return api.get(`/admin/users/${userId}`);
  },

  // 更新用户状态
  updateUserStatus: (userId: string, status: string): Promise<AxiosResponse<ApiResponse<User>>> => {
    return api.put(`/admin/users/${userId}/status`, { status });
  },

  // 获取资产审核列表
  getAssetReviews: (params: PaginationParams & {
    status?: string;
    category?: string;
  }): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Asset>>>> => {
    return api.get('/admin/assets/reviews', { params });
  },

  // 审核资产
  reviewAsset: (assetId: string, params: {
    status: 'approved' | 'rejected';
    reason?: string;
  }): Promise<AxiosResponse<ApiResponse<Asset>>> => {
    return api.post(`/admin/assets/${assetId}/review`, params);
  },

  // 获取系统统计
  getSystemStats: (): Promise<AxiosResponse<ApiResponse<any>>> => {
    return api.get('/admin/stats');
  },

  // 获取财务报告
  getFinancialReport: (params: {
    startDate?: string;
    endDate?: string;
    type?: string;
  }): Promise<AxiosResponse<ApiResponse<any>>> => {
    return api.get('/admin/financial-report', { params });
  },

  // 获取举报列表
  getReports: (params: PaginationParams & {
    type?: string;
    status?: string;
  }): Promise<AxiosResponse<ApiResponse<PaginatedResponse<any>>>> => {
    return api.get('/admin/reports', { params });
  },

  // 处理举报
  handleReport: (reportId: string, params: {
    action: 'dismiss' | 'suspend' | 'ban';
    reason?: string;
  }): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.post(`/admin/reports/${reportId}/handle`, params);
  },

  // 获取系统设置
  getSystemSettings: (): Promise<AxiosResponse<ApiResponse<any>>> => {
    return api.get('/admin/settings');
  },

  // 更新系统设置
  updateSystemSettings: (settings: any): Promise<AxiosResponse<ApiResponse<any>>> => {
    return api.put('/admin/settings', settings);
  },
};

// 文件处理API
export const fileAPI = {
  // 获取文件信息
  getFileInfo: (fileId: string): Promise<AxiosResponse<ApiResponse<any>>> => {
    return api.get(`/files/${fileId}`);
  },

  // 删除文件
  deleteFile: (fileId: string): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.delete(`/files/${fileId}`);
  },

  // 获取文件预览
  getFilePreview: (fileId: string): Promise<AxiosResponse<ApiResponse<{ previewUrl: string }>>> => {
    return api.get(`/files/${fileId}/preview`);
  },

  // 压缩文件
  compressFile: (fileId: string, params: {
    quality?: number;
    format?: string;
  }): Promise<AxiosResponse<ApiResponse<{ compressedFileId: string }>>> => {
    return api.post(`/files/${fileId}/compress`, params);
  },

  // 转换文件格式
  convertFile: (fileId: string, params: {
    targetFormat: string;
    options?: Record<string, any>;
  }): Promise<AxiosResponse<ApiResponse<{ convertedFileId: string }>>> => {
    return api.post(`/files/${fileId}/convert`, params);
  },
};

// 搜索API
export const searchAPI = {
  // 全局搜索
  globalSearch: (params: {
    query: string;
    type?: 'assets' | 'users' | 'all';
    filters?: Record<string, any>;
  } & PaginationParams): Promise<AxiosResponse<ApiResponse<PaginatedResponse<any>>>> => {
    return api.get('/search', { params });
  },

  // 获取搜索建议
  getSearchSuggestions: (query: string): Promise<AxiosResponse<ApiResponse<string[]>>> => {
    return api.get('/search/suggestions', { params: { query } });
  },

  // 获取热门搜索
  getHotSearches: (): Promise<AxiosResponse<ApiResponse<string[]>>> => {
    return api.get('/search/hot');
  },

  // 获取搜索历史
  getSearchHistory: (): Promise<AxiosResponse<ApiResponse<string[]>>> => {
    return api.get('/search/history');
  },

  // 清除搜索历史
  clearSearchHistory: (): Promise<AxiosResponse<ApiResponse<null>>> => {
    return api.delete('/search/history');
  },
};

// 导出API实例
export { api };
export default api;