import { Router } from 'express';
import { AssetController } from '@/controllers/asset.controller';
import authMiddleware from '@/middlewares/auth.middleware';

const router = Router();
const assetController = new AssetController();

// 公开路由
router.get('/', assetController.getAssetList);
router.get('/:id', assetController.getAssetById);
router.get('/:id/stats', assetController.getAssetStats);

// 需要认证的路由
router.use(authMiddleware.authenticate);

// 创作者路由
router.post('/', authMiddleware.requireCreator, assetController.createAsset);
router.put('/:id', authMiddleware.requireCreator, assetController.updateAsset);
router.post('/:id/publish', authMiddleware.requireCreator, assetController.publishAsset);
router.delete('/:id', authMiddleware.requireCreator, assetController.deleteAsset);

// 文件操作路由
router.post('/:id/file', authMiddleware.requireCreator, assetController.updateAssetFile);
router.get('/:id/download', assetController.downloadAsset);

// 创作者资产管理
router.get('/creator/:creatorId', assetController.getCreatorAssets);
router.get('/my/assets', assetController.getMyAssets);

export default router;