#!/bin/bash

# ===========================================
# AIGC Service Hub MVP 1.0 - 备份脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AIGC Service Hub MVP 1.0 - 备份脚本

使用方法: $0 [OPTIONS]

OPTIONS:
    -h, --help          显示帮助信息
    -v, --verbose       详细输出
    -f, --full          完整备份 (包括上传文件)
    -d, --database      仅备份数据库
    -u, --uploads       仅备份上传文件
    -c, --compress      压缩备份文件
    -e, --encrypt       加密备份文件
    -r, --remote        上传到远程存储
    -s, --schedule      定时备份模式
    --retention N       保留N天的备份 (默认: 30)
    --output DIR        指定输出目录
    --config FILE       指定配置文件

示例:
    $0                  # 标准备份
    $0 -f               # 完整备份
    $0 -d               # 仅备份数据库
    $0 -c -e            # 压缩并加密备份
    $0 -r -s            # 定时备份并上传到远程

EOF
}

# 加载配置
load_config() {
    # 默认配置
    BACKUP_DIR="./database/backups"
    UPLOADS_DIR="./backend/uploads"
    POSTGRES_CONTAINER="aigc-postgres"
    REDIS_CONTAINER="aigc-redis"
    RETENTION_DAYS=30
    COMPRESSION=false
    ENCRYPTION=false
    REMOTE_BACKUP=false
    SCHEDULE_MODE=false
    
    # 从环境变量加载配置
    if [ -f ".env" ]; then
        source .env
        BACKUP_DIR="${BACKUP_DIR:-./database/backups}"
        RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
    fi
    
    # 从配置文件加载
    if [ -n "$CONFIG_FILE" ] && [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE"
    fi
}

# 创建备份目录
create_backup_dir() {
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_NAME="aigc_backup_${TIMESTAMP}"
    BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"
    
    mkdir -p "$BACKUP_PATH"
    log_info "创建备份目录: $BACKUP_PATH"
}

# 备份PostgreSQL数据库
backup_postgres() {
    log_info "备份PostgreSQL数据库..."
    
    # 检查容器是否运行
    if ! docker ps | grep -q "$POSTGRES_CONTAINER"; then
        log_error "PostgreSQL容器 $POSTGRES_CONTAINER 未运行"
        return 1
    fi
    
    # 获取数据库连接信息
    DB_NAME=$(docker exec "$POSTGRES_CONTAINER" printenv POSTGRES_DB)
    DB_USER=$(docker exec "$POSTGRES_CONTAINER" printenv POSTGRES_USER)
    
    # 创建数据库备份
    docker exec "$POSTGRES_CONTAINER" pg_dump -U "$DB_USER" "$DB_NAME" > "$BACKUP_PATH/postgres_dump.sql"
    
    # 备份数据库Schema
    docker exec "$POSTGRES_CONTAINER" pg_dump -U "$DB_USER" -s "$DB_NAME" > "$BACKUP_PATH/postgres_schema.sql"
    
    # 备份数据库统计信息
    docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "\l" > "$BACKUP_PATH/postgres_databases.txt"
    docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "\dt" > "$BACKUP_PATH/postgres_tables.txt"
    docker exec "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" -c "SELECT COUNT(*) FROM users;" > "$BACKUP_PATH/postgres_stats.txt"
    
    log_success "PostgreSQL数据库备份完成"
}

# 备份Redis数据
backup_redis() {
    log_info "备份Redis数据..."
    
    # 检查容器是否运行
    if ! docker ps | grep -q "$REDIS_CONTAINER"; then
        log_error "Redis容器 $REDIS_CONTAINER 未运行"
        return 1
    fi
    
    # 创建Redis备份
    docker exec "$REDIS_CONTAINER" redis-cli SAVE
    docker cp "$REDIS_CONTAINER:/data/dump.rdb" "$BACKUP_PATH/redis_dump.rdb"
    
    # 备份Redis配置
    docker exec "$REDIS_CONTAINER" redis-cli CONFIG GET "*" > "$BACKUP_PATH/redis_config.txt"
    
    # 备份Redis统计信息
    docker exec "$REDIS_CONTAINER" redis-cli INFO > "$BACKUP_PATH/redis_info.txt"
    
    log_success "Redis数据备份完成"
}

# 备份上传文件
backup_uploads() {
    log_info "备份上传文件..."
    
    if [ -d "$UPLOADS_DIR" ]; then
        # 计算目录大小
        UPLOADS_SIZE=$(du -sh "$UPLOADS_DIR" | cut -f1)
        log_info "上传文件目录大小: $UPLOADS_SIZE"
        
        # 复制上传文件
        cp -r "$UPLOADS_DIR" "$BACKUP_PATH/uploads"
        
        # 创建文件列表
        find "$UPLOADS_DIR" -type f > "$BACKUP_PATH/uploads_files.txt"
        
        log_success "上传文件备份完成"
    else
        log_warning "上传文件目录不存在: $UPLOADS_DIR"
    fi
}

# 备份配置文件
backup_configs() {
    log_info "备份配置文件..."
    
    mkdir -p "$BACKUP_PATH/configs"
    
    # 备份环境配置
    if [ -f ".env" ]; then
        cp ".env" "$BACKUP_PATH/configs/env"
    fi
    
    # 备份Docker配置
    config_files=(
        "docker-compose.yml"
        "docker-compose.prod.yml"
        "docker-compose.dev.yml"
        "docker-compose.test.yml"
    )
    
    for config in "${config_files[@]}"; do
        if [ -f "$config" ]; then
            cp "$config" "$BACKUP_PATH/configs/"
        fi
    done
    
    # 备份Nginx配置
    if [ -d "nginx" ]; then
        cp -r "nginx" "$BACKUP_PATH/configs/"
    fi
    
    # 备份数据库配置
    if [ -d "database" ]; then
        cp -r "database" "$BACKUP_PATH/configs/"
        # 排除数据文件
        rm -rf "$BACKUP_PATH/configs/database/postgres-data"
        rm -rf "$BACKUP_PATH/configs/database/redis-data"
        rm -rf "$BACKUP_PATH/configs/database/backups"
    fi
    
    log_success "配置文件备份完成"
}

# 备份日志文件
backup_logs() {
    log_info "备份日志文件..."
    
    mkdir -p "$BACKUP_PATH/logs"
    
    # 备份应用日志
    if [ -d "backend/logs" ]; then
        cp -r "backend/logs" "$BACKUP_PATH/logs/backend"
    fi
    
    # 备份Nginx日志
    if [ -d "nginx/logs" ]; then
        cp -r "nginx/logs" "$BACKUP_PATH/logs/nginx"
    fi
    
    # 备份Docker日志
    docker logs "$POSTGRES_CONTAINER" > "$BACKUP_PATH/logs/postgres.log" 2>&1 || true
    docker logs "$REDIS_CONTAINER" > "$BACKUP_PATH/logs/redis.log" 2>&1 || true
    
    log_success "日志文件备份完成"
}

# 创建备份清单
create_manifest() {
    log_info "创建备份清单..."
    
    cat > "$BACKUP_PATH/manifest.txt" << EOF
AIGC Service Hub MVP 1.0 - 备份清单
=====================================

备份时间: $(date)
备份类型: $BACKUP_TYPE
主机名: $(hostname)
系统: $(uname -a)

数据库信息:
- PostgreSQL: $(docker exec "$POSTGRES_CONTAINER" postgres --version 2>/dev/null || echo "N/A")
- Redis: $(docker exec "$REDIS_CONTAINER" redis-server --version 2>/dev/null || echo "N/A")

备份内容:
EOF

    # 添加备份文件列表
    find "$BACKUP_PATH" -type f -exec ls -lh {} \; >> "$BACKUP_PATH/manifest.txt"
    
    # 计算备份大小
    BACKUP_SIZE=$(du -sh "$BACKUP_PATH" | cut -f1)
    echo "总备份大小: $BACKUP_SIZE" >> "$BACKUP_PATH/manifest.txt"
    
    log_success "备份清单创建完成"
}

# 压缩备份
compress_backup() {
    if [ "$COMPRESSION" = true ]; then
        log_info "压缩备份文件..."
        
        cd "$BACKUP_DIR"
        tar -czf "${BACKUP_NAME}.tar.gz" "$BACKUP_NAME"
        
        # 删除原始目录
        rm -rf "$BACKUP_NAME"
        
        # 更新路径
        BACKUP_PATH="$BACKUP_DIR/${BACKUP_NAME}.tar.gz"
        
        log_success "备份压缩完成: $BACKUP_PATH"
    fi
}

# 加密备份
encrypt_backup() {
    if [ "$ENCRYPTION" = true ]; then
        log_info "加密备份文件..."
        
        # 检查加密密钥
        if [ -z "$BACKUP_ENCRYPTION_KEY" ]; then
            log_error "未设置加密密钥 BACKUP_ENCRYPTION_KEY"
            return 1
        fi
        
        # 加密文件
        openssl enc -aes-256-cbc -salt -in "$BACKUP_PATH" -out "${BACKUP_PATH}.enc" -k "$BACKUP_ENCRYPTION_KEY"
        
        # 删除原始文件
        rm "$BACKUP_PATH"
        
        # 更新路径
        BACKUP_PATH="${BACKUP_PATH}.enc"
        
        log_success "备份加密完成: $BACKUP_PATH"
    fi
}

# 上传到远程存储
upload_remote() {
    if [ "$REMOTE_BACKUP" = true ]; then
        log_info "上传备份到远程存储..."
        
        # AWS S3上传
        if [ -n "$AWS_S3_BACKUP_BUCKET" ]; then
            aws s3 cp "$BACKUP_PATH" "s3://$AWS_S3_BACKUP_BUCKET/backups/"
            log_success "备份上传到S3完成"
        fi
        
        # 其他远程存储可以在这里添加
        log_success "远程备份上传完成"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份文件..."
    
    # 查找并删除超过保留期的备份
    find "$BACKUP_DIR" -name "aigc_backup_*" -mtime +$RETENTION_DAYS -delete
    
    log_success "旧备份清理完成 (保留 $RETENTION_DAYS 天)"
}

# 验证备份
verify_backup() {
    log_info "验证备份文件..."
    
    # 检查备份文件是否存在
    if [ ! -f "$BACKUP_PATH" ] && [ ! -d "$BACKUP_PATH" ]; then
        log_error "备份文件不存在: $BACKUP_PATH"
        return 1
    fi
    
    # 检查文件大小
    if [ -f "$BACKUP_PATH" ]; then
        SIZE=$(stat -c%s "$BACKUP_PATH")
        if [ $SIZE -eq 0 ]; then
            log_error "备份文件为空: $BACKUP_PATH"
            return 1
        fi
    fi
    
    log_success "备份验证完成"
}

# 发送通知
send_notification() {
    log_info "发送备份通知..."
    
    # 邮件通知
    if [ -n "$BACKUP_EMAIL" ]; then
        SUBJECT="AIGC Service Hub - 备份完成"
        BODY="备份已完成\n时间: $(date)\n路径: $BACKUP_PATH\n大小: $(du -sh "$BACKUP_PATH" | cut -f1)"
        
        echo -e "$BODY" | mail -s "$SUBJECT" "$BACKUP_EMAIL"
        log_success "邮件通知发送完成"
    fi
    
    # Slack通知
    if [ -n "$SLACK_WEBHOOK_URL" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"AIGC Service Hub 备份完成\\n时间: $(date)\\n路径: $BACKUP_PATH\"}" \
            "$SLACK_WEBHOOK_URL"
        log_success "Slack通知发送完成"
    fi
}

# 主函数
main() {
    # 默认参数
    BACKUP_TYPE="standard"
    VERBOSE=false
    FULL_BACKUP=false
    DATABASE_ONLY=false
    UPLOADS_ONLY=false
    COMPRESSION=false
    ENCRYPTION=false
    REMOTE_BACKUP=false
    SCHEDULE_MODE=false
    CONFIG_FILE=""
    OUTPUT_DIR=""
    RETENTION_DAYS=30
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--full)
                FULL_BACKUP=true
                BACKUP_TYPE="full"
                shift
                ;;
            -d|--database)
                DATABASE_ONLY=true
                BACKUP_TYPE="database"
                shift
                ;;
            -u|--uploads)
                UPLOADS_ONLY=true
                BACKUP_TYPE="uploads"
                shift
                ;;
            -c|--compress)
                COMPRESSION=true
                shift
                ;;
            -e|--encrypt)
                ENCRYPTION=true
                shift
                ;;
            -r|--remote)
                REMOTE_BACKUP=true
                shift
                ;;
            -s|--schedule)
                SCHEDULE_MODE=true
                shift
                ;;
            --retention)
                RETENTION_DAYS=$2
                shift 2
                ;;
            --output)
                OUTPUT_DIR=$2
                shift 2
                ;;
            --config)
                CONFIG_FILE=$2
                shift 2
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置详细输出
    if [ "$VERBOSE" = true ]; then
        set -x
    fi
    
    # 加载配置
    load_config
    
    # 设置输出目录
    if [ -n "$OUTPUT_DIR" ]; then
        BACKUP_DIR="$OUTPUT_DIR"
    fi
    
    log_info "开始备份 AIGC Service Hub MVP 1.0"
    log_info "备份类型: $BACKUP_TYPE"
    
    # 创建备份目录
    create_backup_dir
    
    # 执行备份
    case $BACKUP_TYPE in
        full)
            backup_postgres
            backup_redis
            backup_uploads
            backup_configs
            backup_logs
            ;;
        database)
            backup_postgres
            backup_redis
            ;;
        uploads)
            backup_uploads
            ;;
        *)
            backup_postgres
            backup_redis
            backup_configs
            ;;
    esac
    
    # 创建备份清单
    create_manifest
    
    # 压缩备份
    compress_backup
    
    # 加密备份
    encrypt_backup
    
    # 验证备份
    verify_backup
    
    # 上传到远程存储
    upload_remote
    
    # 清理旧备份
    cleanup_old_backups
    
    # 发送通知
    send_notification
    
    log_success "备份完成！"
    log_info "备份路径: $BACKUP_PATH"
    log_info "备份大小: $(du -sh "$BACKUP_PATH" | cut -f1)"
}

# 执行主函数
main "$@"