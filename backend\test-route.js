const http = require('http');

console.log('🧪 Testing API route...');

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/auth/check-email?email=<EMAIL>',
  method: 'GET',
  headers: {
    'Accept': 'application/json'
  }
};

const req = http.request(options, (res) => {
  console.log(`📡 Status Code: ${res.statusCode}`);
  console.log(`📋 Headers:`, res.headers);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log(`📄 Response Body: ${data}`);
    
    if (res.statusCode === 200) {
      console.log('✅ Route is working correctly!');
    } else if (res.statusCode === 404) {
      console.log('❌ Route not found - API routing issue');
    } else {
      console.log(`⚠️  Unexpected status code: ${res.statusCode}`);
    }
  });
});

req.on('error', (e) => {
  console.error(`❌ Request error: ${e.message}`);
});

req.end();
