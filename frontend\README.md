# AIGC Service Hub - Frontend

AIGC Service Hub 前端应用是一个基于 React 18 和 TypeScript 的现代化 Web 应用，为 AI 生成内容提供完整的市场平台用户界面。

## 🚀 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite 5
- **状态管理**: Redux Toolkit + React Redux
- **UI 组件库**: Material-UI (MUI) 5
- **路由**: React Router v6
- **HTTP 客户端**: Axios
- **样式方案**: Emotion + CSS-in-JS
- **表单处理**: React Hook Form
- **国际化**: react-i18next
- **动画**: Framer Motion
- **图表**: Recharts
- **代码规范**: ESLint + Prettier + Husky
- **测试**: Vitest + React Testing Library
- **PWA**: Vite PWA Plugin

## 📁 项目结构

```
frontend/
├── public/                 # 静态资源
│   ├── favicon.ico
│   ├── manifest.json
│   └── robots.txt
├── src/
│   ├── assets/            # 静态资源文件
│   │   ├── images/
│   │   ├── icons/
│   │   └── fonts/
│   ├── components/        # 可复用组件
│   │   ├── common/        # 通用组件
│   │   ├── forms/         # 表单组件
│   │   ├── layout/        # 布局组件
│   │   └── ui/            # UI 组件
│   ├── pages/             # 页面组件
│   │   ├── auth/          # 认证相关页面
│   │   ├── dashboard/     # 仪表板页面
│   │   ├── assets/        # 资产相关页面
│   │   ├── profile/       # 用户资料页面
│   │   └── admin/         # 管理后台页面
│   ├── hooks/             # 自定义 Hook
│   ├── services/          # API 服务层
│   ├── store/             # Redux 状态管理
│   │   ├── slices/        # Redux 切片
│   │   └── index.ts       # Store 配置
│   ├── types/             # TypeScript 类型定义
│   ├── utils/             # 工具函数
│   ├── constants/         # 常量定义
│   ├── config/            # 配置文件
│   ├── locales/           # 多语言文件
│   ├── styles/            # 样式文件
│   ├── App.tsx            # 根组件
│   ├── main.tsx           # 应用入口
│   └── vite-env.d.ts      # Vite 类型声明
├── package.json
├── tsconfig.json
├── vite.config.ts
├── .eslintrc.js
├── .prettierrc
├── .gitignore
└── README.md
```

## 🛠️ 开发环境设置

### 前置要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 使用 npm
npm install

# 使用 yarn
yarn install

# 使用 pnpm
pnpm install
```

### 环境变量配置

1. 复制环境变量模板文件：
```bash
cp .env.example .env
```

2. 根据实际情况配置环境变量：
```bash
# API 配置
VITE_API_BASE_URL=http://localhost:3001/api
VITE_UPLOAD_URL=http://localhost:3001

# OAuth 配置
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_FACEBOOK_APP_ID=your_facebook_app_id

# 支付配置
VITE_PAYPAL_CLIENT_ID=your_paypal_client_id
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
```

## 🚀 运行项目

### 开发模式

```bash
# 启动开发服务器
npm run dev

# 或者使用 yarn
yarn dev
```

应用将在 `http://localhost:3000` 启动。

### 构建项目

```bash
# 构建生产版本
npm run build

# 构建开发版本
npm run build:dev

# 构建预发布版本
npm run build:staging
```

### 预览构建结果

```bash
npm run preview
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
npm run test

# 运行测试并查看覆盖率
npm run test:coverage

# 运行测试 UI 界面
npm run test:ui

# 监听模式运行测试
npm run test:watch
```

### 测试文件结构

```
src/
├── __tests__/             # 全局测试文件
├── components/
│   └── __tests__/         # 组件测试
├── pages/
│   └── __tests__/         # 页面测试
├── hooks/
│   └── __tests__/         # Hook 测试
├── utils/
│   └── __tests__/         # 工具函数测试
└── setupTests.ts          # 测试设置文件
```

## 📝 代码规范

### 代码检查

```bash
# 运行 ESLint
npm run lint

# 自动修复 ESLint 错误
npm run lint:fix

# 检查代码格式
npm run format:check

# 自动格式化代码
npm run format
```

### 提交规范

项目使用 Husky 进行 Git 钩子管理，在提交代码时会自动：

1. 运行代码检查
2. 格式化代码
3. 运行测试

提交信息格式：
```
<type>(<scope>): <subject>

<body>

<footer>
```

类型说明：
- `feat`: 新功能
- `fix`: 修复
- `docs`: 文档
- `style`: 格式
- `refactor`: 重构
- `test`: 测试
- `chore`: 构建过程或辅助工具的变动

## 🔧 配置说明

### TypeScript 配置

项目使用严格的 TypeScript 配置，包括：
- 严格模式检查
- 路径别名支持
- 类型导入优化
- 装饰器支持

### 状态管理

使用 Redux Toolkit 进行状态管理，包含以下模块：
- `authSlice`: 认证状态
- `uiSlice`: UI 状态
- `assetsSlice`: 资产状态
- `transactionsSlice`: 交易状态
- `userSlice`: 用户状态
- `themeSlice`: 主题状态
- `notificationSlice`: 通知状态
- `uploadSlice`: 上传状态

### 路由配置

使用 React Router v6 进行路由管理，支持：
- 懒加载
- 路由守卫
- 嵌套路由
- 动态路由

### 国际化

支持多语言国际化，包含：
- 中文简体/繁体
- 英语
- 日语
- 韩语
- 西班牙语
- 法语
- 德语
- 等其他语言

## 📱 PWA 支持

应用支持 PWA（Progressive Web App）功能：
- 离线缓存
- 推送通知
- 安装到桌面
- 后台同步

## 🎨 主题系统

支持完整的主题系统：
- 明亮/暗黑模式
- 自定义主题色彩
- 响应式设计
- 无障碍支持

## 📊 性能优化

### 代码分割

- 路由级别的代码分割
- 组件级别的懒加载
- 第三方库的分包策略

### 缓存策略

- HTTP 缓存
- Service Worker 缓存
- 本地存储缓存

### 图片优化

- 响应式图片
- 懒加载
- WebP 格式支持

## 🔐 安全性

### 认证安全

- JWT Token 管理
- 自动刷新机制
- 安全存储策略

### 数据安全

- XSS 防护
- CSRF 防护
- 内容安全策略

## 🚀 部署

### 构建优化

```bash
# 分析构建包大小
npm run analyze

# 清理构建缓存
npm run clean
```

### 部署脚本

```bash
# 部署到测试环境
npm run deploy:staging

# 部署到生产环境
npm run deploy:production
```

## 📚 API 文档

API 服务层封装了所有与后端的交互：

### 认证 API
- `authAPI.login()` - 用户登录
- `authAPI.register()` - 用户注册
- `authAPI.logout()` - 用户退出
- `authAPI.refreshToken()` - 刷新令牌

### 资产 API
- `assetsAPI.getAssets()` - 获取资产列表
- `assetsAPI.createAsset()` - 创建资产
- `assetsAPI.updateAsset()` - 更新资产
- `assetsAPI.deleteAsset()` - 删除资产

### 用户 API
- `userAPI.getUserProfile()` - 获取用户资料
- `userAPI.updateUserProfile()` - 更新用户资料
- `userAPI.uploadAvatar()` - 上传头像

### 交易 API
- `transactionsAPI.getTransactions()` - 获取交易记录
- `transactionsAPI.createPaymentIntent()` - 创建支付意图
- `transactionsAPI.confirmPayment()` - 确认支付

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📝 更新日志

### [1.0.0] - 2024-01-01
- 初始版本发布
- 完整的用户认证系统
- 资产管理功能
- 交易支付功能
- 用户个人中心
- 管理后台
- 多语言支持
- PWA 支持

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 官网: https://aigchub.com
- 邮箱: <EMAIL>
- GitHub: https://github.com/aigchub/aigc-service-hub

## 🙏 致谢

感谢所有为项目做出贡献的开发者和用户。

---

**注意**: 这是一个正在开发中的项目，某些功能可能还不完善。如果您遇到任何问题或有改进建议，请随时提交 Issue 或 Pull Request。