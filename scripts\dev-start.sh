#!/bin/bash

# ===========================================
# AIGC Service Hub MVP 1.0 - 开发环境启动脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AIGC Service Hub MVP 1.0 - 开发环境启动脚本

使用方法: $0 [OPTIONS]

OPTIONS:
    -h, --help          显示帮助信息
    -v, --verbose       详细输出
    -f, --fresh         全新启动 (清理数据)
    -b, --build         重新构建镜像
    -d, --detach        后台运行
    -w, --watch         监听文件变化
    -t, --test          启动测试环境
    --db-only           仅启动数据库服务
    --api-only          仅启动API服务
    --web-only          仅启动前端服务
    --tools             启动开发工具
    --no-logs           不显示日志

示例:
    $0                  # 启动完整开发环境
    $0 -f               # 全新启动
    $0 -b               # 重新构建后启动
    $0 --db-only        # 仅启动数据库
    $0 --tools          # 启动开发工具

EOF
}

# 检查环境
check_environment() {
    log_info "检查开发环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "docker-compose.dev.yml" ]; then
        log_error "开发环境配置文件不存在: docker-compose.dev.yml"
        exit 1
    fi
    
    # 设置环境变量
    if [ ! -f ".env" ]; then
        if [ -f ".env.development" ]; then
            cp ".env.development" ".env"
            log_info "使用开发环境配置: .env.development"
        else
            log_error "环境配置文件不存在"
            exit 1
        fi
    fi
    
    log_success "环境检查完成"
}

# 创建必要目录
create_directories() {
    log_info "创建开发环境目录..."
    
    directories=(
        "database/backups"
        "database/postgres-data"
        "database/redis-data"
        "backend/logs"
        "backend/uploads"
        "nginx/logs"
        "scripts/logs"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
    done
    
    log_success "目录创建完成"
}

# 清理现有环境
clean_environment() {
    if [ "$FRESH_START" = true ]; then
        log_warning "清理现有开发环境..."
        
        # 停止所有容器
        docker-compose -f docker-compose.dev.yml down -v --remove-orphans 2>/dev/null || true
        
        # 清理数据目录
        rm -rf database/postgres-data/* 2>/dev/null || true
        rm -rf database/redis-data/* 2>/dev/null || true
        rm -rf backend/logs/* 2>/dev/null || true
        rm -rf nginx/logs/* 2>/dev/null || true
        
        # 清理Docker镜像
        docker system prune -f 2>/dev/null || true
        
        log_success "环境清理完成"
    fi
}

# 构建镜像
build_images() {
    if [ "$BUILD_IMAGES" = true ]; then
        log_info "构建开发环境镜像..."
        
        docker-compose -f docker-compose.dev.yml build --no-cache
        
        log_success "镜像构建完成"
    fi
}

# 启动数据库服务
start_database() {
    log_info "启动数据库服务..."
    
    # 启动PostgreSQL和Redis
    docker-compose -f docker-compose.dev.yml up -d postgres redis
    
    # 等待数据库启动
    log_info "等待数据库服务启动..."
    sleep 10
    
    # 检查数据库连接
    for i in {1..30}; do
        if docker-compose -f docker-compose.dev.yml exec postgres pg_isready -U postgres -q; then
            log_success "PostgreSQL 启动成功"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "PostgreSQL 启动超时"
            exit 1
        fi
        
        sleep 2
    done
    
    # 检查Redis连接
    for i in {1..30}; do
        if docker-compose -f docker-compose.dev.yml exec redis redis-cli ping | grep -q "PONG"; then
            log_success "Redis 启动成功"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "Redis 启动超时"
            exit 1
        fi
        
        sleep 2
    done
}

# 启动API服务
start_api() {
    log_info "启动API服务..."
    
    # 启动后端服务
    docker-compose -f docker-compose.dev.yml up -d backend
    
    # 等待API启动
    log_info "等待API服务启动..."
    sleep 15
    
    # 检查API健康状态
    for i in {1..30}; do
        if curl -f -s "http://localhost:3000/api/v1/health" > /dev/null; then
            log_success "API 启动成功"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "API 启动超时"
            exit 1
        fi
        
        sleep 2
    done
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."
    
    # 启动前端服务
    docker-compose -f docker-compose.dev.yml up -d frontend
    
    # 等待前端启动
    log_info "等待前端服务启动..."
    sleep 20
    
    # 检查前端服务
    for i in {1..30}; do
        if curl -f -s "http://localhost:3001" > /dev/null; then
            log_success "前端 启动成功"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "前端 启动超时"
            exit 1
        fi
        
        sleep 2
    done
}

# 启动开发工具
start_tools() {
    if [ "$START_TOOLS" = true ]; then
        log_info "启动开发工具..."
        
        # 启动数据库管理工具
        docker-compose -f docker-compose.dev.yml up -d adminer
        
        # 启动Redis管理工具
        docker-compose -f docker-compose.dev.yml up -d redis-commander
        
        # 启动邮件测试工具
        docker-compose -f docker-compose.dev.yml up -d mailhog
        
        # 启动文档服务
        docker-compose -f docker-compose.dev.yml up -d docs
        
        sleep 5
        
        log_success "开发工具启动完成"
    fi
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待后端服务完全启动
    sleep 5
    
    # 运行迁移
    docker-compose -f docker-compose.dev.yml exec backend npm run db:migrate || true
    
    log_success "数据库迁移完成"
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    
    # 显示容器状态
    docker-compose -f docker-compose.dev.yml ps
    
    echo
    log_info "服务访问地址:"
    echo "  🌐 前端应用: http://localhost:3001"
    echo "  🚀 后端API: http://localhost:3000"
    echo "  📚 API文档: http://localhost:3000/api/docs"
    echo "  🗄️  数据库管理: http://localhost:8080"
    echo "  🔧 Redis管理: http://localhost:8081"
    echo "  📧 邮件测试: http://localhost:8025"
    echo "  📖 项目文档: http://localhost:8082"
    echo
    log_info "开发命令:"
    echo "  查看日志: ./scripts/logs.sh"
    echo "  停止服务: ./scripts/dev-stop.sh"
    echo "  重置环境: ./scripts/dev-reset.sh"
    echo "  执行测试: docker-compose -f docker-compose.dev.yml exec backend npm test"
}

# 监听文件变化
watch_files() {
    if [ "$WATCH_FILES" = true ]; then
        log_info "启动文件监听..."
        
        # 安装文件监听工具
        if ! command -v fswatch &> /dev/null; then
            log_warning "fswatch 未安装，跳过文件监听"
            return
        fi
        
        # 监听后端文件变化
        fswatch -o backend/src | while read num; do
            log_info "检测到后端文件变化，重启服务..."
            docker-compose -f docker-compose.dev.yml restart backend
        done &
        
        # 监听前端文件变化
        fswatch -o frontend/src | while read num; do
            log_info "检测到前端文件变化，重新构建..."
            docker-compose -f docker-compose.dev.yml restart frontend
        done &
        
        log_success "文件监听启动完成"
    fi
}

# 显示日志
show_logs() {
    if [ "$SHOW_LOGS" = true ]; then
        log_info "显示实时日志..."
        
        # 显示所有服务日志
        docker-compose -f docker-compose.dev.yml logs -f
    fi
}

# 主函数
main() {
    # 默认参数
    VERBOSE=false
    FRESH_START=false
    BUILD_IMAGES=false
    DETACH=false
    WATCH_FILES=false
    TEST_MODE=false
    DB_ONLY=false
    API_ONLY=false
    WEB_ONLY=false
    START_TOOLS=false
    SHOW_LOGS=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--fresh)
                FRESH_START=true
                shift
                ;;
            -b|--build)
                BUILD_IMAGES=true
                shift
                ;;
            -d|--detach)
                DETACH=true
                SHOW_LOGS=false
                shift
                ;;
            -w|--watch)
                WATCH_FILES=true
                shift
                ;;
            -t|--test)
                TEST_MODE=true
                shift
                ;;
            --db-only)
                DB_ONLY=true
                shift
                ;;
            --api-only)
                API_ONLY=true
                shift
                ;;
            --web-only)
                WEB_ONLY=true
                shift
                ;;
            --tools)
                START_TOOLS=true
                shift
                ;;
            --no-logs)
                SHOW_LOGS=false
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置详细输出
    if [ "$VERBOSE" = true ]; then
        set -x
    fi
    
    log_info "启动 AIGC Service Hub MVP 1.0 开发环境"
    
    # 执行启动步骤
    check_environment
    create_directories
    clean_environment
    build_images
    
    # 根据参数启动不同服务
    if [ "$DB_ONLY" = true ]; then
        start_database
    elif [ "$API_ONLY" = true ]; then
        start_database
        start_api
        run_migrations
    elif [ "$WEB_ONLY" = true ]; then
        start_frontend
    else
        # 启动完整环境
        start_database
        start_api
        start_frontend
        start_tools
        run_migrations
    fi
    
    # 显示状态
    show_status
    
    # 启动文件监听
    watch_files
    
    # 显示日志
    if [ "$SHOW_LOGS" = true ]; then
        show_logs
    fi
    
    log_success "开发环境启动完成！"
}

# 执行主函数
main "$@"