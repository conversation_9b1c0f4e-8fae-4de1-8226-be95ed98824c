// 自定义错误类
export class ApiError extends Error {
  public readonly statusCode: number;
  public readonly code: string;
  public readonly details?: any;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_SERVER_ERROR',
    details?: any,
    isOperational: boolean = true
  ) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = isOperational;
    
    // 设置错误名称
    this.name = this.constructor.name;
    
    // 捕获堆栈跟踪
    Error.captureStackTrace(this, this.constructor);
  }
}

// 业务错误类
export class ValidationError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

export class AuthenticationError extends ApiError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

export class AuthorizationError extends ApiError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

export class NotFoundError extends ApiError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND');
  }
}

export class ConflictError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 409, 'CONFLICT', details);
  }
}

export class RateLimitError extends ApiError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, 'RATE_LIMIT_EXCEEDED');
  }
}

export class InternalServerError extends ApiError {
  constructor(message: string = 'Internal server error') {
    super(message, 500, 'INTERNAL_SERVER_ERROR');
  }
}

export class ServiceUnavailableError extends ApiError {
  constructor(message: string = 'Service unavailable') {
    super(message, 503, 'SERVICE_UNAVAILABLE');
  }
}

// 特定业务错误类
export class InvalidCredentialsError extends AuthenticationError {
  constructor() {
    super('Invalid email or password');
  }
}

export class TokenExpiredError extends AuthenticationError {
  constructor() {
    super('Token has expired');
  }
}

export class InvalidTokenError extends AuthenticationError {
  constructor() {
    super('Invalid token');
  }
}

export class UserNotFoundError extends NotFoundError {
  constructor() {
    super('User not found');
  }
}

export class AssetNotFoundError extends NotFoundError {
  constructor() {
    super('Asset not found');
  }
}

export class TransactionNotFoundError extends NotFoundError {
  constructor() {
    super('Transaction not found');
  }
}

export class InsufficientPointsError extends ApiError {
  constructor(required: number, available: number) {
    super(`Insufficient points. Required: ${required}, Available: ${available}`, 400, 'INSUFFICIENT_POINTS', {
      required,
      available,
    });
  }
}

export class InsufficientBalanceError extends ApiError {
  constructor(required: number, available: number) {
    super(`Insufficient balance. Required: ${required}, Available: ${available}`, 400, 'INSUFFICIENT_BALANCE', {
      required,
      available,
    });
  }
}

export class AssetNotPurchasedError extends AuthorizationError {
  constructor() {
    super('Asset not purchased');
  }
}

export class FileUploadError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 400, 'FILE_UPLOAD_ERROR', details);
  }
}

export class PaymentProcessingError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 400, 'PAYMENT_PROCESSING_ERROR', details);
  }
}

export class WithdrawalError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 400, 'WITHDRAWAL_ERROR', details);
  }
}

export class CommissionCalculationError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 500, 'COMMISSION_CALCULATION_ERROR', details);
  }
}

export class DatabaseError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 500, 'DATABASE_ERROR', details);
  }
}

export class CacheError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 500, 'CACHE_ERROR', details);
  }
}

export class S3Error extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 500, 'S3_ERROR', details);
  }
}

export class EmailError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 500, 'EMAIL_ERROR', details);
  }
}

export class PayPalError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 500, 'PAYPAL_ERROR', details);
  }
}

export class OAuthError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 400, 'OAUTH_ERROR', details);
  }
}

// 错误处理工具函数
export const isApiError = (error: any): error is ApiError => {
  return error instanceof ApiError;
};

export const isOperationalError = (error: any): boolean => {
  return isApiError(error) && error.isOperational;
};

// 错误响应格式化
export const formatErrorResponse = (error: ApiError | Error) => {
  if (isApiError(error)) {
    return {
      success: false,
      error: {
        code: error.code,
        message: error.message,
        details: error.details,
      },
    };
  }

  // 对于非ApiError，返回通用错误响应
  return {
    success: false,
    error: {
      code: 'INTERNAL_SERVER_ERROR',
      message: 'An unexpected error occurred',
    },
  };
};

// 错误处理中间件
export const errorHandler = (error: any, req: any, res: any, next: any) => {
  // 记录错误
  console.error('Error occurred:', error);

  // 如果是ApiError，返回格式化的错误响应
  if (isApiError(error)) {
    return res.status(error.statusCode).json(formatErrorResponse(error));
  }

  // 处理常见的第三方错误
  if (error.name === 'ValidationError') {
    const validationError = new ValidationError('Validation failed', error.details);
    return res.status(validationError.statusCode).json(formatErrorResponse(validationError));
  }

  if (error.name === 'JsonWebTokenError') {
    const jwtError = new InvalidTokenError();
    return res.status(jwtError.statusCode).json(formatErrorResponse(jwtError));
  }

  if (error.name === 'TokenExpiredError') {
    const expiredError = new TokenExpiredError();
    return res.status(expiredError.statusCode).json(formatErrorResponse(expiredError));
  }

  if (error.code === 'LIMIT_FILE_SIZE') {
    const fileSizeError = new FileUploadError('File size too large');
    return res.status(fileSizeError.statusCode).json(formatErrorResponse(fileSizeError));
  }

  if (error.code === '23505') { // PostgreSQL unique constraint violation
    const conflictError = new ConflictError('Resource already exists');
    return res.status(conflictError.statusCode).json(formatErrorResponse(conflictError));
  }

  if (error.code === '23503') { // PostgreSQL foreign key constraint violation
    const notFoundError = new NotFoundError('Referenced resource not found');
    return res.status(notFoundError.statusCode).json(formatErrorResponse(notFoundError));
  }

  // 对于未知错误，返回通用服务器错误
  const serverError = new InternalServerError();
  return res.status(serverError.statusCode).json(formatErrorResponse(serverError));
};

// 异步错误处理包装器
export const asyncHandler = (fn: Function) => {
  return (req: any, res: any, next: any) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 错误验证
export const validateError = (error: any): ApiError => {
  if (isApiError(error)) {
    return error;
  }
  
  return new InternalServerError(error.message || 'An unexpected error occurred');
};