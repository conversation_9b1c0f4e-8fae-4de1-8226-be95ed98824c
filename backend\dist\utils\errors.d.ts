export declare class ApiError extends Error {
    readonly statusCode: number;
    readonly code: string;
    readonly details?: any;
    readonly isOperational: boolean;
    constructor(message: string, statusCode?: number, code?: string, details?: any, isOperational?: boolean);
}
export declare class ValidationError extends ApiError {
    constructor(message: string, details?: any);
}
export declare class AuthenticationError extends ApiError {
    constructor(message?: string);
}
export declare class AuthorizationError extends ApiError {
    constructor(message?: string);
}
export declare class NotFoundError extends ApiError {
    constructor(message?: string);
}
export declare class ConflictError extends ApiError {
    constructor(message: string, details?: any);
}
export declare class RateLimitError extends ApiError {
    constructor(message?: string);
}
export declare class InternalServerError extends ApiError {
    constructor(message?: string);
}
export declare class ServiceUnavailableError extends ApiError {
    constructor(message?: string);
}
export declare class InvalidCredentialsError extends AuthenticationError {
    constructor();
}
export declare class TokenExpiredError extends AuthenticationError {
    constructor();
}
export declare class InvalidTokenError extends AuthenticationError {
    constructor();
}
export declare class UserNotFoundError extends NotFoundError {
    constructor();
}
export declare class AssetNotFoundError extends NotFoundError {
    constructor();
}
export declare class TransactionNotFoundError extends NotFoundError {
    constructor();
}
export declare class InsufficientPointsError extends ApiError {
    constructor(required: number, available: number);
}
export declare class InsufficientBalanceError extends ApiError {
    constructor(required: number, available: number);
}
export declare class AssetNotPurchasedError extends AuthorizationError {
    constructor();
}
export declare class FileUploadError extends ApiError {
    constructor(message: string, details?: any);
}
export declare class PaymentProcessingError extends ApiError {
    constructor(message: string, details?: any);
}
export declare class WithdrawalError extends ApiError {
    constructor(message: string, details?: any);
}
export declare class CommissionCalculationError extends ApiError {
    constructor(message: string, details?: any);
}
export declare class DatabaseError extends ApiError {
    constructor(message: string, details?: any);
}
export declare class CacheError extends ApiError {
    constructor(message: string, details?: any);
}
export declare class S3Error extends ApiError {
    constructor(message: string, details?: any);
}
export declare class EmailError extends ApiError {
    constructor(message: string, details?: any);
}
export declare class PayPalError extends ApiError {
    constructor(message: string, details?: any);
}
export declare class OAuthError extends ApiError {
    constructor(message: string, details?: any);
}
export declare const isApiError: (error: any) => error is ApiError;
export declare const isOperationalError: (error: any) => boolean;
export declare const formatErrorResponse: (error: ApiError | Error) => {
    success: boolean;
    error: {
        code: string;
        message: string;
        details: any;
    };
} | {
    success: boolean;
    error: {
        code: string;
        message: string;
        details?: undefined;
    };
};
export declare const errorHandler: (error: any, req: any, res: any, next: any) => any;
export declare const asyncHandler: (fn: Function) => (req: any, res: any, next: any) => void;
export declare const validateError: (error: any) => ApiError;
//# sourceMappingURL=errors.d.ts.map