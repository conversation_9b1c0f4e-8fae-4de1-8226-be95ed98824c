"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_controller_1 = require("../controllers/auth.controller");
const auth_middleware_1 = __importDefault(require("../middlewares/auth.middleware"));
const router = (0, express_1.Router)();
const authController = new auth_controller_1.AuthController();
router.post('/register', authController.register.bind(authController));
router.post('/login', authController.login.bind(authController));
router.post('/refresh', authController.refreshToken.bind(authController));
router.post('/request-password-reset', authController.requestPasswordReset.bind(authController));
router.post('/reset-password', authController.resetPassword.bind(authController));
router.get('/verify-email', authController.verifyEmail.bind(authController));
router.post('/resend-verification', authController.resendVerification.bind(authController));
router.get('/check-email', authController.checkEmailAvailability.bind(authController));
router.get('/google/callback', authController.googleCallback.bind(authController));
router.get('/github/callback', authController.githubCallback.bind(authController));
router.use(auth_middleware_1.default.authenticate);
router.get('/profile', authController.getProfile.bind(authController));
router.post('/change-password', authController.changePassword.bind(authController));
router.get('/permissions', authController.getUserPermissions.bind(authController));
router.post('/logout', authController.logout.bind(authController));
exports.default = router;
//# sourceMappingURL=auth.routes.js.map