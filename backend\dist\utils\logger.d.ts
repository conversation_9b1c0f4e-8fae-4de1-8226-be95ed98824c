import winston from 'winston';
declare const logger: winston.Logger;
export declare const logError: (message: string, error?: Error, meta?: any) => void;
export declare const logWarn: (message: string, meta?: any) => void;
export declare const logInfo: (message: string, meta?: any) => void;
export declare const logDebug: (message: string, meta?: any) => void;
export declare const createRequestLogger: () => (req: any, res: any, next: any) => void;
export declare const createErrorLogger: () => (error: any, req: any, res: any, next: any) => void;
export declare const logDatabaseOperation: (operation: string, table: string, meta?: any) => void;
export declare const logBusinessOperation: (operation: string, userId: number, meta?: any) => void;
export declare const logSecurityEvent: (event: string, userId?: number, meta?: any) => void;
export declare const logPerformance: (operation: string, duration: number, meta?: any) => void;
export declare const logFinancialOperation: (operation: string, amount: number, userId: number, meta?: any) => void;
export declare const logFileOperation: (operation: string, fileKey: string, userId: number, meta?: any) => void;
export declare const logCacheOperation: (operation: string, key: string, hit?: boolean, meta?: any) => void;
export declare const logThirdPartyService: (service: string, operation: string, success: boolean, meta?: any) => void;
export { logger };
export default logger;
//# sourceMappingURL=logger.d.ts.map