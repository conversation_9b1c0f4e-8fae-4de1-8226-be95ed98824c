import { Transaction, TransactionStatus, PurchaseRequest, TransactionQuery } from '../types';
export declare class TransactionService {
    private db;
    constructor();
    createPurchaseTransaction(buyerId: number, purchaseData: PurchaseRequest): Promise<Transaction>;
    confirmPurchase(transactionId: number, paypalTransactionId?: string): Promise<Transaction>;
    cancelTransaction(transactionId: number): Promise<void>;
    getTransactionById(transactionId: number): Promise<Transaction>;
    getUserTransactions(userId: number, query: TransactionQuery): Promise<{
        transactions: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getAllTransactions(query: TransactionQuery): Promise<{
        transactions: any[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    updateTransactionStatus(transactionId: number, status: TransactionStatus): Promise<void>;
    private calculateCreatorEarnings;
}
export default TransactionService;
//# sourceMappingURL=transaction.service.d.ts.map