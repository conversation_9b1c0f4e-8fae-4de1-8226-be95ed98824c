# 应用配置
NODE_ENV=development
PORT=3000
API_VERSION=v1
APP_NAME=AIGC Service Hub

# 数据库配置
DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/aigc_service_hub
DATABASE_POOL_SIZE=20

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# AWS配置
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
S3_PRIVATE_BUCKET=aigc-private-assets-dev
S3_PUBLIC_BUCKET=aigc-public-assets-dev

# JWT配置
JWT_SECRET=your-jwt-secret-key-change-this-in-production
JWT_EXPIRES_IN=1d
REFRESH_TOKEN_EXPIRES_IN=7d

# PayPal配置
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_SANDBOX=true
PAYPAL_WEBHOOK_ID=your-webhook-id

# OAuth配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# 邮件配置
AWS_SES_REGION=us-west-2
FROM_EMAIL=<EMAIL>
SMTP_HOST=email-smtp.us-west-2.amazonaws.com
SMTP_PORT=587
SMTP_USER=your-smtp-username
SMTP_PASSWORD=your-smtp-password

# 应用URL
FRONTEND_URL=http://localhost:3001
BACKEND_URL=http://localhost:3000

# 文件上传配置
MAX_FILE_SIZE=32212254720
UPLOAD_URL_EXPIRES_IN=900
DOWNLOAD_URL_EXPIRES_IN=300

# 缓存配置
CACHE_DEFAULT_TTL=300
CACHE_ASSETS_TTL=300
CACHE_USER_PROFILE_TTL=600

# 速率限制配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 监控配置
ENABLE_METRICS=true
METRICS_INTERVAL=60000

# 系统配置
POINTS_RATE=100
COMMISSION_INDIVIDUAL_BASE=5
COMMISSION_INDIVIDUAL_INCREMENT=5
COMMISSION_INDIVIDUAL_MAX=50
COMMISSION_ENTERPRISE_BASE=8
COMMISSION_ENTERPRISE_INCREMENT=8
COMMISSION_ENTERPRISE_MAX=56

# 财务配置
LEDGER_PENDING_DAYS=7
MIN_WITHDRAWAL_AMOUNT=10.00