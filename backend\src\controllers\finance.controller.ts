import { Request, Response } from 'express';
import { FinanceService } from '@/services/finance.service';
import { WithdrawalRequestBody } from '@/types';
import { ValidationError } from '@/utils/errors';
import { asyncHandler } from '@/utils/errors';
import { logger, logBusinessOperation } from '@/utils/logger';

export class FinanceController {
  private financeService: FinanceService;

  constructor() {
    this.financeService = new FinanceService();
  }

  // 创建提现请求
  createWithdrawalRequest = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const { amount, paypalEmail }: WithdrawalRequestBody = req.body;

    if (!amount || !paypalEmail) {
      throw new ValidationError('Amount and PayPal email are required');
    }

    const withdrawalRequest = await this.financeService.createWithdrawalRequest(
      req.user.id,
      amount,
      paypalEmail
    );

    res.status(201).json({
      success: true,
      data: { withdrawalRequest },
      message: 'Withdrawal request created successfully',
    });
  });

  // 获取用户的提现请求
  getWithdrawalRequests = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;

    const result = await this.financeService.getUserWithdrawalRequests(req.user.id, { page, limit });

    res.json({
      success: true,
      data: result,
      message: 'Withdrawal requests retrieved successfully',
    });
  });

  // 获取提现请求详情
  getWithdrawalById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const withdrawalId = parseInt(req.params.id);
    
    if (isNaN(withdrawalId)) {
      throw new ValidationError('Invalid withdrawal ID');
    }

    const withdrawal = await this.financeService.getWithdrawalById(withdrawalId);

    res.json({
      success: true,
      data: { withdrawal },
      message: 'Withdrawal details retrieved successfully',
    });
  });

  // 获取用户收益
  getEarnings = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const earnings = await this.financeService.getUserEarnings(req.user.id);

    res.json({
      success: true,
      data: earnings,
      message: 'Earnings retrieved successfully',
    });
  });

  // 获取用户余额
  getBalance = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const balance = await this.financeService.getUserBalance(req.user.id);

    res.json({
      success: true,
      data: balance,
      message: 'Balance retrieved successfully',
    });
  });

  // 获取所有提现请求（管理员）
  getAllWithdrawals = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 20;
    const status = req.query.status as string;

    const result = await this.financeService.getAllWithdrawalRequests({ page, limit, status });

    res.json({
      success: true,
      data: result,
      message: 'All withdrawal requests retrieved successfully',
    });
  });

  // 批准提现请求（管理员）
  approveWithdrawal = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const withdrawalId = parseInt(req.params.id);
    
    if (isNaN(withdrawalId)) {
      throw new ValidationError('Invalid withdrawal ID');
    }

    const { adminNotes } = req.body;

    await this.financeService.approveWithdrawal(withdrawalId, req.user.id, adminNotes);

    res.json({
      success: true,
      message: 'Withdrawal request approved successfully',
    });
  });

  // 拒绝提现请求（管理员）
  rejectWithdrawal = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const withdrawalId = parseInt(req.params.id);
    
    if (isNaN(withdrawalId)) {
      throw new ValidationError('Invalid withdrawal ID');
    }

    const { rejectionReason } = req.body;

    if (!rejectionReason) {
      throw new ValidationError('Rejection reason is required');
    }

    await this.financeService.rejectWithdrawal(withdrawalId, req.user.id, rejectionReason);

    res.json({
      success: true,
      message: 'Withdrawal request rejected successfully',
    });
  });

  // 获取平台统计数据（管理员）
  getPlatformStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const stats = await this.financeService.getPlatformStats();

    res.json({
      success: true,
      data: stats,
      message: 'Platform statistics retrieved successfully',
    });
  });
}

export default FinanceController;