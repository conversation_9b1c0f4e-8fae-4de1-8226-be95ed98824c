"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = void 0;
const admin_service_1 = require("../services/admin.service");
const errors_1 = require("../utils/errors");
class AdminController {
    constructor() {
        this.getUsers = async (req, res, next) => {
            try {
                const { page = 1, limit = 20, search, role, status } = req.query;
                const filters = {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    search: search,
                    role: role,
                    status: status,
                };
                const result = await this.adminService.getUsers(filters);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getUserById = async (req, res, next) => {
            try {
                const { userId } = req.params;
                const user = await this.adminService.getUserById(parseInt(userId));
                if (!user) {
                    throw new errors_1.NotFoundError('User not found');
                }
                res.json({
                    success: true,
                    data: user,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.updateUser = async (req, res, next) => {
            try {
                const { userId } = req.params;
                const updateData = req.body;
                const user = await this.adminService.updateUser(parseInt(userId), updateData);
                res.json({
                    success: true,
                    data: user,
                    message: 'User updated successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteUser = async (req, res, next) => {
            try {
                const { userId } = req.params;
                await this.adminService.deleteUser(parseInt(userId));
                res.json({
                    success: true,
                    message: 'User deleted successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.activateUser = async (req, res, next) => {
            try {
                const { userId } = req.params;
                await this.adminService.activateUser(parseInt(userId));
                res.json({
                    success: true,
                    message: 'User activated successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.deactivateUser = async (req, res, next) => {
            try {
                const { userId } = req.params;
                await this.adminService.deactivateUser(parseInt(userId));
                res.json({
                    success: true,
                    message: 'User deactivated successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getAssets = async (req, res, next) => {
            try {
                const { page = 1, limit = 20, search, category, status } = req.query;
                const filters = {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    search: search,
                    category: category,
                    status: status,
                };
                const result = await this.adminService.getAssets(filters);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getAssetById = async (req, res, next) => {
            try {
                const { assetId } = req.params;
                const asset = await this.adminService.getAssetById(parseInt(assetId));
                if (!asset) {
                    throw new errors_1.NotFoundError('Asset not found');
                }
                res.json({
                    success: true,
                    data: asset,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.updateAsset = async (req, res, next) => {
            try {
                const { assetId } = req.params;
                const updateData = req.body;
                const asset = await this.adminService.updateAsset(parseInt(assetId), updateData);
                res.json({
                    success: true,
                    data: asset,
                    message: 'Asset updated successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.deleteAsset = async (req, res, next) => {
            try {
                const { assetId } = req.params;
                await this.adminService.deleteAsset(parseInt(assetId));
                res.json({
                    success: true,
                    message: 'Asset deleted successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.approveAsset = async (req, res, next) => {
            try {
                const { assetId } = req.params;
                await this.adminService.approveAsset(parseInt(assetId));
                res.json({
                    success: true,
                    message: 'Asset approved successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.rejectAsset = async (req, res, next) => {
            try {
                const { assetId } = req.params;
                const { reason } = req.body;
                await this.adminService.rejectAsset(parseInt(assetId), reason);
                res.json({
                    success: true,
                    message: 'Asset rejected successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getTransactions = async (req, res, next) => {
            try {
                const { page = 1, limit = 20, search, status, type } = req.query;
                const filters = {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    search: search,
                    status: status,
                    type: type,
                };
                const result = await this.adminService.getTransactions(filters);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getTransactionById = async (req, res, next) => {
            try {
                const { transactionId } = req.params;
                const transaction = await this.adminService.getTransactionById(parseInt(transactionId));
                if (!transaction) {
                    throw new errors_1.NotFoundError('Transaction not found');
                }
                res.json({
                    success: true,
                    data: transaction,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.refundTransaction = async (req, res, next) => {
            try {
                const { transactionId } = req.params;
                const { reason } = req.body;
                await this.adminService.refundTransaction(parseInt(transactionId), reason);
                res.json({
                    success: true,
                    message: 'Transaction refunded successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getWithdrawals = async (req, res, next) => {
            try {
                const { page = 1, limit = 20, status } = req.query;
                const filters = {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    status: status,
                };
                const result = await this.adminService.getWithdrawals(filters);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getWithdrawalById = async (req, res, next) => {
            try {
                const { withdrawalId } = req.params;
                const withdrawal = await this.adminService.getWithdrawalById(parseInt(withdrawalId));
                if (!withdrawal) {
                    throw new errors_1.NotFoundError('Withdrawal not found');
                }
                res.json({
                    success: true,
                    data: withdrawal,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.approveWithdrawal = async (req, res, next) => {
            try {
                const { withdrawalId } = req.params;
                await this.adminService.approveWithdrawal(parseInt(withdrawalId));
                res.json({
                    success: true,
                    message: 'Withdrawal approved successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.rejectWithdrawal = async (req, res, next) => {
            try {
                const { withdrawalId } = req.params;
                const { reason } = req.body;
                await this.adminService.rejectWithdrawal(parseInt(withdrawalId), reason);
                res.json({
                    success: true,
                    message: 'Withdrawal rejected successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getOverviewStats = async (req, res, next) => {
            try {
                const stats = await this.adminService.getOverviewStats();
                res.json({
                    success: true,
                    data: stats,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getUserStats = async (req, res, next) => {
            try {
                const stats = await this.adminService.getUserStats();
                res.json({
                    success: true,
                    data: stats,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getAssetStats = async (req, res, next) => {
            try {
                const stats = await this.adminService.getAssetStats();
                res.json({
                    success: true,
                    data: stats,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getTransactionStats = async (req, res, next) => {
            try {
                const stats = await this.adminService.getTransactionStats();
                res.json({
                    success: true,
                    data: stats,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getRevenueStats = async (req, res, next) => {
            try {
                const stats = await this.adminService.getRevenueStats();
                res.json({
                    success: true,
                    data: stats,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getSettings = async (req, res, next) => {
            try {
                const settings = await this.adminService.getSettings();
                res.json({
                    success: true,
                    data: settings,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.updateSettings = async (req, res, next) => {
            try {
                const settings = await this.adminService.updateSettings(req.body);
                res.json({
                    success: true,
                    data: settings,
                    message: 'Settings updated successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getSecurityLogs = async (req, res, next) => {
            try {
                const { page = 1, limit = 50 } = req.query;
                const filters = {
                    page: parseInt(page),
                    limit: parseInt(limit),
                };
                const result = await this.adminService.getSecurityLogs(filters);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getErrorLogs = async (req, res, next) => {
            try {
                const { page = 1, limit = 50 } = req.query;
                const filters = {
                    page: parseInt(page),
                    limit: parseInt(limit),
                };
                const result = await this.adminService.getErrorLogs(filters);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getAuditLogs = async (req, res, next) => {
            try {
                const { page = 1, limit = 50 } = req.query;
                const filters = {
                    page: parseInt(page),
                    limit: parseInt(limit),
                };
                const result = await this.adminService.getAuditLogs(filters);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getSystemHealth = async (req, res, next) => {
            try {
                const health = await this.adminService.getSystemHealth();
                res.json({
                    success: true,
                    data: health,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getSystemMetrics = async (req, res, next) => {
            try {
                const metrics = await this.adminService.getSystemMetrics();
                res.json({
                    success: true,
                    data: metrics,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.getReports = async (req, res, next) => {
            try {
                const { page = 1, limit = 20, status } = req.query;
                const filters = {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    status: status,
                };
                const result = await this.adminService.getReports(filters);
                res.json({
                    success: true,
                    data: result,
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.resolveReport = async (req, res, next) => {
            try {
                const { reportId } = req.params;
                const { action, reason } = req.body;
                await this.adminService.resolveReport(parseInt(reportId), action, reason);
                res.json({
                    success: true,
                    message: 'Report resolved successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.bulkActivateUsers = async (req, res, next) => {
            try {
                const { userIds } = req.body;
                await this.adminService.bulkActivateUsers(userIds);
                res.json({
                    success: true,
                    message: 'Users activated successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.bulkDeactivateUsers = async (req, res, next) => {
            try {
                const { userIds } = req.body;
                await this.adminService.bulkDeactivateUsers(userIds);
                res.json({
                    success: true,
                    message: 'Users deactivated successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.bulkApproveAssets = async (req, res, next) => {
            try {
                const { assetIds } = req.body;
                await this.adminService.bulkApproveAssets(assetIds);
                res.json({
                    success: true,
                    message: 'Assets approved successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.bulkRejectAssets = async (req, res, next) => {
            try {
                const { assetIds, reason } = req.body;
                await this.adminService.bulkRejectAssets(assetIds, reason);
                res.json({
                    success: true,
                    message: 'Assets rejected successfully',
                });
            }
            catch (error) {
                next(error);
            }
        };
        this.adminService = new admin_service_1.AdminService();
    }
}
exports.AdminController = AdminController;
//# sourceMappingURL=admin.controller.js.map