import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// 主题模式
type ThemeMode = 'light' | 'dark' | 'system';

// 颜色方案
interface ColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  warning: string;
  success: string;
  info: string;
}

// 字体设置
interface FontSettings {
  family: string;
  size: 'small' | 'medium' | 'large';
  weight: 'normal' | 'medium' | 'bold';
  lineHeight: number;
}

// 间距设置
interface SpacingSettings {
  unit: number;
  compact: boolean;
}

// 圆角设置
interface BorderRadiusSettings {
  small: number;
  medium: number;
  large: number;
  round: number;
}

// 阴影设置
interface ShadowSettings {
  enabled: boolean;
  intensity: 'low' | 'medium' | 'high';
}

// 动画设置
interface AnimationSettings {
  enabled: boolean;
  duration: 'fast' | 'medium' | 'slow';
  easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
}

// 自定义主题
interface CustomTheme {
  id: string;
  name: string;
  colors: ColorScheme;
  fonts: FontSettings;
  spacing: SpacingSettings;
  borderRadius: BorderRadiusSettings;
  shadows: ShadowSettings;
  animations: AnimationSettings;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

// 主题状态接口
interface ThemeState {
  // 当前主题设置
  mode: ThemeMode;
  currentTheme: string; // 主题ID
  
  // 基础设置
  colors: ColorScheme;
  fonts: FontSettings;
  spacing: SpacingSettings;
  borderRadius: BorderRadiusSettings;
  shadows: ShadowSettings;
  animations: AnimationSettings;
  
  // 系统设置
  systemPreference: 'light' | 'dark';
  highContrast: boolean;
  reducedMotion: boolean;
  
  // 自定义主题
  customThemes: CustomTheme[];
  
  // 预设主题
  presetThemes: {
    light: CustomTheme;
    dark: CustomTheme;
    blue: CustomTheme;
    green: CustomTheme;
    purple: CustomTheme;
    orange: CustomTheme;
  };
  
  // 主题历史
  themeHistory: string[];
  
  // 导入导出
  isImporting: boolean;
  isExporting: boolean;
  
  // 错误状态
  error: string | null;
}

// 预设的亮色主题
const lightTheme: CustomTheme = {
  id: 'light',
  name: '明亮主题',
  colors: {
    primary: '#1976d2',
    secondary: '#dc004e',
    accent: '#ff9800',
    background: '#ffffff',
    surface: '#f5f5f5',
    text: '#212121',
    textSecondary: '#757575',
    border: '#e0e0e0',
    error: '#f44336',
    warning: '#ff9800',
    success: '#4caf50',
    info: '#2196f3',
  },
  fonts: {
    family: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
    size: 'medium',
    weight: 'normal',
    lineHeight: 1.5,
  },
  spacing: {
    unit: 8,
    compact: false,
  },
  borderRadius: {
    small: 4,
    medium: 8,
    large: 12,
    round: 50,
  },
  shadows: {
    enabled: true,
    intensity: 'medium',
  },
  animations: {
    enabled: true,
    duration: 'medium',
    easing: 'ease-in-out',
  },
  isDefault: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

// 预设的暗色主题
const darkTheme: CustomTheme = {
  id: 'dark',
  name: '深色主题',
  colors: {
    primary: '#90caf9',
    secondary: '#f48fb1',
    accent: '#ffcc02',
    background: '#121212',
    surface: '#1e1e1e',
    text: '#ffffff',
    textSecondary: '#b0b0b0',
    border: '#333333',
    error: '#f44336',
    warning: '#ff9800',
    success: '#4caf50',
    info: '#2196f3',
  },
  fonts: {
    family: 'Inter, -apple-system, BlinkMacSystemFont, sans-serif',
    size: 'medium',
    weight: 'normal',
    lineHeight: 1.5,
  },
  spacing: {
    unit: 8,
    compact: false,
  },
  borderRadius: {
    small: 4,
    medium: 8,
    large: 12,
    round: 50,
  },
  shadows: {
    enabled: true,
    intensity: 'medium',
  },
  animations: {
    enabled: true,
    duration: 'medium',
    easing: 'ease-in-out',
  },
  isDefault: true,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
};

// 从localStorage获取保存的主题设置
const getSavedThemeSettings = (): Partial<ThemeState> => {
  try {
    const savedMode = localStorage.getItem('theme-mode') as ThemeMode;
    const savedCurrentTheme = localStorage.getItem('theme-current');
    const savedCustomThemes = localStorage.getItem('theme-custom');
    const savedThemeHistory = localStorage.getItem('theme-history');
    
    return {
      mode: savedMode || 'system',
      currentTheme: savedCurrentTheme || 'light',
      customThemes: savedCustomThemes ? JSON.parse(savedCustomThemes) : [],
      themeHistory: savedThemeHistory ? JSON.parse(savedThemeHistory) : [],
    };
  } catch (error) {
    console.error('Error loading saved theme settings:', error);
    return {};
  }
};

// 检测系统主题偏好
const detectSystemPreference = (): 'light' | 'dark' => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'light';
};

// 检测系统辅助功能设置
const detectAccessibilitySettings = () => {
  const reducedMotion = typeof window !== 'undefined' && window.matchMedia && 
    window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  
  const highContrast = typeof window !== 'undefined' && window.matchMedia && 
    window.matchMedia('(prefers-contrast: high)').matches;
  
  return { reducedMotion, highContrast };
};

// 获取当前活动主题
const getActiveTheme = (state: ThemeState): CustomTheme => {
  const { mode, currentTheme, systemPreference, presetThemes } = state;
  
  if (mode === 'system') {
    return systemPreference === 'dark' ? presetThemes.dark : presetThemes.light;
  }
  
  if (mode === 'light') return presetThemes.light;
  if (mode === 'dark') return presetThemes.dark;
  
  // 查找自定义主题
  const customTheme = state.customThemes.find(theme => theme.id === currentTheme);
  return customTheme || presetThemes.light;
};

// 初始状态
const initialState: ThemeState = {
  mode: 'system',
  currentTheme: 'light',
  colors: lightTheme.colors,
  fonts: lightTheme.fonts,
  spacing: lightTheme.spacing,
  borderRadius: lightTheme.borderRadius,
  shadows: lightTheme.shadows,
  animations: lightTheme.animations,
  systemPreference: detectSystemPreference(),
  customThemes: [],
  presetThemes: {
    light: lightTheme,
    dark: darkTheme,
    blue: { ...lightTheme, id: 'blue', name: '蓝色主题', colors: { ...lightTheme.colors, primary: '#2196f3' } },
    green: { ...lightTheme, id: 'green', name: '绿色主题', colors: { ...lightTheme.colors, primary: '#4caf50' } },
    purple: { ...lightTheme, id: 'purple', name: '紫色主题', colors: { ...lightTheme.colors, primary: '#9c27b0' } },
    orange: { ...lightTheme, id: 'orange', name: '橙色主题', colors: { ...lightTheme.colors, primary: '#ff9800' } },
  },
  themeHistory: [],
  isImporting: false,
  isExporting: false,
  error: null,
  ...getSavedThemeSettings(),
  ...detectAccessibilitySettings(),
};

// 应用主题到状态
const applyThemeToState = (state: ThemeState, theme: CustomTheme) => {
  state.colors = theme.colors;
  state.fonts = theme.fonts;
  state.spacing = theme.spacing;
  state.borderRadius = theme.borderRadius;
  state.shadows = theme.shadows;
  state.animations = theme.animations;
  
  // 如果启用了辅助功能，覆盖某些设置
  if (state.reducedMotion) {
    state.animations.enabled = false;
  }
  
  if (state.highContrast) {
    // 增加对比度
    state.colors.text = theme.colors.background === '#ffffff' ? '#000000' : '#ffffff';
    state.colors.border = theme.colors.background === '#ffffff' ? '#000000' : '#ffffff';
  }
};

// 创建slice
const themeSlice = createSlice({
  name: 'theme',
  initialState: (() => {
    const state = { ...initialState };
    const activeTheme = getActiveTheme(state);
    applyThemeToState(state, activeTheme);
    return state;
  })(),
  reducers: {
    // 设置主题模式
    setThemeMode: (state, action: PayloadAction<ThemeMode>) => {
      state.mode = action.payload;
      localStorage.setItem('theme-mode', action.payload);
      
      // 添加到历史记录
      if (!state.themeHistory.includes(action.payload)) {
        state.themeHistory.unshift(action.payload);
        state.themeHistory = state.themeHistory.slice(0, 10);
        localStorage.setItem('theme-history', JSON.stringify(state.themeHistory));
      }
      
      // 应用主题
      const activeTheme = getActiveTheme(state);
      applyThemeToState(state, activeTheme);
    },
    
    // 设置当前主题
    setCurrentTheme: (state, action: PayloadAction<string>) => {
      state.currentTheme = action.payload;
      localStorage.setItem('theme-current', action.payload);
      
      // 添加到历史记录
      if (!state.themeHistory.includes(action.payload)) {
        state.themeHistory.unshift(action.payload);
        state.themeHistory = state.themeHistory.slice(0, 10);
        localStorage.setItem('theme-history', JSON.stringify(state.themeHistory));
      }
      
      // 应用主题
      const activeTheme = getActiveTheme(state);
      applyThemeToState(state, activeTheme);
    },
    
    // 更新系统偏好
    updateSystemPreference: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.systemPreference = action.payload;
      
      // 如果当前是系统模式，重新应用主题
      if (state.mode === 'system') {
        const activeTheme = getActiveTheme(state);
        applyThemeToState(state, activeTheme);
      }
    },
    
    // 切换高对比度
    toggleHighContrast: (state) => {
      state.highContrast = !state.highContrast;
      const activeTheme = getActiveTheme(state);
      applyThemeToState(state, activeTheme);
    },
    
    // 切换减少动画
    toggleReducedMotion: (state) => {
      state.reducedMotion = !state.reducedMotion;
      const activeTheme = getActiveTheme(state);
      applyThemeToState(state, activeTheme);
    },
    
    // 创建自定义主题
    createCustomTheme: (state, action: PayloadAction<Omit<CustomTheme, 'id' | 'createdAt' | 'updatedAt'>>) => {
      const theme: CustomTheme = {
        ...action.payload,
        id: `custom-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      state.customThemes.push(theme);
      localStorage.setItem('theme-custom', JSON.stringify(state.customThemes));
    },
    
    // 更新自定义主题
    updateCustomTheme: (state, action: PayloadAction<CustomTheme>) => {
      const index = state.customThemes.findIndex(theme => theme.id === action.payload.id);
      if (index >= 0) {
        state.customThemes[index] = {
          ...action.payload,
          updatedAt: new Date().toISOString(),
        };
        localStorage.setItem('theme-custom', JSON.stringify(state.customThemes));
        
        // 如果当前使用的是这个主题，重新应用
        if (state.currentTheme === action.payload.id) {
          applyThemeToState(state, action.payload);
        }
      }
    },
    
    // 删除自定义主题
    deleteCustomTheme: (state, action: PayloadAction<string>) => {
      const themeId = action.payload;
      state.customThemes = state.customThemes.filter(theme => theme.id !== themeId);
      localStorage.setItem('theme-custom', JSON.stringify(state.customThemes));
      
      // 如果删除的是当前主题，切换到默认主题
      if (state.currentTheme === themeId) {
        state.currentTheme = 'light';
        localStorage.setItem('theme-current', 'light');
        applyThemeToState(state, state.presetThemes.light);
      }
    },
    
    // 复制主题
    duplicateTheme: (state, action: PayloadAction<string>) => {
      const originalTheme = state.customThemes.find(theme => theme.id === action.payload) ||
                          Object.values(state.presetThemes).find(theme => theme.id === action.payload);
      
      if (originalTheme) {
        const duplicatedTheme: CustomTheme = {
          ...originalTheme,
          id: `custom-${Date.now()}`,
          name: `${originalTheme.name} 副本`,
          isDefault: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        state.customThemes.push(duplicatedTheme);
        localStorage.setItem('theme-custom', JSON.stringify(state.customThemes));
      }
    },
    
    // 导入主题
    importThemes: (state, action: PayloadAction<CustomTheme[]>) => {
      const importedThemes = action.payload.map(theme => ({
        ...theme,
        id: `imported-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }));
      
      state.customThemes.push(...importedThemes);
      localStorage.setItem('theme-custom', JSON.stringify(state.customThemes));
    },
    
    // 重置主题设置
    resetThemeSettings: (state) => {
      state.mode = 'system';
      state.currentTheme = 'light';
      state.customThemes = [];
      state.themeHistory = [];
      state.highContrast = false;
      state.reducedMotion = false;
      
      // 清除localStorage
      localStorage.removeItem('theme-mode');
      localStorage.removeItem('theme-current');
      localStorage.removeItem('theme-custom');
      localStorage.removeItem('theme-history');
      
      // 重新应用默认主题
      const activeTheme = getActiveTheme(state);
      applyThemeToState(state, activeTheme);
    },
    
    // 设置导入状态
    setImportingState: (state, action: PayloadAction<boolean>) => {
      state.isImporting = action.payload;
    },
    
    // 设置导出状态
    setExportingState: (state, action: PayloadAction<boolean>) => {
      state.isExporting = action.payload;
    },
    
    // 设置错误
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
  },
});

// 导出actions
export const {
  setThemeMode,
  setCurrentTheme,
  updateSystemPreference,
  toggleHighContrast,
  toggleReducedMotion,
  createCustomTheme,
  updateCustomTheme,
  deleteCustomTheme,
  duplicateTheme,
  importThemes,
  resetThemeSettings,
  setImportingState,
  setExportingState,
  setError,
  clearError,
} = themeSlice.actions;

// 选择器
export const selectTheme = (state: { theme: ThemeState }) => state.theme;
export const selectThemeMode = (state: { theme: ThemeState }) => state.theme.mode;
export const selectCurrentTheme = (state: { theme: ThemeState }) => state.theme.currentTheme;
export const selectColors = (state: { theme: ThemeState }) => state.theme.colors;
export const selectFonts = (state: { theme: ThemeState }) => state.theme.fonts;
export const selectSpacing = (state: { theme: ThemeState }) => state.theme.spacing;
export const selectBorderRadius = (state: { theme: ThemeState }) => state.theme.borderRadius;
export const selectShadows = (state: { theme: ThemeState }) => state.theme.shadows;
export const selectAnimations = (state: { theme: ThemeState }) => state.theme.animations;
export const selectSystemPreference = (state: { theme: ThemeState }) => state.theme.systemPreference;
export const selectHighContrast = (state: { theme: ThemeState }) => state.theme.highContrast;
export const selectReducedMotion = (state: { theme: ThemeState }) => state.theme.reducedMotion;
export const selectCustomThemes = (state: { theme: ThemeState }) => state.theme.customThemes;
export const selectPresetThemes = (state: { theme: ThemeState }) => state.theme.presetThemes;
export const selectThemeHistory = (state: { theme: ThemeState }) => state.theme.themeHistory;
export const selectIsImporting = (state: { theme: ThemeState }) => state.theme.isImporting;
export const selectIsExporting = (state: { theme: ThemeState }) => state.theme.isExporting;
export const selectThemeError = (state: { theme: ThemeState }) => state.theme.error;

// 复合选择器
export const selectActiveTheme = (state: { theme: ThemeState }) => getActiveTheme(state.theme);
export const selectAllThemes = (state: { theme: ThemeState }) => [
  ...Object.values(state.theme.presetThemes),
  ...state.theme.customThemes,
];
export const selectIsDarkMode = (state: { theme: ThemeState }) => {
  const theme = getActiveTheme(state.theme);
  return theme.id === 'dark' || theme.colors.background === '#121212';
};

// 导出reducer
export default themeSlice.reducer;