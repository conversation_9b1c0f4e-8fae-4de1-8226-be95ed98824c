version: '3.8'

services:
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: aigc-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=10GB'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
      - '--web.external-url=http://localhost:9090'
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/rules:/etc/prometheus/rules
      - ./monitoring/prometheus/targets:/etc/prometheus/targets
      - prometheus_data:/prometheus
    networks:
      - aigc-network
    restart: unless-stopped
    depends_on:
      - node-exporter
      - cadvisor
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.prometheus.rule=Host(`prometheus.localhost`)"
      - "traefik.http.services.prometheus.loadbalancer.server.port=9090"

  grafana:
    image: grafana/grafana:10.0.3
    container_name: aigc-grafana
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
      - ./monitoring/grafana/plugins:/var/lib/grafana/plugins
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_SECURITY_ALLOW_EMBEDDING=true
      - GF_SECURITY_COOKIE_SECURE=true
      - GF_SECURITY_COOKIE_SAMESITE=strict
      - GF_SERVER_DOMAIN=${GRAFANA_DOMAIN:-localhost}
      - GF_SERVER_ROOT_URL=http://localhost:3001
      - GF_SMTP_ENABLED=${GRAFANA_SMTP_ENABLED:-false}
      - GF_SMTP_HOST=${GRAFANA_SMTP_HOST:-localhost:587}
      - GF_SMTP_USER=${GRAFANA_SMTP_USER:-}
      - GF_SMTP_PASSWORD=${GRAFANA_SMTP_PASSWORD:-}
      - GF_SMTP_FROM_ADDRESS=${GRAFANA_SMTP_FROM:-grafana@localhost}
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource,grafana-piechart-panel
    networks:
      - aigc-network
    restart: unless-stopped
    depends_on:
      - prometheus
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`grafana.localhost`)"
      - "traefik.http.services.grafana.loadbalancer.server.port=3000"

  node-exporter:
    image: prom/node-exporter:v1.6.1
    container_name: aigc-node-exporter
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--path.rootfs=/rootfs'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    networks:
      - aigc-network
    restart: unless-stopped

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: aigc-cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
    networks:
      - aigc-network
    restart: unless-stopped
    privileged: true

  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: aigc-alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    networks:
      - aigc-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.alertmanager.rule=Host(`alertmanager.localhost`)"
      - "traefik.http.services.alertmanager.loadbalancer.server.port=9093"

  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: aigc-elasticsearch
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - aigc-network
    restart: unless-stopped
    mem_limit: 2g

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: aigc-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - KIBANA_SYSTEM_PASSWORD=${KIBANA_PASSWORD:-kibana}
    volumes:
      - ./monitoring/kibana/kibana.yml:/usr/share/kibana/config/kibana.yml
    networks:
      - aigc-network
    restart: unless-stopped
    depends_on:
      - elasticsearch
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.kibana.rule=Host(`kibana.localhost`)"
      - "traefik.http.services.kibana.loadbalancer.server.port=5601"

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: aigc-logstash
    ports:
      - "5044:5044"
      - "5000:5000/tcp"
      - "5000:5000/udp"
      - "9600:9600"
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
      - ./monitoring/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml
    networks:
      - aigc-network
    restart: unless-stopped
    depends_on:
      - elasticsearch
    environment:
      - LS_JAVA_OPTS=-Xmx1g -Xms1g

  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: aigc-filebeat
    user: root
    volumes:
      - ./monitoring/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./backend/logs:/var/log/backend:ro
      - ./nginx/logs:/var/log/nginx:ro
    networks:
      - aigc-network
    restart: unless-stopped
    depends_on:
      - elasticsearch
      - logstash
    command: filebeat -e -strict.perms=false

  redis-exporter:
    image: oliver006/redis_exporter:v1.52.0
    container_name: aigc-redis-exporter
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis:6379
    networks:
      - aigc-network
    restart: unless-stopped
    depends_on:
      - redis

  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.13.2
    container_name: aigc-postgres-exporter
    ports:
      - "9187:9187"
    environment:
      - DATA_SOURCE_NAME=postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD:-password}@postgres:5432/${POSTGRES_DB:-aigc_hub}?sslmode=disable
    networks:
      - aigc-network
    restart: unless-stopped
    depends_on:
      - postgres

  jaeger:
    image: jaegertracing/all-in-one:1.47
    container_name: aigc-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    networks:
      - aigc-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.jaeger.rule=Host(`jaeger.localhost`)"
      - "traefik.http.services.jaeger.loadbalancer.server.port=16686"

  uptimekuma:
    image: louislam/uptime-kuma:1.23.0
    container_name: aigc-uptime-kuma
    ports:
      - "3002:3001"
    volumes:
      - uptime_kuma_data:/app/data
    networks:
      - aigc-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.uptime.rule=Host(`uptime.localhost`)"
      - "traefik.http.services.uptime.loadbalancer.server.port=3001"

networks:
  aigc-network:
    external: true

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  alertmanager_data:
    driver: local
  elasticsearch_data:
    driver: local
  uptime_kuma_data:
    driver: local