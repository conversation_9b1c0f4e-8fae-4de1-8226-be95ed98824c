const express = require('express');

async function testRoutes() {
  console.log('🔍 Testing Route Loading...\n');

  try {
    // Test importing auth routes
    console.log('1. Testing auth routes import...');
    const authRoutes = require('./dist/routes/auth.routes.js');
    console.log('   ✅ Auth routes imported:', typeof authRoutes.default);

    // Test importing auth controller
    console.log('2. Testing auth controller import...');
    const AuthController = require('./dist/controllers/auth.controller.js').AuthController;
    console.log('   ✅ Auth controller imported:', typeof AuthController);

    // Test creating controller instance
    console.log('3. Testing controller instantiation...');
    const authController = new AuthController();
    console.log('   ✅ Auth controller instantiated');

    // Test creating express app and mounting routes
    console.log('4. Testing route mounting...');
    const app = express();
    const apiRouter = express.Router();
    
    apiRouter.use('/auth', authRoutes.default);
    app.use('/api', apiRouter);
    
    console.log('   ✅ Routes mounted successfully');

    // Test route stack
    console.log('5. Checking route stack...');
    const authRouter = authRoutes.default;
    console.log('   Auth router stack length:', authRouter.stack.length);
    
    authRouter.stack.forEach((layer, index) => {
      console.log(`   Route ${index + 1}: ${layer.route?.path || 'middleware'} (${layer.route?.methods ? Object.keys(layer.route.methods).join(',') : 'N/A'})`);
    });

    // Test API router stack
    console.log('6. Checking API router stack...');
    console.log('   API router stack length:', apiRouter.stack.length);
    
    apiRouter.stack.forEach((layer, index) => {
      console.log(`   API Route ${index + 1}: ${layer.regexp.source}`);
    });

    // Test main app stack
    console.log('7. Checking main app stack...');
    console.log('   Main app stack length:', app._router.stack.length);
    
    app._router.stack.forEach((layer, index) => {
      console.log(`   App Route ${index + 1}: ${layer.regexp.source}`);
    });

    console.log('\n✅ All route tests passed!');
    
    // Start a test server
    console.log('\n8. Starting test server...');
    const server = app.listen(3001, () => {
      console.log('   ✅ Test server running on port 3001');
      
      // Test the route
      const http = require('http');
      const options = {
        hostname: 'localhost',
        port: 3001,
        path: '/api/auth/check-email?email=<EMAIL>',
        method: 'GET'
      };

      const req = http.request(options, (res) => {
        console.log(`   📡 Test request status: ${res.statusCode}`);
        
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          console.log(`   📄 Response: ${data}`);
          server.close();
          
          if (res.statusCode === 404) {
            console.log('\n❌ Route still returns 404 - there may be an issue with the controller method binding');
          } else {
            console.log('\n✅ Route is working!');
          }
        });
      });

      req.on('error', (e) => {
        console.error(`   ❌ Request error: ${e.message}`);
        server.close();
      });

      req.end();
    });

  } catch (error) {
    console.error('❌ Error during route testing:', error);
    console.error('Stack trace:', error.stack);
  }
}

testRoutes();
