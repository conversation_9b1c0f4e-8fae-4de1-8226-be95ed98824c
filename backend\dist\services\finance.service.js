"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinanceService = void 0;
const connection_1 = require("../database/connection");
const types_1 = require("../types");
const errors_1 = require("../utils/errors");
const logger_1 = require("../utils/logger");
class FinanceService {
    constructor() {
        this.db = (0, connection_1.getDb)();
    }
    async createWithdrawalRequest(userId, amount, paypalEmail) {
        const client = await this.db.connect();
        try {
            await client.query('BEGIN');
            const balanceQuery = `
        SELECT COALESCE(SUM(CASE WHEN status = 'AVAILABLE' THEN amount ELSE 0 END), 0) as available_balance
        FROM ledger_entries
        WHERE user_id = $1 AND entry_type = 'SALE_CREDIT'
      `;
            const balanceResult = await client.query(balanceQuery, [userId]);
            const availableBalance = parseFloat(balanceResult.rows[0].available_balance);
            if (availableBalance < amount) {
                throw new errors_1.InsufficientBalanceError(amount, availableBalance);
            }
            const withdrawalQuery = `
        INSERT INTO withdrawal_requests (user_id, amount, paypal_email, status)
        VALUES ($1, $2, $3, $4)
        RETURNING id, user_id, amount, paypal_email, status, created_at
      `;
            const withdrawalResult = await client.query(withdrawalQuery, [
                userId,
                amount,
                paypalEmail,
                types_1.WithdrawalStatus.PENDING
            ]);
            const withdrawal = withdrawalResult.rows[0];
            await client.query('COMMIT');
            (0, logger_1.logBusinessOperation)('WITHDRAWAL_REQUEST_CREATE', userId, {
                withdrawalId: withdrawal.id,
                amount,
                paypalEmail
            });
            return {
                id: withdrawal.id,
                userId: withdrawal.user_id,
                amount: withdrawal.amount,
                paypalEmail: withdrawal.paypal_email,
                status: withdrawal.status,
                createdAt: withdrawal.created_at,
            };
        }
        catch (error) {
            await client.query('ROLLBACK');
            logger_1.logger.error('Failed to create withdrawal request:', error);
            throw error;
        }
        finally {
            client.release();
        }
    }
    async getUserWithdrawalRequests(userId, query) {
        try {
            const { page = 1, limit = 20 } = query;
            const offset = (page - 1) * limit;
            const withdrawalsQuery = `
        SELECT id, user_id, amount, paypal_email, status, admin_notes, 
               rejection_reason, created_at, processed_at
        FROM withdrawal_requests
        WHERE user_id = $1
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3
      `;
            const countQuery = `
        SELECT COUNT(*) as total
        FROM withdrawal_requests
        WHERE user_id = $1
      `;
            const [withdrawalsResult, countResult] = await Promise.all([
                this.db.query(withdrawalsQuery, [userId, limit, offset]),
                this.db.query(countQuery, [userId])
            ]);
            const withdrawals = withdrawalsResult.rows;
            const total = parseInt(countResult.rows[0].total);
            const totalPages = Math.ceil(total / limit);
            return {
                withdrawals,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get user withdrawal requests:', error);
            throw error;
        }
    }
    async getWithdrawalById(withdrawalId) {
        try {
            const query = `
        SELECT wr.*, u.display_name as user_name, u.email as user_email
        FROM withdrawal_requests wr
        JOIN users u ON wr.user_id = u.id
        WHERE wr.id = $1
      `;
            const result = await this.db.query(query, [withdrawalId]);
            if (result.rows.length === 0) {
                throw new errors_1.NotFoundError('Withdrawal not found');
            }
            const withdrawal = result.rows[0];
            return {
                id: withdrawal.id,
                userId: withdrawal.user_id,
                amount: withdrawal.amount,
                paypalEmail: withdrawal.paypal_email,
                status: withdrawal.status,
                adminNotes: withdrawal.admin_notes,
                rejectionReason: withdrawal.rejection_reason,
                createdAt: withdrawal.created_at,
                processedAt: withdrawal.processed_at,
                processedBy: withdrawal.processed_by,
                paypalPayoutId: withdrawal.paypal_payout_id,
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get withdrawal by ID:', error);
            throw error;
        }
    }
    async getUserEarnings(userId) {
        try {
            const query = `
        SELECT 
          COALESCE(SUM(CASE WHEN le.status = 'AVAILABLE' THEN le.amount ELSE 0 END), 0) as available_balance,
          COALESCE(SUM(CASE WHEN le.status = 'PENDING' THEN le.amount ELSE 0 END), 0) as pending_balance,
          COALESCE(SUM(CASE WHEN le.status = 'WITHDRAWN' THEN le.amount ELSE 0 END), 0) as withdrawn_balance,
          COALESCE(SUM(CASE WHEN le.entry_type = 'SALE_CREDIT' THEN le.amount ELSE 0 END), 0) as total_earnings,
          COUNT(DISTINCT t.id) as total_sales,
          COUNT(DISTINCT a.id) as total_assets
        FROM users u
        LEFT JOIN ledger_entries le ON u.id = le.user_id
        LEFT JOIN assets a ON u.id = a.creator_id
        LEFT JOIN transactions t ON a.id = t.asset_id AND t.status = 'COMPLETED'
        WHERE u.id = $1
        GROUP BY u.id
      `;
            const result = await this.db.query(query, [userId]);
            if (result.rows.length === 0) {
                return {
                    totalEarnings: 0,
                    availableBalance: 0,
                    pendingBalance: 0,
                    withdrawnBalance: 0,
                    totalSales: 0,
                    totalAssets: 0,
                };
            }
            const earnings = result.rows[0];
            return {
                totalEarnings: parseFloat(earnings.total_earnings),
                availableBalance: parseFloat(earnings.available_balance),
                pendingBalance: parseFloat(earnings.pending_balance),
                withdrawnBalance: parseFloat(earnings.withdrawn_balance),
                totalSales: parseInt(earnings.total_sales),
                totalAssets: parseInt(earnings.total_assets),
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get user earnings:', error);
            throw error;
        }
    }
    async getUserBalance(userId) {
        try {
            const query = `
        SELECT 
          u.points_balance,
          COALESCE(SUM(CASE WHEN le.status = 'AVAILABLE' THEN le.amount ELSE 0 END), 0) as available_balance,
          COALESCE(SUM(CASE WHEN le.status = 'PENDING' THEN le.amount ELSE 0 END), 0) as pending_balance
        FROM users u
        LEFT JOIN ledger_entries le ON u.id = le.user_id AND le.entry_type = 'SALE_CREDIT'
        WHERE u.id = $1
        GROUP BY u.id, u.points_balance
      `;
            const result = await this.db.query(query, [userId]);
            if (result.rows.length === 0) {
                return {
                    pointsBalance: 0,
                    availableBalance: 0,
                    pendingBalance: 0,
                };
            }
            const balance = result.rows[0];
            return {
                pointsBalance: balance.points_balance,
                availableBalance: parseFloat(balance.available_balance),
                pendingBalance: parseFloat(balance.pending_balance),
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get user balance:', error);
            throw error;
        }
    }
    async getAllWithdrawalRequests(query) {
        try {
            const { page = 1, limit = 20, status } = query;
            const offset = (page - 1) * limit;
            const conditions = [];
            const params = [];
            let paramIndex = 1;
            if (status) {
                conditions.push(`wr.status = $${paramIndex}`);
                params.push(status);
                paramIndex++;
            }
            const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
            const withdrawalsQuery = `
        SELECT wr.*, u.display_name as user_name, u.email as user_email
        FROM withdrawal_requests wr
        JOIN users u ON wr.user_id = u.id
        ${whereClause}
        ORDER BY wr.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;
            const countQuery = `
        SELECT COUNT(*) as total
        FROM withdrawal_requests wr
        ${whereClause}
      `;
            params.push(limit, offset);
            const [withdrawalsResult, countResult] = await Promise.all([
                this.db.query(withdrawalsQuery, params),
                this.db.query(countQuery, params.slice(0, -2))
            ]);
            const withdrawals = withdrawalsResult.rows;
            const total = parseInt(countResult.rows[0].total);
            const totalPages = Math.ceil(total / limit);
            return {
                withdrawals,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get all withdrawal requests:', error);
            throw error;
        }
    }
    async approveWithdrawal(withdrawalId, adminId, adminNotes) {
        const client = await this.db.connect();
        try {
            await client.query('BEGIN');
            const updateQuery = `
        UPDATE withdrawal_requests 
        SET status = $1, processed_at = CURRENT_TIMESTAMP, processed_by = $2, admin_notes = $3
        WHERE id = $4 AND status = $5
        RETURNING user_id, amount
      `;
            const result = await client.query(updateQuery, [
                types_1.WithdrawalStatus.APPROVED,
                adminId,
                adminNotes,
                withdrawalId,
                types_1.WithdrawalStatus.PENDING
            ]);
            if (result.rows.length === 0) {
                throw new errors_1.NotFoundError('Withdrawal not found');
            }
            const { user_id, amount } = result.rows[0];
            await client.query(`
        UPDATE ledger_entries 
        SET status = 'WITHDRAWN', withdrawn_at = CURRENT_TIMESTAMP
        WHERE user_id = $1 AND status = 'AVAILABLE' AND entry_type = 'SALE_CREDIT'
        AND amount <= $2
      `, [user_id, amount]);
            await client.query('COMMIT');
            (0, logger_1.logBusinessOperation)('WITHDRAWAL_APPROVE', adminId, { withdrawalId, userId: user_id, amount });
        }
        catch (error) {
            await client.query('ROLLBACK');
            logger_1.logger.error('Failed to approve withdrawal:', error);
            throw error;
        }
        finally {
            client.release();
        }
    }
    async rejectWithdrawal(withdrawalId, adminId, rejectionReason) {
        try {
            const query = `
        UPDATE withdrawal_requests 
        SET status = $1, processed_at = CURRENT_TIMESTAMP, processed_by = $2, rejection_reason = $3
        WHERE id = $4 AND status = $5
      `;
            const result = await this.db.query(query, [
                types_1.WithdrawalStatus.REJECTED,
                adminId,
                rejectionReason,
                withdrawalId,
                types_1.WithdrawalStatus.PENDING
            ]);
            if (result.rowCount === 0) {
                throw new errors_1.NotFoundError('Withdrawal not found');
            }
            (0, logger_1.logBusinessOperation)('WITHDRAWAL_REJECT', adminId, { withdrawalId, rejectionReason });
        }
        catch (error) {
            logger_1.logger.error('Failed to reject withdrawal:', error);
            throw error;
        }
    }
    async getPlatformStats() {
        try {
            const query = `
        SELECT 
          (SELECT COUNT(*) FROM users) as total_users,
          (SELECT COUNT(*) FROM users WHERE user_role != 'ADMIN') as total_creators,
          (SELECT COUNT(*) FROM assets) as total_assets,
          (SELECT COUNT(*) FROM assets WHERE status = 'PUBLISHED') as published_assets,
          (SELECT COUNT(*) FROM transactions WHERE status = 'COMPLETED') as total_transactions,
          (SELECT COALESCE(SUM(amount_usd), 0) FROM transactions WHERE status = 'COMPLETED' AND currency = 'USD') as total_usd_volume,
          (SELECT COALESCE(SUM(amount_points), 0) FROM transactions WHERE status = 'COMPLETED' AND currency = 'POINTS') as total_points_volume,
          (SELECT COALESCE(SUM(amount), 0) FROM ledger_entries WHERE entry_type = 'PLATFORM_FEE') as total_platform_fees
      `;
            const result = await this.db.query(query);
            const stats = result.rows[0];
            return {
                totalUsers: parseInt(stats.total_users),
                totalCreators: parseInt(stats.total_creators),
                totalAssets: parseInt(stats.total_assets),
                publishedAssets: parseInt(stats.published_assets),
                totalTransactions: parseInt(stats.total_transactions),
                totalUsdVolume: parseFloat(stats.total_usd_volume),
                totalPointsVolume: parseInt(stats.total_points_volume),
                totalPlatformFees: parseFloat(stats.total_platform_fees),
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get platform stats:', error);
            throw error;
        }
    }
}
exports.FinanceService = FinanceService;
exports.default = FinanceService;
//# sourceMappingURL=finance.service.js.map