groups:
  - name: system.rules
    rules:
      - alert: HighCpuUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes on {{ $labels.instance }}"

      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) / node_filesystem_size_bytes{fstype!="tmpfs"} * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High disk usage detected"
          description: "Disk usage is above 85% for more than 5 minutes on {{ $labels.instance }} filesystem {{ $labels.mountpoint }}"

      - alert: HighDiskUsageCritical
        expr: (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) / node_filesystem_size_bytes{fstype!="tmpfs"} * 100 > 95
        for: 2m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Critical disk usage detected"
          description: "Disk usage is above 95% for more than 2 minutes on {{ $labels.instance }} filesystem {{ $labels.mountpoint }}"

      - alert: HighLoadAverage
        expr: node_load1 / count(count(node_cpu_seconds_total) by (cpu)) by (instance) > 0.8
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High load average detected"
          description: "Load average is above 0.8 for more than 5 minutes on {{ $labels.instance }}"

  - name: container.rules
    rules:
      - alert: ContainerKilled
        expr: time() - container_last_seen > 60
        for: 5m
        labels:
          severity: warning
          service: container
        annotations:
          summary: "Container killed"
          description: "Container {{ $labels.name }} has been killed"

      - alert: ContainerCpuUsage
        expr: (sum by (container_label_com_docker_compose_service) (rate(container_cpu_usage_seconds_total[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: container
        annotations:
          summary: "Container high CPU usage"
          description: "Container {{ $labels.container_label_com_docker_compose_service }} CPU usage is above 80%"

      - alert: ContainerMemoryUsage
        expr: (sum by (container_label_com_docker_compose_service) (container_memory_usage_bytes) / sum by (container_label_com_docker_compose_service) (container_spec_memory_limit_bytes) * 100) > 85
        for: 5m
        labels:
          severity: warning
          service: container
        annotations:
          summary: "Container high memory usage"
          description: "Container {{ $labels.container_label_com_docker_compose_service }} memory usage is above 85%"

      - alert: ContainerVolumeUsage
        expr: (1 - (container_fs_usage_bytes / container_fs_limit_bytes)) * 100 < 10
        for: 5m
        labels:
          severity: warning
          service: container
        annotations:
          summary: "Container volume usage"
          description: "Container {{ $labels.container_label_com_docker_compose_service }} volume usage is above 90%"

  - name: application.rules
    rules:
      - alert: ApplicationDown
        expr: up == 0
        for: 5m
        labels:
          severity: critical
          service: application
        annotations:
          summary: "Application is down"
          description: "Application {{ $labels.job }} on {{ $labels.instance }} is down for more than 5 minutes"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le)) > 1
        for: 5m
        labels:
          severity: warning
          service: application
        annotations:
          summary: "High response time"
          description: "95th percentile response time is above 1 second for more than 5 minutes"

      - alert: HighErrorRate
        expr: sum(rate(http_requests_total{status=~"5.."}[5m])) by (service) / sum(rate(http_requests_total[5m])) by (service) > 0.05
        for: 5m
        labels:
          severity: warning
          service: application
        annotations:
          summary: "High error rate"
          description: "Error rate is above 5% for more than 5 minutes on service {{ $labels.service }}"

      - alert: HighErrorRateCritical
        expr: sum(rate(http_requests_total{status=~"5.."}[5m])) by (service) / sum(rate(http_requests_total[5m])) by (service) > 0.1
        for: 2m
        labels:
          severity: critical
          service: application
        annotations:
          summary: "Critical error rate"
          description: "Error rate is above 10% for more than 2 minutes on service {{ $labels.service }}"

      - alert: LowThroughput
        expr: sum(rate(http_requests_total[5m])) by (service) < 1
        for: 10m
        labels:
          severity: warning
          service: application
        annotations:
          summary: "Low throughput"
          description: "Request throughput is below 1 RPS for more than 10 minutes on service {{ $labels.service }}"

  - name: database.rules
    rules:
      - alert: PostgresDown
        expr: pg_up == 0
        for: 5m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database is down for more than 5 minutes"

      - alert: PostgresConnectionsHigh
        expr: sum(pg_stat_activity_count) by (instance) > 80
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "High PostgreSQL connections"
          description: "PostgreSQL connections are above 80 for more than 5 minutes on {{ $labels.instance }}"

      - alert: PostgresSlowQueries
        expr: pg_stat_activity_max_tx_duration > 300
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "PostgreSQL slow queries"
          description: "PostgreSQL has queries running for more than 5 minutes"

      - alert: PostgresReplicationLag
        expr: pg_replication_lag > 30
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "PostgreSQL replication lag"
          description: "PostgreSQL replication lag is above 30 seconds for more than 5 minutes"

      - alert: RedisDown
        expr: redis_up == 0
        for: 5m
        labels:
          severity: critical
          service: database
        annotations:
          summary: "Redis is down"
          description: "Redis is down for more than 5 minutes"

      - alert: RedisMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "High Redis memory usage"
          description: "Redis memory usage is above 85% for more than 5 minutes"

      - alert: RedisConnectionsHigh
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          service: database
        annotations:
          summary: "High Redis connections"
          description: "Redis connections are above 100 for more than 5 minutes"

  - name: network.rules
    rules:
      - alert: HighNetworkReceiveErrors
        expr: rate(node_network_receive_errs_total[5m]) > 0.01
        for: 5m
        labels:
          severity: warning
          service: network
        annotations:
          summary: "High network receive errors"
          description: "Network receive errors are above 1% for more than 5 minutes on {{ $labels.instance }}"

      - alert: HighNetworkTransmitErrors
        expr: rate(node_network_transmit_errs_total[5m]) > 0.01
        for: 5m
        labels:
          severity: warning
          service: network
        annotations:
          summary: "High network transmit errors"
          description: "Network transmit errors are above 1% for more than 5 minutes on {{ $labels.instance }}"

      - alert: HighNetworkUtilization
        expr: rate(node_network_receive_bytes_total[5m]) + rate(node_network_transmit_bytes_total[5m]) > 1000000
        for: 5m
        labels:
          severity: warning
          service: network
        annotations:
          summary: "High network utilization"
          description: "Network utilization is above 1MB/s for more than 5 minutes on {{ $labels.instance }}"

  - name: security.rules
    rules:
      - alert: TooManyFailedLogins
        expr: sum(rate(auth_failed_attempts_total[5m])) by (instance) > 5
        for: 5m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "Too many failed login attempts"
          description: "More than 5 failed login attempts per minute detected on {{ $labels.instance }}"

      - alert: UnauthorizedAccess
        expr: sum(rate(http_requests_total{status="401"}[5m])) by (instance) > 10
        for: 5m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "High unauthorized access attempts"
          description: "More than 10 unauthorized access attempts per minute detected on {{ $labels.instance }}"

      - alert: SuspiciousActivity
        expr: sum(rate(http_requests_total{status="403"}[5m])) by (instance) > 5
        for: 5m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "Suspicious activity detected"
          description: "More than 5 forbidden requests per minute detected on {{ $labels.instance }}"

  - name: business.rules
    rules:
      - alert: LowUserRegistrations
        expr: sum(rate(user_registrations_total[1h])) < 1
        for: 1h
        labels:
          severity: warning
          service: business
        annotations:
          summary: "Low user registrations"
          description: "User registrations are below 1 per hour for more than 1 hour"

      - alert: HighUserChurnRate
        expr: (sum(rate(user_deactivations_total[24h])) / sum(rate(user_registrations_total[24h]))) > 0.1
        for: 1h
        labels:
          severity: warning
          service: business
        annotations:
          summary: "High user churn rate"
          description: "User churn rate is above 10% for more than 1 hour"

      - alert: APIQuotaExceeded
        expr: sum(rate(api_requests_total[5m])) by (user) > 100
        for: 5m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "API quota exceeded"
          description: "User {{ $labels.user }} has exceeded API quota (100 requests/5min)"

  - name: backup.rules
    rules:
      - alert: BackupFailed
        expr: time() - backup_last_success_timestamp > 86400
        for: 5m
        labels:
          severity: critical
          service: backup
        annotations:
          summary: "Backup failed"
          description: "Backup has not succeeded for more than 24 hours"

      - alert: BackupOld
        expr: time() - backup_last_success_timestamp > 43200
        for: 5m
        labels:
          severity: warning
          service: backup
        annotations:
          summary: "Backup is old"
          description: "Backup is older than 12 hours"

  - name: certificate.rules
    rules:
      - alert: CertificateExpiringSoon
        expr: (ssl_certificate_expiry_timestamp - time()) / 86400 < 30
        for: 5m
        labels:
          severity: warning
          service: certificate
        annotations:
          summary: "Certificate expiring soon"
          description: "SSL certificate for {{ $labels.instance }} expires in less than 30 days"

      - alert: CertificateExpiringSoonCritical
        expr: (ssl_certificate_expiry_timestamp - time()) / 86400 < 7
        for: 5m
        labels:
          severity: critical
          service: certificate
        annotations:
          summary: "Certificate expiring very soon"
          description: "SSL certificate for {{ $labels.instance }} expires in less than 7 days"