#!/bin/bash

# ===========================================
# AIGC Service Hub MVP 1.0 - 一键部署脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AIGC Service Hub MVP 1.0 - 一键部署脚本

使用方法: $0 [OPTIONS] [ENVIRONMENT]

ENVIRONMENT:
    dev         部署开发环境
    test        部署测试环境
    prod        部署生产环境 (默认)

OPTIONS:
    -h, --help      显示帮助信息
    -v, --verbose   详细输出
    -f, --force     强制重新构建
    -c, --clean     清理现有容器和数据
    -b, --backup    部署前创建备份
    -s, --skip-deps 跳过依赖检查
    -p, --pull      拉取最新镜像
    -r, --restart   重启现有服务
    -d, --daemon    后台运行
    --no-cache      不使用缓存构建
    --health-check  执行健康检查
    --migrate       运行数据库迁移

示例:
    $0 prod                    # 部署生产环境
    $0 dev -f                  # 强制重新构建开发环境
    $0 test -c -b              # 清理并备份后部署测试环境
    $0 prod --health-check     # 部署生产环境并执行健康检查

EOF
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查Docker服务状态
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    directories=(
        "database/backups"
        "database/postgres-data"
        "database/redis-data"
        "backend/logs"
        "backend/uploads"
        "nginx/logs"
        "nginx/ssl"
        "monitoring/data"
        "scripts/logs"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    log_success "目录创建完成"
}

# 环境配置检查
check_environment() {
    local env=$1
    log_info "检查环境配置: $env"
    
    # 检查环境配置文件
    if [ ! -f ".env.$env" ]; then
        log_error "环境配置文件 .env.$env 不存在"
        exit 1
    fi
    
    # 复制环境配置文件
    cp ".env.$env" ".env"
    log_info "使用环境配置: .env.$env"
    
    # 检查必要的环境变量
    source ".env"
    
    required_vars=(
        "POSTGRES_PASSWORD"
        "JWT_SECRET"
        "AWS_ACCESS_KEY_ID"
        "AWS_SECRET_ACCESS_KEY"
        "PAYPAL_CLIENT_ID"
        "PAYPAL_CLIENT_SECRET"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ] || [[ "${!var}" =~ ^(CHANGE_THIS|your_|dev-|test-) ]]; then
            log_warning "环境变量 $var 未正确设置"
        fi
    done
    
    log_success "环境配置检查完成"
}

# 拉取最新镜像
pull_images() {
    log_info "拉取最新镜像..."
    
    if [ "$PULL_IMAGES" = true ]; then
        docker-compose -f "$compose_file" pull
        log_success "镜像拉取完成"
    else
        log_info "跳过镜像拉取"
    fi
}

# 创建备份
create_backup() {
    if [ "$CREATE_BACKUP" = true ]; then
        log_info "创建部署前备份..."
        
        if [ -f "scripts/backup.sh" ]; then
            ./scripts/backup.sh
            log_success "备份创建完成"
        else
            log_warning "备份脚本不存在，跳过备份"
        fi
    fi
}

# 清理现有容器和数据
clean_environment() {
    if [ "$CLEAN_ENVIRONMENT" = true ]; then
        log_warning "清理现有容器和数据..."
        
        read -p "确定要清理现有数据吗？此操作不可逆 (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose -f "$compose_file" down -v --remove-orphans
            docker system prune -f
            log_success "环境清理完成"
        else
            log_info "跳过环境清理"
        fi
    fi
}

# 构建和部署服务
deploy_services() {
    log_info "开始部署服务..."
    
    # 构建选项
    build_opts=""
    if [ "$FORCE_REBUILD" = true ]; then
        build_opts="$build_opts --build"
    fi
    
    if [ "$NO_CACHE" = true ]; then
        build_opts="$build_opts --no-cache"
    fi
    
    # 部署选项
    deploy_opts=""
    if [ "$DAEMON_MODE" = true ]; then
        deploy_opts="$deploy_opts -d"
    fi
    
    # 执行部署
    if [ "$RESTART_SERVICES" = true ]; then
        docker-compose -f "$compose_file" restart
        log_success "服务重启完成"
    else
        docker-compose -f "$compose_file" up $build_opts $deploy_opts
        log_success "服务部署完成"
    fi
}

# 数据库迁移
run_migrations() {
    if [ "$RUN_MIGRATIONS" = true ]; then
        log_info "运行数据库迁移..."
        
        # 等待数据库服务启动
        sleep 10
        
        # 运行迁移
        docker-compose -f "$compose_file" exec -T backend npm run db:migrate
        log_success "数据库迁移完成"
    fi
}

# 健康检查
health_check() {
    if [ "$HEALTH_CHECK" = true ]; then
        log_info "执行健康检查..."
        
        # 等待服务启动
        sleep 30
        
        # 检查服务状态
        services=(
            "postgres:5432"
            "redis:6379"
            "backend:3000"
            "frontend:80"
        )
        
        for service in "${services[@]}"; do
            container=$(echo "$service" | cut -d':' -f1)
            port=$(echo "$service" | cut -d':' -f2)
            
            if docker-compose -f "$compose_file" exec -T "$container" nc -z localhost "$port" 2>/dev/null; then
                log_success "服务 $container 健康检查通过"
            else
                log_error "服务 $container 健康检查失败"
            fi
        done
        
        # 检查API端点
        if curl -f -s "http://localhost:3000/api/v1/health" > /dev/null; then
            log_success "后端API健康检查通过"
        else
            log_error "后端API健康检查失败"
        fi
        
        # 检查前端
        if curl -f -s "http://localhost:3001" > /dev/null; then
            log_success "前端应用健康检查通过"
        else
            log_error "前端应用健康检查失败"
        fi
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    
    echo
    echo "=================== 部署信息 ==================="
    echo "环境: $ENVIRONMENT"
    echo "时间: $(date)"
    echo "Docker Compose 文件: $compose_file"
    echo
    echo "服务访问地址:"
    echo "  前端应用: http://localhost:3001"
    echo "  后端API: http://localhost:3000"
    echo "  API文档: http://localhost:3000/api/docs"
    echo "  数据库管理: http://localhost:8080"
    echo "  Redis管理: http://localhost:8081"
    echo "  邮件测试: http://localhost:8025"
    echo
    echo "常用命令:"
    echo "  查看日志: docker-compose -f $compose_file logs -f"
    echo "  查看状态: docker-compose -f $compose_file ps"
    echo "  停止服务: docker-compose -f $compose_file down"
    echo "  重启服务: docker-compose -f $compose_file restart"
    echo "=============================================="
}

# 主函数
main() {
    # 默认参数
    ENVIRONMENT="prod"
    VERBOSE=false
    FORCE_REBUILD=false
    CLEAN_ENVIRONMENT=false
    CREATE_BACKUP=false
    SKIP_DEPS=false
    PULL_IMAGES=false
    RESTART_SERVICES=false
    DAEMON_MODE=true
    NO_CACHE=false
    HEALTH_CHECK=false
    RUN_MIGRATIONS=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--force)
                FORCE_REBUILD=true
                shift
                ;;
            -c|--clean)
                CLEAN_ENVIRONMENT=true
                shift
                ;;
            -b|--backup)
                CREATE_BACKUP=true
                shift
                ;;
            -s|--skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            -p|--pull)
                PULL_IMAGES=true
                shift
                ;;
            -r|--restart)
                RESTART_SERVICES=true
                shift
                ;;
            -d|--daemon)
                DAEMON_MODE=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --health-check)
                HEALTH_CHECK=true
                shift
                ;;
            --migrate)
                RUN_MIGRATIONS=true
                shift
                ;;
            dev|test|prod)
                ENVIRONMENT=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置详细输出
    if [ "$VERBOSE" = true ]; then
        set -x
    fi
    
    # 确定Docker Compose文件
    case $ENVIRONMENT in
        dev)
            compose_file="docker-compose.dev.yml"
            ;;
        test)
            compose_file="docker-compose.test.yml"
            ;;
        prod)
            compose_file="docker-compose.prod.yml"
            ;;
        *)
            compose_file="docker-compose.yml"
            ;;
    esac
    
    # 检查Docker Compose文件是否存在
    if [ ! -f "$compose_file" ]; then
        log_error "Docker Compose文件 $compose_file 不存在"
        exit 1
    fi
    
    log_info "开始部署 AIGC Service Hub MVP 1.0"
    log_info "环境: $ENVIRONMENT"
    log_info "配置文件: $compose_file"
    
    # 执行部署步骤
    if [ "$SKIP_DEPS" = false ]; then
        check_dependencies
    fi
    
    create_directories
    check_environment "$ENVIRONMENT"
    create_backup
    clean_environment
    pull_images
    deploy_services
    run_migrations
    health_check
    show_deployment_info
    
    log_success "部署完成！"
}

# 执行主函数
main "$@"