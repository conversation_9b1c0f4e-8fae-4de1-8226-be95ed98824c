import { User, UserRole, BalanceResponse, UserEarningsStats, PaginationQuery } from '../types';
export declare class UserService {
    private db;
    constructor();
    getUserById(id: number): Promise<User>;
    getUserBalance(userId: number): Promise<BalanceResponse>;
    getUserEarningsStats(userId: number): Promise<UserEarningsStats>;
    updateUserProfile(userId: number, updates: Partial<User>): Promise<User>;
    updatePointsBalance(userId: number, amount: number): Promise<void>;
    checkUserPoints(userId: number, requiredPoints: number): Promise<boolean>;
    getUserPurchaseHistory(userId: number, query: PaginationQuery): Promise<{
        purchases: {
            transactionId: any;
            currency: any;
            amountUsd: any;
            amountPoints: any;
            purchasedAt: any;
            asset: {
                id: any;
                title: any;
                assetType: any;
                coverImageUrl: any;
                creatorName: any;
            };
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getUserSalesHistory(userId: number, query: PaginationQuery): Promise<{
        sales: {
            transactionId: any;
            currency: any;
            amountUsd: any;
            amountPoints: any;
            soldAt: any;
            creatorEarnings: any;
            asset: {
                id: any;
                title: any;
                assetType: any;
            };
            buyerName: any;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    verifyAssetOwnership(userId: number, assetId: number): Promise<boolean>;
    verifyAssetPurchase(userId: number, assetId: number): Promise<boolean>;
    getUserList(query: PaginationQuery & {
        role?: UserRole;
        isActive?: boolean;
    }): Promise<{
        users: {
            id: any;
            email: any;
            displayName: any;
            userRole: any;
            pointsBalance: any;
            oauthProvider: any;
            isActive: any;
            createdAt: any;
            updatedAt: any;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    updateUserStatus(userId: number, isActive: boolean, adminId: number): Promise<void>;
    deleteUser(userId: number, adminId: number): Promise<void>;
}
export default UserService;
//# sourceMappingURL=user.service.d.ts.map