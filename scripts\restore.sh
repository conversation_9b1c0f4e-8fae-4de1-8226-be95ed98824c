#!/bin/bash

# ===========================================
# AIGC Service Hub MVP 1.0 - 恢复脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AIGC Service Hub MVP 1.0 - 恢复脚本

使用方法: $0 [OPTIONS] BACKUP_PATH

参数:
    BACKUP_PATH         备份文件或目录路径

OPTIONS:
    -h, --help          显示帮助信息
    -v, --verbose       详细输出
    -f, --force         强制恢复 (覆盖现有数据)
    -d, --database      仅恢复数据库
    -u, --uploads       仅恢复上传文件
    -c, --configs       仅恢复配置文件
    -l, --logs          仅恢复日志文件
    -t, --test          测试模式 (不实际恢复)
    --decrypt           解密备份文件
    --download          从远程下载备份
    --verify            验证备份完整性
    --no-stop           不停止现有服务

示例:
    $0 /path/to/backup                  # 完整恢复
    $0 backup.tar.gz                    # 从压缩包恢复
    $0 -d /path/to/backup               # 仅恢复数据库
    $0 -f --decrypt backup.tar.gz.enc  # 解密并强制恢复
    $0 -t /path/to/backup               # 测试恢复

EOF
}

# 加载配置
load_config() {
    # 默认配置
    BACKUP_DIR="./database/backups"
    UPLOADS_DIR="./backend/uploads"
    POSTGRES_CONTAINER="aigc-postgres"
    REDIS_CONTAINER="aigc-redis"
    
    # 从环境变量加载配置
    if [ -f ".env" ]; then
        source .env
    fi
}

# 验证备份路径
validate_backup_path() {
    if [ -z "$BACKUP_PATH" ]; then
        log_error "未指定备份路径"
        show_help
        exit 1
    fi
    
    if [ ! -e "$BACKUP_PATH" ]; then
        log_error "备份路径不存在: $BACKUP_PATH"
        exit 1
    fi
    
    log_info "备份路径: $BACKUP_PATH"
}

# 检测备份类型
detect_backup_type() {
    if [ -f "$BACKUP_PATH" ]; then
        # 文件类型检测
        if [[ "$BACKUP_PATH" == *.tar.gz ]]; then
            BACKUP_TYPE="compressed"
        elif [[ "$BACKUP_PATH" == *.enc ]]; then
            BACKUP_TYPE="encrypted"
        elif [[ "$BACKUP_PATH" == *.sql ]]; then
            BACKUP_TYPE="sql"
        else
            BACKUP_TYPE="file"
        fi
    elif [ -d "$BACKUP_PATH" ]; then
        BACKUP_TYPE="directory"
    else
        log_error "无法识别备份类型: $BACKUP_PATH"
        exit 1
    fi
    
    log_info "备份类型: $BACKUP_TYPE"
}

# 解密备份文件
decrypt_backup() {
    if [ "$DECRYPT" = true ] || [ "$BACKUP_TYPE" = "encrypted" ]; then
        log_info "解密备份文件..."
        
        # 检查解密密钥
        if [ -z "$BACKUP_ENCRYPTION_KEY" ]; then
            log_error "未设置解密密钥 BACKUP_ENCRYPTION_KEY"
            exit 1
        fi
        
        # 解密文件
        DECRYPTED_PATH="${BACKUP_PATH%.enc}"
        openssl enc -aes-256-cbc -d -in "$BACKUP_PATH" -out "$DECRYPTED_PATH" -k "$BACKUP_ENCRYPTION_KEY"
        
        # 更新路径
        BACKUP_PATH="$DECRYPTED_PATH"
        
        # 重新检测类型
        detect_backup_type
        
        log_success "备份解密完成"
    fi
}

# 解压备份文件
extract_backup() {
    if [ "$BACKUP_TYPE" = "compressed" ]; then
        log_info "解压备份文件..."
        
        # 创建临时目录
        TEMP_DIR=$(mktemp -d)
        
        # 解压文件
        tar -xzf "$BACKUP_PATH" -C "$TEMP_DIR"
        
        # 查找备份目录
        BACKUP_DIR=$(find "$TEMP_DIR" -name "aigc_backup_*" -type d | head -n 1)
        
        if [ -z "$BACKUP_DIR" ]; then
            log_error "未找到备份目录"
            exit 1
        fi
        
        # 更新路径
        BACKUP_PATH="$BACKUP_DIR"
        BACKUP_TYPE="directory"
        
        log_success "备份解压完成"
    fi
}

# 验证备份完整性
verify_backup() {
    if [ "$VERIFY" = true ]; then
        log_info "验证备份完整性..."
        
        # 检查必要文件
        required_files=(
            "manifest.txt"
            "postgres_dump.sql"
            "redis_dump.rdb"
        )
        
        for file in "${required_files[@]}"; do
            if [ ! -f "$BACKUP_PATH/$file" ]; then
                log_warning "备份文件不完整，缺少: $file"
            fi
        done
        
        # 检查备份清单
        if [ -f "$BACKUP_PATH/manifest.txt" ]; then
            log_info "备份清单:"
            cat "$BACKUP_PATH/manifest.txt"
        fi
        
        log_success "备份验证完成"
    fi
}

# 停止现有服务
stop_services() {
    if [ "$NO_STOP" = false ]; then
        log_info "停止现有服务..."
        
        # 确定Docker Compose文件
        if [ -f "docker-compose.yml" ]; then
            docker-compose down
        fi
        
        log_success "服务停止完成"
    fi
}

# 恢复PostgreSQL数据库
restore_postgres() {
    if [ "$RESTORE_DATABASE" = true ]; then
        log_info "恢复PostgreSQL数据库..."
        
        # 检查备份文件
        if [ ! -f "$BACKUP_PATH/postgres_dump.sql" ]; then
            log_error "PostgreSQL备份文件不存在: $BACKUP_PATH/postgres_dump.sql"
            return 1
        fi
        
        # 启动PostgreSQL服务
        if [ "$NO_STOP" = false ]; then
            docker-compose up -d postgres
            sleep 10
        fi
        
        # 检查容器是否运行
        if ! docker ps | grep -q "$POSTGRES_CONTAINER"; then
            log_error "PostgreSQL容器未运行"
            return 1
        fi
        
        # 获取数据库信息
        DB_NAME=$(docker exec "$POSTGRES_CONTAINER" printenv POSTGRES_DB)
        DB_USER=$(docker exec "$POSTGRES_CONTAINER" printenv POSTGRES_USER)
        
        # 如果强制恢复，删除现有数据库
        if [ "$FORCE_RESTORE" = true ]; then
            log_warning "删除现有数据库..."
            docker exec "$POSTGRES_CONTAINER" dropdb -U "$DB_USER" "$DB_NAME" --if-exists
            docker exec "$POSTGRES_CONTAINER" createdb -U "$DB_USER" "$DB_NAME"
        fi
        
        # 恢复数据库
        if [ "$TEST_MODE" = false ]; then
            docker exec -i "$POSTGRES_CONTAINER" psql -U "$DB_USER" -d "$DB_NAME" < "$BACKUP_PATH/postgres_dump.sql"
        else
            log_info "测试模式: 跳过数据库实际恢复"
        fi
        
        log_success "PostgreSQL数据库恢复完成"
    fi
}

# 恢复Redis数据
restore_redis() {
    if [ "$RESTORE_DATABASE" = true ]; then
        log_info "恢复Redis数据..."
        
        # 检查备份文件
        if [ ! -f "$BACKUP_PATH/redis_dump.rdb" ]; then
            log_error "Redis备份文件不存在: $BACKUP_PATH/redis_dump.rdb"
            return 1
        fi
        
        # 启动Redis服务
        if [ "$NO_STOP" = false ]; then
            docker-compose up -d redis
            sleep 5
        fi
        
        # 检查容器是否运行
        if ! docker ps | grep -q "$REDIS_CONTAINER"; then
            log_error "Redis容器未运行"
            return 1
        fi
        
        # 停止Redis服务以复制文件
        if [ "$TEST_MODE" = false ]; then
            docker-compose stop redis
            
            # 复制备份文件
            docker cp "$BACKUP_PATH/redis_dump.rdb" "$REDIS_CONTAINER:/data/dump.rdb"
            
            # 重启Redis服务
            docker-compose start redis
        else
            log_info "测试模式: 跳过Redis实际恢复"
        fi
        
        log_success "Redis数据恢复完成"
    fi
}

# 恢复上传文件
restore_uploads() {
    if [ "$RESTORE_UPLOADS" = true ]; then
        log_info "恢复上传文件..."
        
        # 检查备份目录
        if [ ! -d "$BACKUP_PATH/uploads" ]; then
            log_error "上传文件备份不存在: $BACKUP_PATH/uploads"
            return 1
        fi
        
        # 创建上传目录
        mkdir -p "$UPLOADS_DIR"
        
        # 恢复上传文件
        if [ "$TEST_MODE" = false ]; then
            if [ "$FORCE_RESTORE" = true ]; then
                rm -rf "$UPLOADS_DIR"/*
            fi
            
            cp -r "$BACKUP_PATH/uploads"/* "$UPLOADS_DIR/"
        else
            log_info "测试模式: 跳过上传文件实际恢复"
        fi
        
        log_success "上传文件恢复完成"
    fi
}

# 恢复配置文件
restore_configs() {
    if [ "$RESTORE_CONFIGS" = true ]; then
        log_info "恢复配置文件..."
        
        # 检查备份目录
        if [ ! -d "$BACKUP_PATH/configs" ]; then
            log_error "配置文件备份不存在: $BACKUP_PATH/configs"
            return 1
        fi
        
        # 恢复环境配置
        if [ -f "$BACKUP_PATH/configs/env" ]; then
            if [ "$FORCE_RESTORE" = true ] || [ ! -f ".env" ]; then
                if [ "$TEST_MODE" = false ]; then
                    cp "$BACKUP_PATH/configs/env" ".env"
                    log_info "恢复环境配置文件"
                else
                    log_info "测试模式: 跳过环境配置恢复"
                fi
            else
                log_warning "环境配置文件已存在，跳过恢复"
            fi
        fi
        
        # 恢复Docker配置
        config_files=(
            "docker-compose.yml"
            "docker-compose.prod.yml"
            "docker-compose.dev.yml"
            "docker-compose.test.yml"
        )
        
        for config in "${config_files[@]}"; do
            if [ -f "$BACKUP_PATH/configs/$config" ]; then
                if [ "$FORCE_RESTORE" = true ] || [ ! -f "$config" ]; then
                    if [ "$TEST_MODE" = false ]; then
                        cp "$BACKUP_PATH/configs/$config" "$config"
                        log_info "恢复配置文件: $config"
                    else
                        log_info "测试模式: 跳过配置文件恢复 $config"
                    fi
                fi
            fi
        done
        
        # 恢复Nginx配置
        if [ -d "$BACKUP_PATH/configs/nginx" ]; then
            if [ "$FORCE_RESTORE" = true ] || [ ! -d "nginx" ]; then
                if [ "$TEST_MODE" = false ]; then
                    rm -rf "nginx"
                    cp -r "$BACKUP_PATH/configs/nginx" "nginx"
                    log_info "恢复Nginx配置"
                else
                    log_info "测试模式: 跳过Nginx配置恢复"
                fi
            fi
        fi
        
        log_success "配置文件恢复完成"
    fi
}

# 恢复日志文件
restore_logs() {
    if [ "$RESTORE_LOGS" = true ]; then
        log_info "恢复日志文件..."
        
        # 检查备份目录
        if [ ! -d "$BACKUP_PATH/logs" ]; then
            log_error "日志文件备份不存在: $BACKUP_PATH/logs"
            return 1
        fi
        
        # 恢复应用日志
        if [ -d "$BACKUP_PATH/logs/backend" ]; then
            mkdir -p "backend/logs"
            if [ "$TEST_MODE" = false ]; then
                cp -r "$BACKUP_PATH/logs/backend"/* "backend/logs/"
            else
                log_info "测试模式: 跳过后端日志恢复"
            fi
        fi
        
        # 恢复Nginx日志
        if [ -d "$BACKUP_PATH/logs/nginx" ]; then
            mkdir -p "nginx/logs"
            if [ "$TEST_MODE" = false ]; then
                cp -r "$BACKUP_PATH/logs/nginx"/* "nginx/logs/"
            else
                log_info "测试模式: 跳过Nginx日志恢复"
            fi
        fi
        
        log_success "日志文件恢复完成"
    fi
}

# 启动服务
start_services() {
    if [ "$NO_STOP" = false ] && [ "$TEST_MODE" = false ]; then
        log_info "启动服务..."
        
        # 启动所有服务
        docker-compose up -d
        
        # 等待服务启动
        sleep 30
        
        # 检查服务状态
        docker-compose ps
        
        log_success "服务启动完成"
    fi
}

# 验证恢复结果
verify_restore() {
    if [ "$TEST_MODE" = false ]; then
        log_info "验证恢复结果..."
        
        # 检查数据库连接
        if docker exec "$POSTGRES_CONTAINER" pg_isready -U postgres; then
            log_success "PostgreSQL服务正常"
        else
            log_error "PostgreSQL服务异常"
        fi
        
        # 检查Redis连接
        if docker exec "$REDIS_CONTAINER" redis-cli ping | grep -q "PONG"; then
            log_success "Redis服务正常"
        else
            log_error "Redis服务异常"
        fi
        
        # 检查API端点
        if curl -f -s "http://localhost:3000/api/v1/health" > /dev/null; then
            log_success "后端API正常"
        else
            log_warning "后端API可能未启动"
        fi
        
        log_success "恢复验证完成"
    fi
}

# 清理临时文件
cleanup() {
    log_info "清理临时文件..."
    
    # 清理解密文件
    if [ -n "$DECRYPTED_PATH" ] && [ -f "$DECRYPTED_PATH" ]; then
        rm -f "$DECRYPTED_PATH"
    fi
    
    # 清理临时目录
    if [ -n "$TEMP_DIR" ] && [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
    
    log_success "清理完成"
}

# 主函数
main() {
    # 默认参数
    BACKUP_PATH=""
    VERBOSE=false
    FORCE_RESTORE=false
    RESTORE_DATABASE=true
    RESTORE_UPLOADS=true
    RESTORE_CONFIGS=true
    RESTORE_LOGS=true
    TEST_MODE=false
    DECRYPT=false
    DOWNLOAD=false
    VERIFY=false
    NO_STOP=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--force)
                FORCE_RESTORE=true
                shift
                ;;
            -d|--database)
                RESTORE_DATABASE=true
                RESTORE_UPLOADS=false
                RESTORE_CONFIGS=false
                RESTORE_LOGS=false
                shift
                ;;
            -u|--uploads)
                RESTORE_DATABASE=false
                RESTORE_UPLOADS=true
                RESTORE_CONFIGS=false
                RESTORE_LOGS=false
                shift
                ;;
            -c|--configs)
                RESTORE_DATABASE=false
                RESTORE_UPLOADS=false
                RESTORE_CONFIGS=true
                RESTORE_LOGS=false
                shift
                ;;
            -l|--logs)
                RESTORE_DATABASE=false
                RESTORE_UPLOADS=false
                RESTORE_CONFIGS=false
                RESTORE_LOGS=true
                shift
                ;;
            -t|--test)
                TEST_MODE=true
                shift
                ;;
            --decrypt)
                DECRYPT=true
                shift
                ;;
            --download)
                DOWNLOAD=true
                shift
                ;;
            --verify)
                VERIFY=true
                shift
                ;;
            --no-stop)
                NO_STOP=true
                shift
                ;;
            -*)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
            *)
                BACKUP_PATH=$1
                shift
                ;;
        esac
    done
    
    # 设置详细输出
    if [ "$VERBOSE" = true ]; then
        set -x
    fi
    
    # 加载配置
    load_config
    
    log_info "开始恢复 AIGC Service Hub MVP 1.0"
    if [ "$TEST_MODE" = true ]; then
        log_warning "测试模式: 不会实际修改数据"
    fi
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行恢复步骤
    validate_backup_path
    detect_backup_type
    decrypt_backup
    extract_backup
    verify_backup
    stop_services
    
    # 执行恢复
    restore_postgres
    restore_redis
    restore_uploads
    restore_configs
    restore_logs
    
    # 启动服务
    start_services
    
    # 验证恢复结果
    verify_restore
    
    log_success "恢复完成！"
    
    if [ "$TEST_MODE" = false ]; then
        log_info "请检查服务状态: docker-compose ps"
        log_info "前端访问: http://localhost:3001"
        log_info "后端API: http://localhost:3000"
    fi
}

# 执行主函数
main "$@"