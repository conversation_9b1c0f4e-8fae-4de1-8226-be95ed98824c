import { Request, Response } from 'express';
import { AssetService } from '@/services/asset.service';
import { CreateAssetRequest, AssetQuery, AssetType } from '@/types';
import { ValidationError, AssetNotFoundError } from '@/utils/errors';
import { asyncHandler } from '@/utils/errors';
import { logger, logBusinessOperation } from '@/utils/logger';

export class AssetController {
  private assetService: AssetService;

  constructor() {
    this.assetService = new AssetService();
  }

  // 创建资产
  createAsset = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const assetData: CreateAssetRequest = req.body;
    const asset = await this.assetService.createAsset(req.user.id, assetData);

    res.status(201).json({
      success: true,
      data: { asset },
      message: 'Asset created successfully',
    });
  });

  // 更新资产
  updateAsset = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const assetId = parseInt(req.params.id);
    
    if (isNaN(assetId)) {
      throw new ValidationError('Invalid asset ID');
    }

    const updates: Partial<CreateAssetRequest> = req.body;
    const asset = await this.assetService.updateAsset(assetId, req.user.id, updates);

    res.json({
      success: true,
      data: { asset },
      message: 'Asset updated successfully',
    });
  });

  // 发布资产
  publishAsset = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const assetId = parseInt(req.params.id);
    
    if (isNaN(assetId)) {
      throw new ValidationError('Invalid asset ID');
    }

    const asset = await this.assetService.publishAsset(assetId, req.user.id);

    res.json({
      success: true,
      data: { asset },
      message: 'Asset published successfully',
    });
  });

  // 删除资产
  deleteAsset = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const assetId = parseInt(req.params.id);
    
    if (isNaN(assetId)) {
      throw new ValidationError('Invalid asset ID');
    }

    await this.assetService.deleteAsset(assetId, req.user.id);

    res.json({
      success: true,
      message: 'Asset deleted successfully',
    });
  });

  // 获取资产详情
  getAssetById = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const assetId = parseInt(req.params.id);
    
    if (isNaN(assetId)) {
      throw new ValidationError('Invalid asset ID');
    }

    const includePrivate = req.user && req.user.userRole === 'ADMIN';
    const asset = await this.assetService.getAssetById(assetId, includePrivate);

    res.json({
      success: true,
      data: { asset },
      message: 'Asset details retrieved successfully',
    });
  });

  // 获取资产列表
  getAssetList = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const query: AssetQuery = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      category: req.query.category as string,
      style: req.query.style as string,
      assetType: req.query.assetType as AssetType,
      sortBy: req.query.sortBy as 'created_at' | 'price_usd' | 'download_count',
      sortOrder: req.query.sortOrder as 'asc' | 'desc',
      search: req.query.search as string,
    };

    const result = await this.assetService.getAssetList(query);

    res.json({
      success: true,
      data: result,
      message: 'Asset list retrieved successfully',
    });
  });

  // 获取创作者资产
  getCreatorAssets = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const creatorId = parseInt(req.params.creatorId);
    
    if (isNaN(creatorId)) {
      throw new ValidationError('Invalid creator ID');
    }

    const query: AssetQuery = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      assetType: req.query.assetType as AssetType,
      sortBy: req.query.sortBy as 'created_at' | 'price_usd' | 'download_count',
      sortOrder: req.query.sortOrder as 'asc' | 'desc',
    };

    const result = await this.assetService.getCreatorAssets(creatorId, query);

    res.json({
      success: true,
      data: result,
      message: 'Creator assets retrieved successfully',
    });
  });

  // 获取我的资产
  getMyAssets = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const query: AssetQuery = {
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20,
      assetType: req.query.assetType as AssetType,
      sortBy: req.query.sortBy as 'created_at' | 'price_usd' | 'download_count',
      sortOrder: req.query.sortOrder as 'asc' | 'desc',
    };

    const result = await this.assetService.getCreatorAssets(req.user.id, query);

    res.json({
      success: true,
      data: result,
      message: 'My assets retrieved successfully',
    });
  });

  // 获取资产统计
  getAssetStats = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const assetId = parseInt(req.params.id);
    
    if (isNaN(assetId)) {
      throw new ValidationError('Invalid asset ID');
    }

    const stats = await this.assetService.getAssetStats(assetId);

    res.json({
      success: true,
      data: stats,
      message: 'Asset stats retrieved successfully',
    });
  });

  // 更新资产文件
  updateAssetFile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const assetId = parseInt(req.params.id);
    
    if (isNaN(assetId)) {
      throw new ValidationError('Invalid asset ID');
    }

    const { s3FileKey, fileSize } = req.body;
    
    if (!s3FileKey || !fileSize) {
      throw new ValidationError('s3FileKey and fileSize are required');
    }

    await this.assetService.updateAssetFile(assetId, req.user.id, s3FileKey, fileSize);

    res.json({
      success: true,
      message: 'Asset file updated successfully',
    });
  });

  // 下载资产
  downloadAsset = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const assetId = parseInt(req.params.id);
    
    if (isNaN(assetId)) {
      throw new ValidationError('Invalid asset ID');
    }

    // 增加下载计数
    await this.assetService.incrementDownloadCount(assetId);

    // 这里应该生成S3签名URL并返回
    // 暂时返回成功响应
    res.json({
      success: true,
      data: {
        downloadUrl: `https://example.com/download/${assetId}`,
        expiresAt: new Date(Date.now() + 3600000).toISOString(), // 1小时后过期
      },
      message: 'Download URL generated successfully',
    });
  });
}

export default AssetController;