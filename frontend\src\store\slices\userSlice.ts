import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { User, UserProfile, UserRole, UserSettings, PaginationParams } from '../../types';
import { userAPI } from '../../services/api';

// 异步actions
export const fetchUserProfile = createAsyncThunk(
  'user/fetchUserProfile',
  async (userId: string | undefined, { rejectWithValue }) => {
    try {
      const response = await userAPI.getUserProfile(userId);
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取用户资料失败' });
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateUserProfile',
  async (profileData: Partial<UserProfile>, { rejectWithValue }) => {
    try {
      const response = await userAPI.updateUserProfile(profileData);
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '更新用户资料失败' });
    }
  }
);

export const updateUserSettings = createAsyncThunk(
  'user/updateUserSettings',
  async (settings: Partial<UserSettings>, { rejectWithValue }) => {
    try {
      const response = await userAPI.updateUserSettings(settings);
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '更新用户设置失败' });
    }
  }
);

export const uploadAvatar = createAsyncThunk(
  'user/uploadAvatar',
  async (file: File, { rejectWithValue }) => {
    try {
      const formData = new FormData();
      formData.append('avatar', file);
      const response = await userAPI.uploadAvatar(formData);
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '上传头像失败' });
    }
  }
);

export const changePassword = createAsyncThunk(
  'user/changePassword',
  async (params: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }, { rejectWithValue }) => {
    try {
      const response = await userAPI.changePassword(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '修改密码失败' });
    }
  }
);

export const deleteAccount = createAsyncThunk(
  'user/deleteAccount',
  async (params: { password: string; reason?: string }, { rejectWithValue }) => {
    try {
      const response = await userAPI.deleteAccount(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '删除账户失败' });
    }
  }
);

export const fetchUserStats = createAsyncThunk(
  'user/fetchUserStats',
  async (userId: string | undefined, { rejectWithValue }) => {
    try {
      const response = await userAPI.getUserStats(userId);
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取用户统计失败' });
    }
  }
);

export const fetchFollowers = createAsyncThunk(
  'user/fetchFollowers',
  async (params: PaginationParams & { userId?: string }, { rejectWithValue }) => {
    try {
      const response = await userAPI.getFollowers(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取关注者失败' });
    }
  }
);

export const fetchFollowing = createAsyncThunk(
  'user/fetchFollowing',
  async (params: PaginationParams & { userId?: string }, { rejectWithValue }) => {
    try {
      const response = await userAPI.getFollowing(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '获取关注列表失败' });
    }
  }
);

export const followUser = createAsyncThunk(
  'user/followUser',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await userAPI.followUser(userId);
      return { userId, isFollowing: response.data.data.isFollowing };
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '关注操作失败' });
    }
  }
);

export const unfollowUser = createAsyncThunk(
  'user/unfollowUser',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await userAPI.unfollowUser(userId);
      return { userId, isFollowing: response.data.data.isFollowing };
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '取消关注失败' });
    }
  }
);

export const reportUser = createAsyncThunk(
  'user/reportUser',
  async (params: {
    userId: string;
    reason: string;
    description?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await userAPI.reportUser(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '举报用户失败' });
    }
  }
);

export const blockUser = createAsyncThunk(
  'user/blockUser',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await userAPI.blockUser(userId);
      return { userId, isBlocked: response.data.data.isBlocked };
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '屏蔽用户失败' });
    }
  }
);

export const unblockUser = createAsyncThunk(
  'user/unblockUser',
  async (userId: string, { rejectWithValue }) => {
    try {
      const response = await userAPI.unblockUser(userId);
      return { userId, isBlocked: response.data.data.isBlocked };
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '取消屏蔽失败' });
    }
  }
);

export const enableTwoFactor = createAsyncThunk(
  'user/enableTwoFactor',
  async (_, { rejectWithValue }) => {
    try {
      const response = await userAPI.enableTwoFactor();
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '启用两步验证失败' });
    }
  }
);

export const disableTwoFactor = createAsyncThunk(
  'user/disableTwoFactor',
  async (params: { password: string; code: string }, { rejectWithValue }) => {
    try {
      const response = await userAPI.disableTwoFactor(params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || { message: '禁用两步验证失败' });
    }
  }
);

// 用户统计接口
interface UserStats {
  totalAssets: number;
  totalSales: number;
  totalEarnings: number;
  totalFollowers: number;
  totalFollowing: number;
  totalViews: number;
  totalDownloads: number;
  averageRating: number;
  joinDate: string;
  lastActive: string;
}

// 关注关系接口
interface FollowRelation {
  id: string;
  userId: string;
  followerId: string;
  createdAt: string;
  user: User;
}

// 分页响应接口
interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

// 状态接口
interface UserState {
  // 用户信息
  profile: UserProfile | null;
  settings: UserSettings | null;
  stats: UserStats | null;
  
  // 关注关系
  followers: FollowRelation[];
  following: FollowRelation[];
  followersCount: number;
  followingCount: number;
  
  // 屏蔽列表
  blockedUsers: string[];
  
  // 加载状态
  isLoading: boolean;
  isUpdatingProfile: boolean;
  isUpdatingSettings: boolean;
  isUploadingAvatar: boolean;
  isChangingPassword: boolean;
  isDeletingAccount: boolean;
  isLoadingStats: boolean;
  isLoadingFollowers: boolean;
  isLoadingFollowing: boolean;
  isFollowingUser: boolean;
  isUnfollowingUser: boolean;
  isReportingUser: boolean;
  isBlockingUser: boolean;
  isUnblockingUser: boolean;
  isEnablingTwoFactor: boolean;
  isDisablingTwoFactor: boolean;
  
  // 错误状态
  error: string | null;
  
  // 两步验证
  twoFactorEnabled: boolean;
  twoFactorSecret: string | null;
  twoFactorQRCode: string | null;
  
  // 上传进度
  avatarUploadProgress: number;
  
  // 分页信息
  followersPagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  followingPagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

// 初始状态
const initialState: UserState = {
  profile: null,
  settings: null,
  stats: null,
  followers: [],
  following: [],
  followersCount: 0,
  followingCount: 0,
  blockedUsers: JSON.parse(localStorage.getItem('blockedUsers') || '[]'),
  isLoading: false,
  isUpdatingProfile: false,
  isUpdatingSettings: false,
  isUploadingAvatar: false,
  isChangingPassword: false,
  isDeletingAccount: false,
  isLoadingStats: false,
  isLoadingFollowers: false,
  isLoadingFollowing: false,
  isFollowingUser: false,
  isUnfollowingUser: false,
  isReportingUser: false,
  isBlockingUser: false,
  isUnblockingUser: false,
  isEnablingTwoFactor: false,
  isDisablingTwoFactor: false,
  error: null,
  twoFactorEnabled: false,
  twoFactorSecret: null,
  twoFactorQRCode: null,
  avatarUploadProgress: 0,
  followersPagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
  },
  followingPagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
  },
};

// 创建slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    
    // 设置头像上传进度
    setAvatarUploadProgress: (state, action: PayloadAction<number>) => {
      state.avatarUploadProgress = action.payload;
    },
    
    // 添加到屏蔽列表
    addToBlockedUsers: (state, action: PayloadAction<string>) => {
      const userId = action.payload;
      if (!state.blockedUsers.includes(userId)) {
        state.blockedUsers.push(userId);
        localStorage.setItem('blockedUsers', JSON.stringify(state.blockedUsers));
      }
    },
    
    // 从屏蔽列表移除
    removeFromBlockedUsers: (state, action: PayloadAction<string>) => {
      const userId = action.payload;
      state.blockedUsers = state.blockedUsers.filter(id => id !== userId);
      localStorage.setItem('blockedUsers', JSON.stringify(state.blockedUsers));
    },
    
    // 更新统计数据
    updateStats: (state, action: PayloadAction<Partial<UserStats>>) => {
      if (state.stats) {
        state.stats = { ...state.stats, ...action.payload };
      }
    },
    
    // 增加关注者数量
    incrementFollowersCount: (state) => {
      state.followersCount += 1;
      if (state.stats) {
        state.stats.totalFollowers += 1;
      }
    },
    
    // 减少关注者数量
    decrementFollowersCount: (state) => {
      state.followersCount = Math.max(0, state.followersCount - 1);
      if (state.stats) {
        state.stats.totalFollowers = Math.max(0, state.stats.totalFollowers - 1);
      }
    },
    
    // 增加关注数量
    incrementFollowingCount: (state) => {
      state.followingCount += 1;
      if (state.stats) {
        state.stats.totalFollowing += 1;
      }
    },
    
    // 减少关注数量
    decrementFollowingCount: (state) => {
      state.followingCount = Math.max(0, state.followingCount - 1);
      if (state.stats) {
        state.stats.totalFollowing = Math.max(0, state.stats.totalFollowing - 1);
      }
    },
    
    // 重置用户状态
    resetUserState: (state) => {
      Object.assign(state, initialState);
    },
  },
  extraReducers: (builder) => {
    // 获取用户资料
    builder
      .addCase(fetchUserProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.profile = action.payload;
        state.error = null;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 更新用户资料
    builder
      .addCase(updateUserProfile.pending, (state) => {
        state.isUpdatingProfile = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.isUpdatingProfile = false;
        state.profile = action.payload;
        state.error = null;
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.isUpdatingProfile = false;
        state.error = action.payload as string;
      });

    // 更新用户设置
    builder
      .addCase(updateUserSettings.pending, (state) => {
        state.isUpdatingSettings = true;
        state.error = null;
      })
      .addCase(updateUserSettings.fulfilled, (state, action) => {
        state.isUpdatingSettings = false;
        state.settings = action.payload;
        state.error = null;
      })
      .addCase(updateUserSettings.rejected, (state, action) => {
        state.isUpdatingSettings = false;
        state.error = action.payload as string;
      });

    // 上传头像
    builder
      .addCase(uploadAvatar.pending, (state) => {
        state.isUploadingAvatar = true;
        state.error = null;
      })
      .addCase(uploadAvatar.fulfilled, (state, action) => {
        state.isUploadingAvatar = false;
        state.avatarUploadProgress = 0;
        if (state.profile) {
          state.profile.avatar = action.payload.avatarUrl;
        }
        state.error = null;
      })
      .addCase(uploadAvatar.rejected, (state, action) => {
        state.isUploadingAvatar = false;
        state.avatarUploadProgress = 0;
        state.error = action.payload as string;
      });

    // 修改密码
    builder
      .addCase(changePassword.pending, (state) => {
        state.isChangingPassword = true;
        state.error = null;
      })
      .addCase(changePassword.fulfilled, (state) => {
        state.isChangingPassword = false;
        state.error = null;
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.isChangingPassword = false;
        state.error = action.payload as string;
      });

    // 删除账户
    builder
      .addCase(deleteAccount.pending, (state) => {
        state.isDeletingAccount = true;
        state.error = null;
      })
      .addCase(deleteAccount.fulfilled, (state) => {
        state.isDeletingAccount = false;
        state.error = null;
      })
      .addCase(deleteAccount.rejected, (state, action) => {
        state.isDeletingAccount = false;
        state.error = action.payload as string;
      });

    // 获取用户统计
    builder
      .addCase(fetchUserStats.pending, (state) => {
        state.isLoadingStats = true;
        state.error = null;
      })
      .addCase(fetchUserStats.fulfilled, (state, action) => {
        state.isLoadingStats = false;
        state.stats = action.payload;
        state.followersCount = action.payload.totalFollowers;
        state.followingCount = action.payload.totalFollowing;
        state.error = null;
      })
      .addCase(fetchUserStats.rejected, (state, action) => {
        state.isLoadingStats = false;
        state.error = action.payload as string;
      });

    // 获取关注者
    builder
      .addCase(fetchFollowers.pending, (state) => {
        state.isLoadingFollowers = true;
        state.error = null;
      })
      .addCase(fetchFollowers.fulfilled, (state, action) => {
        state.isLoadingFollowers = false;
        const { data, pagination } = action.payload.data as PaginatedResponse<FollowRelation>;
        state.followers = data;
        state.followersPagination = pagination;
        state.error = null;
      })
      .addCase(fetchFollowers.rejected, (state, action) => {
        state.isLoadingFollowers = false;
        state.error = action.payload as string;
      });

    // 获取关注列表
    builder
      .addCase(fetchFollowing.pending, (state) => {
        state.isLoadingFollowing = true;
        state.error = null;
      })
      .addCase(fetchFollowing.fulfilled, (state, action) => {
        state.isLoadingFollowing = false;
        const { data, pagination } = action.payload.data as PaginatedResponse<FollowRelation>;
        state.following = data;
        state.followingPagination = pagination;
        state.error = null;
      })
      .addCase(fetchFollowing.rejected, (state, action) => {
        state.isLoadingFollowing = false;
        state.error = action.payload as string;
      });

    // 关注用户
    builder
      .addCase(followUser.pending, (state) => {
        state.isFollowingUser = true;
        state.error = null;
      })
      .addCase(followUser.fulfilled, (state, action) => {
        state.isFollowingUser = false;
        const { isFollowing } = action.payload;
        if (isFollowing) {
          state.followingCount += 1;
        }
        state.error = null;
      })
      .addCase(followUser.rejected, (state, action) => {
        state.isFollowingUser = false;
        state.error = action.payload as string;
      });

    // 取消关注
    builder
      .addCase(unfollowUser.pending, (state) => {
        state.isUnfollowingUser = true;
        state.error = null;
      })
      .addCase(unfollowUser.fulfilled, (state, action) => {
        state.isUnfollowingUser = false;
        const { isFollowing } = action.payload;
        if (!isFollowing) {
          state.followingCount -= 1;
        }
        state.error = null;
      })
      .addCase(unfollowUser.rejected, (state, action) => {
        state.isUnfollowingUser = false;
        state.error = action.payload as string;
      });

    // 举报用户
    builder
      .addCase(reportUser.pending, (state) => {
        state.isReportingUser = true;
        state.error = null;
      })
      .addCase(reportUser.fulfilled, (state) => {
        state.isReportingUser = false;
        state.error = null;
      })
      .addCase(reportUser.rejected, (state, action) => {
        state.isReportingUser = false;
        state.error = action.payload as string;
      });

    // 屏蔽用户
    builder
      .addCase(blockUser.pending, (state) => {
        state.isBlockingUser = true;
        state.error = null;
      })
      .addCase(blockUser.fulfilled, (state, action) => {
        state.isBlockingUser = false;
        const { userId, isBlocked } = action.payload;
        if (isBlocked) {
          state.blockedUsers.push(userId);
        }
        state.error = null;
      })
      .addCase(blockUser.rejected, (state, action) => {
        state.isBlockingUser = false;
        state.error = action.payload as string;
      });

    // 取消屏蔽
    builder
      .addCase(unblockUser.pending, (state) => {
        state.isUnblockingUser = true;
        state.error = null;
      })
      .addCase(unblockUser.fulfilled, (state, action) => {
        state.isUnblockingUser = false;
        const { userId, isBlocked } = action.payload;
        if (!isBlocked) {
          state.blockedUsers = state.blockedUsers.filter(id => id !== userId);
        }
        state.error = null;
      })
      .addCase(unblockUser.rejected, (state, action) => {
        state.isUnblockingUser = false;
        state.error = action.payload as string;
      });

    // 启用两步验证
    builder
      .addCase(enableTwoFactor.pending, (state) => {
        state.isEnablingTwoFactor = true;
        state.error = null;
      })
      .addCase(enableTwoFactor.fulfilled, (state, action) => {
        state.isEnablingTwoFactor = false;
        state.twoFactorEnabled = true;
        state.twoFactorSecret = action.payload.data.secret;
        state.twoFactorQRCode = action.payload.data.qrCode;
        state.error = null;
      })
      .addCase(enableTwoFactor.rejected, (state, action) => {
        state.isEnablingTwoFactor = false;
        state.error = action.payload as string;
      });

    // 禁用两步验证
    builder
      .addCase(disableTwoFactor.pending, (state) => {
        state.isDisablingTwoFactor = true;
        state.error = null;
      })
      .addCase(disableTwoFactor.fulfilled, (state) => {
        state.isDisablingTwoFactor = false;
        state.twoFactorEnabled = false;
        state.twoFactorSecret = null;
        state.twoFactorQRCode = null;
        state.error = null;
      })
      .addCase(disableTwoFactor.rejected, (state, action) => {
        state.isDisablingTwoFactor = false;
        state.error = action.payload as string;
      });
  },
});

// 导出actions
export const {
  clearError,
  setAvatarUploadProgress,
  addToBlockedUsers,
  removeFromBlockedUsers,
  updateStats,
  incrementFollowersCount,
  decrementFollowersCount,
  incrementFollowingCount,
  decrementFollowingCount,
  resetUserState,
} = userSlice.actions;

// 选择器
export const selectUser = (state: { user: UserState }) => state.user;
export const selectUserProfile = (state: { user: UserState }) => state.user.profile;
export const selectUserSettings = (state: { user: UserState }) => state.user.settings;
export const selectUserStats = (state: { user: UserState }) => state.user.stats;
export const selectFollowers = (state: { user: UserState }) => state.user.followers;
export const selectFollowing = (state: { user: UserState }) => state.user.following;
export const selectFollowersCount = (state: { user: UserState }) => state.user.followersCount;
export const selectFollowingCount = (state: { user: UserState }) => state.user.followingCount;
export const selectBlockedUsers = (state: { user: UserState }) => state.user.blockedUsers;
export const selectIsLoading = (state: { user: UserState }) => state.user.isLoading;
export const selectError = (state: { user: UserState }) => state.user.error;
export const selectTwoFactorEnabled = (state: { user: UserState }) => state.user.twoFactorEnabled;
export const selectTwoFactorQRCode = (state: { user: UserState }) => state.user.twoFactorQRCode;
export const selectAvatarUploadProgress = (state: { user: UserState }) => state.user.avatarUploadProgress;

// 导出reducer
export default userSlice.reducer;