{"name": "aigc-service-hub-frontend", "version": "1.0.0", "description": "AIGC Service Hub MVP 1.0 前端应用", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/lab": "^5.0.0-alpha.140", "@mui/material": "^5.14.5", "@reduxjs/toolkit": "^1.9.5", "@types/node": "^20.5.0", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@uppy/aws-s3": "^3.3.1", "@uppy/core": "^3.3.1", "@uppy/dashboard": "^3.5.1", "@uppy/react": "^3.1.3", "axios": "^1.4.0", "i18next": "^23.4.4", "i18next-browser-languagedetector": "^7.1.0", "i18next-resources-to-backend": "^1.1.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.4", "react-i18next": "^13.1.2", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9", "typescript": "^4.9.5", "web-vitals": "^3.4.0"}, "devDependencies": {"@playwright/test": "^1.37.0", "@testing-library/jest-dom": "^6.1.2", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.3", "@types/react-virtualized-auto-sizer": "^1.0.1", "@types/react-window": "^1.8.5", "@types/react-window-infinite-loader": "^1.0.6", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.20", "jest": "^29.6.2", "prettier": "^3.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "test:e2e": "playwright test", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3000", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["aigc", "service-hub", "frontend", "react", "typescript", "material-ui", "redux"], "author": "AIGC Service Hub Team", "license": "MIT"}