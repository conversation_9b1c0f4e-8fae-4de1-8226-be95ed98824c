version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: aigc-postgres
    restart: unless-stopped
    environment:
      # 数据库配置
      POSTGRES_DB: aigc_service_hub
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      # 性能优化配置
      POSTGRES_INITDB_ARGS: --encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      # 数据持久化
      - postgres_data:/var/lib/postgresql/data
      # 初始化脚本挂载
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/init.sql:/docker-entrypoint-initdb.d/02-init.sql:ro
      # 配置文件挂载
      - ./database/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      # 备份目录
      - ./database/backups:/backups
    command: >
      postgres 
      -c config_file=/etc/postgresql/postgresql.conf
      -c log_destination=stderr
      -c log_statement=mod
      -c log_min_duration_statement=1000
      -c max_connections=100
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c work_mem=4MB
      -c maintenance_work_mem=64MB
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d aigc_service_hub"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: aigc-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./database/redis.conf:/etc/redis/redis.conf:ro
    command: redis-server /etc/redis/redis.conf
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # 数据库管理工具 (可选)
  adminer:
    image: adminer:4.8.1-standalone
    container_name: aigc-adminer
    restart: unless-stopped
    ports:
      - "${ADMINER_PORT:-8080}:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: pepa-linha
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - aigc-network

  # 数据库备份服务
  postgres-backup:
    image: postgres:15-alpine
    container_name: aigc-postgres-backup
    restart: "no"
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      POSTGRES_DB: aigc_service_hub
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres123}
      POSTGRES_HOST: postgres
      BACKUP_SCHEDULE: ${BACKUP_SCHEDULE:-"0 2 * * *"}
    volumes:
      - ./database/backups:/backups
      - ./database/backup-script.sh:/backup-script.sh:ro
    command: >
      sh -c '
        apk add --no-cache dcron
        echo "$${BACKUP_SCHEDULE} /backup-script.sh" > /etc/crontabs/root
        crond -f
      '
    networks:
      - aigc-network

  # 数据库监控 (可选)
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.12.0
    container_name: aigc-postgres-exporter
    restart: unless-stopped
    environment:
      DATA_SOURCE_NAME: "postgresql://postgres:${POSTGRES_PASSWORD:-postgres123}@postgres:5432/aigc_service_hub?sslmode=disable"
      PG_EXPORTER_EXTEND_QUERY_PATH: /etc/postgres_exporter/queries.yaml
    ports:
      - "${POSTGRES_EXPORTER_PORT:-9187}:9187"
    volumes:
      - ./database/postgres-exporter-queries.yaml:/etc/postgres_exporter/queries.yaml:ro
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - aigc-network

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      device: ${PWD}/database/postgres-data
      o: bind
  redis_data:
    driver: local
    driver_opts:
      type: none
      device: ${PWD}/database/redis-data
      o: bind

networks:
  aigc-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

# 使用示例:
# 
# 1. 启动数据库服务:
#    docker-compose -f docker-compose.db.yml up -d
#
# 2. 查看日志:
#    docker-compose -f docker-compose.db.yml logs -f postgres
#
# 3. 连接到数据库:
#    docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -d aigc_service_hub
#
# 4. 执行备份:
#    docker-compose -f docker-compose.db.yml run --rm postgres-backup /backup-script.sh
#
# 5. 停止服务:
#    docker-compose -f docker-compose.db.yml down
#
# 6. 删除数据卷 (谨慎使用):
#    docker-compose -f docker-compose.db.yml down -v
#
# 环境变量文件示例 (.env):
# POSTGRES_PASSWORD=your_secure_password
# POSTGRES_PORT=5432
# REDIS_PORT=6379
# ADMINER_PORT=8080
# POSTGRES_EXPORTER_PORT=9187
# BACKUP_SCHEDULE="0 2 * * *"