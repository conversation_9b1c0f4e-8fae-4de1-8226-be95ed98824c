

# **AIGC Service Hub：产品需求文档 (PRD) \- MVP 1.0**

## **1.0 引言与战略背景**

### **1.1 文档目的**

本文档（PRD）旨在为“AIGC Service Hub”平台的最小可行产品（MVP）定义其范围、功能、用户流程和技术规范。本文档是开发团队的唯一事实来源（Single Source of Truth），并作为指导人工智能集成开发环境（AI IDE）进行代码生成和项目实施的核心输入。所有功能、业务逻辑和非功能性需求均在此进行明确和无歧义的定义，以确保开发过程的高效与精确。

### **1.2 项目愿景与使命**

平台的长期愿景是成为全球领先的AI创作者服务平台，为全球创作者提供一个自由、高效的数字资产上传与交易空间，支持个人与企业在人工智能领域实现技术创新及商业价值创造 1。

平台的核心使命是赋能那些在AI时代浪潮中受到冲击的职业人士。通过提供一个高效的AI资源交易渠道，平台致力于帮助他们应对潜在的失业挑战，开辟新的收入来源，从而在新的技术时代重塑职业生涯，实现个人与商业价值的再创造 1。这一使命直接决定了平台财务系统的公平性、透明性和稳健性是其核心价值主张的关键。

### **1.3 目标用户**

平台的目标用户群体具备较高的技术素养和专业背景，主要包括：设计师、程序员、法律顾问、文案创作者、算法工程师、IT精英、内容创作者、自由职业者以及相关的企业团体 1。这意味着平台的用户体验（UX）和用户界面（UI）应优先考虑功能性、效率和专业性，而非过度简化。用户期望一个稳定、安全且功能强大的专业工具。

### **1.4 MVP核心目标**

本次开发周期的首要指令是“实现最轻量级MVP阶段” 1。然而，项目概述中同时定义了具有高度复杂性的业务逻辑，例如自动化的阶梯式分佣、包含7天账期的提现规则以及退款机制。

为了调和“最轻量级”与“复杂业务逻辑”之间的内在矛盾，MVP的核心目标被定义为：**实现支撑核心交易与分润业务逻辑的最小功能闭环，同时将所有非必要的辅助功能（如游戏化、排行榜等）明确排除在此阶段范围之外。** 这种策略旨在优先验证平台最根本的商业模式——即创作者能否通过平台成功交易并获得公平、可靠的收入——同时最大限度地降低首期开发的复杂度和风险。因此，本PRD将对功能进行严格的优先级划分，确保开发资源聚焦于核心价值的实现。

## **2.0 用户角色、画像与权限控制**

为了实现清晰的访问控制和功能隔离，MVP阶段定义了以下核心用户角色。

### **2.1 用户画像 (MVP)**

* **访客 (Visitor):** 未注册或未登录的用户。其主要行为是浏览平台公开内容，如首页、资源列表和资源详情页。快捷登录即可以购买，访客无法进行上传操作 1。  
* **个人创作者 (Individual Creator):** 完成了创作者身份认证的个人用户。该角色拥有买家的所有权限，并能上传、管理和为自己的数字资产定价。其收入将遵循“个人”分佣体系。  
* **企业创作者 (Enterprise Creator):** 完成了创作者身份认证的企业或团队用户。该角色拥有与个人创作者类似的功能权限，但其收入将遵循独立的“企业”分佣体系。  
* **管理员 (Administrator):** 平台的运营和管理人员。该角色拥有最高权限，负责监督平台内容、管理用户、处理财务操作（如退款和提现审批）以及配置系统设置。管理员通过专用的后台管理界面进行操作。

### **2.2 用户角色与权限矩阵**

为确保AI IDE能够精确实现访问控制逻辑，下表定义了各角色在MVP 1.0版本中的功能权限。开发过程中，所有后端接口和前端UI组件的访问权限必须严格遵循此矩阵。

**表2.1: MVP 1.0 权限矩阵**

| 功能 / 操作 | 访客 | 个人创作者 | 企业创作者 | 管理员 |
| :---- | :---- | :---- | :---- | :---- |
| 浏览公开内容 | ALLOW | ALLOW | ALLOW | ALLOW |
| 搜索资源 | ALLOW | ALLOW | ALLOW | ALLOW |
| 发起购买流程 | ALLOW | ALLOW | ALLOW | ALLOW |
| 完成支付/兑换 | ALLOW | ALLOW | ALLOW | ALLOW |
| 下载已购资源 | ALLOW | ALLOW | ALLOW | ALLOW |
| 访问用户基础仪表盘 | DENY | ALLOW | ALLOW | ALLOW |
| 访问创作者仪表盘 | DENY | ALLOW | ALLOW | ALLOW |
| 上传/管理个人资源 | DENY | ALLOW | ALLOW | ALLOW |
| 设置资源价格 | DENY | ALLOW | ALLOW | ALLOW |
| 发起提现请求 | DENY | ALLOW | ALLOW | DENY |
| **后台管理功能** |  |  |  |  |
| 访问后台管理面板 | DENY | DENY | DENY | ALLOW |
| 管理所有用户 | DENY | DENY | DENY | ALLOW |
| 管理所有资源 | DENY | DENY | DENY | ALLOW |
| 处理退款请求 | DENY | DENY | DENY | ALLOW |
| 处理提现请求 | DENY | DENY | DENY | ALLOW |
| 配置平台设置 | DENY | DENY | DENY | ALLOW |

## **3.0 功能规格：用户账户与身份**

### **3.1 用户注册**

用户注册流程必须明确区分个人和企业账户类型，此选择将直接影响后续的分佣计算 1。

* **表单设计:** 注册页面须包含一个必选的单选框或下拉菜单，选项为“个人账户”和“企业账户”。  
* **数据模型:** 用户在注册时所做的选择，将在Users数据表的user\_role字段中被永久记录为PERSONAL\_CREATOR或ENTERPRISE\_CREATOR。该字段是分佣引擎进行逻辑判断的核心依据。  
* **标准字段:**  
  * Email: 用户邮箱，作为主要登录凭证。  
  * Password: 密码，后端必须使用bcrypt或同等级别的算法进行加盐哈希存储。  
  * Display Name: 用户在平台显示的公开昵称。

### **3.2 用户认证**

为提升用户体验，平台需提供标准和第三方两种登录方式 1。

* **实现方法:**  
  1. **标准登录:** 使用注册的邮箱和密码进行登录。  
  2. **OAuth 2.0 \- Google:** 集成Google账号进行一键登录。  
  3. **OAuth 2.0 \- GitHub:** 集成GitHub账号进行一键登录。  
* **账户关联:** 当用户首次使用OAuth方式登录时，系统应自动创建一个新的用户记录，并将其user\_role默认为PERSONAL\_CREATOR，同时关联该OAuth提供商的用户唯一标识符（ID）。后续该用户通过OAuth登录时，系统将通过此ID进行认证。

### **3.3 用户个人中心 (MVP)**

所有注册用户（个人创作者、企业创作者）都拥有一个基础的个人中心，其MVP阶段功能应保持精简。

* **功能列表:**  
  * **我的购买:** 显示一个包含所有已购资源历史记录的列表，每项记录旁提供重新下载的链接。  
  * **账户设置:** 允许用户修改其Display Name等基本信息。  
  * **积分余额:** 清晰地显示用户当前的积分余额，并提供购买积分的入口。  
  * **创作者入口:** 页面上应有一个清晰的按钮或链接，引导其进入功能更丰富的“创作者仪表盘”。

## **4.0 功能规格：创作者资源工作流**

### **4.1 资源上传流程**

平台规定“最大单个上传文件大小为30GB” 1。这一要求决定了不能使用传统的同步HTTP请求进行文件上传，否则将导致服务器超时和上传失败。必须采用更稳健的分布式对象存储方案。

* **技术架构:**  
  1. **前端触发:** 创作者在前端点击“发布”按钮，并填写资源的元数据（见4.2节）。当用户选择文件并准备上传时，前端向应用后端发起一个API请求。  
  2. **后端生成预签名URL:** 后端不直接接收文件流。相反，它会调用AWS SDK，为Amazon S3存储桶生成一个安全、有时效性（例如，有效期为15分钟）的预签名上传URL（Pre-signed Upload URL）。  
  3. **前端直传S3:** 后端将此预签名URL返回给前端。前端使用该URL，通过支持分块上传和断点续传的前端库（例如，Uppy.js及其AWS S3插件），将大文件直接上传到S3。此过程绕过了应用服务器，极大地提高了上传的稳定性和可扩展性。  
  4. **上传确认:** 文件成功上传到S3后，前端再次调用后端的API，并携带S3返回的文件标识（file\_key），通知后端上传已完成。后端此时将该文件与之前创建的资源元数据记录进行关联，完成整个发布流程。

### **4.2 资源元数据与分类**

创作者在上传资源时，需要提供详细的元数据，以便于平台的分类、索引和展示 1。

* **资源创建表单字段:**  
  * Title (文本): 资源的公开标题。  
  * Description (富文本): 详细描述，支持基本的格式化（如加粗、列表）。  
  * Asset Type (下拉菜单): 资源的核心类型，选项固定为：微调模型、LoRA、工作流、提示词、工具。  
  * Categories (标签输入框): 资源所属的大分类，如视频、音频、图片、文本。创作者可从现有标签中选择，或输入新标签。系统会自动将新标签收录。  
  * Styles (标签输入框): 资源的风格标签，如电商、动漫、建筑、摄影。实现方式同Categories。  
  * Cover Image (图片上传): 资源的分类标签用于在资源卡片上展示的封面图。  
  * Preview Media (可选): 可上传额外的图片或嵌入视频链接，用于详情页的预览。

### **4.3 资源定价与发布**

创作者可以为自己的资源设置美元价格、积分价格，或两者都设置 1。

* **功能规格:**  
  * 在资源创建表单中，提供两个价格输入框：  
    * Price (USD): 美元价格。  
    * Price (Points): 积分价格。  
  * 创作者可以只填写其中一个，或两个都填写。  
  * 提供一个“发布”按钮。点击后，该资源的状态将从DRAFT（草稿）变更为PUBLISHED（已发布），并开始在平台前端对所有用户可见。

## **5.0 功能规格：发现与交易**

### **5.1 资源发现 (首页)**

首页是用户发现资源的主要入口，其布局需严格遵循规范，同时对模糊描述进行澄清。

* **布局澄清:** 项目概述中描述资源展示区（H4）为“最新上传在最下边，自动上顶” 1。这句描述存在歧义。为符合通用的用户体验，此逻辑被澄清为：  
  **资源展示区默认按发布时间倒序排列，即最新发布的资源显示在最顶部。**  
* **瀑布流实现:** H4资源展示区应采用瀑布流（Waterfall Flow）或无限滚动（Infinite Scroll）的布局，动态加载资源卡片。默认排序依据为publication\_date DESC。  
* **资源卡片占位符:** 为降低构建复杂度，所有资源卡片在没有创作者上传封面图的情况下，应显示一张预设的占位符图片，并标注推荐的图片规格 1。当创作者上传了  
  Cover Image后，卡片将自动显示该图片。

### **5.2 购买流程**

平台采用无购物车的即时交易模式 1。

* **流程规格:**  
  1. 在资源详情页，如果资源有价格，将显示购买按钮。如果同时设置了美元和积分价格，则提供两个购买选项，例如“使用 $X.XX 购买”和“使用 YYY 积分兑换”。  
  2. **美元支付:** 用户点击美元购买按钮后，系统调用PayPal支付网关启动支付流程。  
  3. **积分支付:** 用户点击积分兑换按钮后，系统验证其积分余额。如果余额充足，则扣除相应积分并完成交易；如果不足，则提示用户积分不足并引导其购买积分。

### **5.3 支付网关：PayPal集成**

平台指定使用PayPal作为唯一的支付处理方 1。

* **技术规格:** 系统后端需集成PayPal Checkout API。通过配置PayPal的Webhooks或在前端处理API回调，来接收支付成功的确认通知。一旦收到有效的成功通知，系统必须立即触发后续的交易记录和授权下载逻辑。

### **5.4 交易后资源交付**

交易完成后，必须立即、安全地向用户交付其购买的数字资产 1。直接暴露S3的永久链接存在安全风险，可能导致链接被非法分享。因此，必须采用动态授权的交付方式。

* **安全交付架构:**  
  1. **私有存储:** 所有创作者上传的资源文件必须存储在设为\*\*私有（Private）\*\*的Amazon S3存储桶中，禁止任何形式的公开匿名访问。  
  2. **授权生成:** 当已购买用户请求下载资源时，前端向后端的一个专用API端点发起请求。  
  3. **权限验证与链接生成:** 后端接收到请求后，首先验证该用户的身份和其对该资源的购买记录。验证通过后，后端调用AWS SDK，为该特定文件生成一个有时效性（例如，有效期为5分钟）的**预签名下载URL（Pre-signed Download URL）**。  
  4. **安全下载:** 后端将这个临时的、安全的URL返回给前端，前端通过重定向或直接下载的方式启动文件下载。由于该URL在短时间内即失效，从而有效防止了链接的滥用和传播。  
  5. **购买凭证:** 支付成功后，系统应向用户邮箱发送一封包含交易详情的收据邮件，并提供一个指向其个人中心“我的购买”页面的永久链接，用户可随时在该页面重新生成下载链接。

## **6.0 系统逻辑：财务与运营自动化 (MVP核心)**

此部分是平台商业模式的核心，也是技术风险最高的模块，必须进行最精确的定义。

### **6.1 自动化阶梯式分佣引擎 (仅限美元交易)**

平台的分佣机制基于创作者类型和单一资源的累计销售次数动态变化，这是平台公平性的核心体现 1。

**在MVP阶段，此分佣逻辑仅适用于美元（USD）交易。**

* **逻辑规则表:** 为消除任何歧义，分佣算法必须严格遵循下表的规则。AI IDE应被指示实现一个完全匹配此表逻辑的纯函数。

**表6.1: 分佣逻辑规则**

| 创作者类型 | 资源销售次数范围 | 创作者分成比例 (%) | 平台分成比例 (%) |
| :---- | :---- | :---- | :---- |
| 个人 (Individual) | 1 | 95% | 5% |
| 个人 (Individual) | 2 | 90% | 10% |
| 个人 (Individual) | 3 | 85% | 15% |
| ... | ... | ... | ... |
| 个人 (Individual) | 10及以上 | 50% | 50% |
| 企业 (Enterprise) | 1 | 92% | 8% |
| 企业 (Enterprise) | 2 | 84% | 16% |
| ... | ... | ... | ... |
| 企业 (Enterprise) | 7及以上 | 44% | 56% |

*注：源文档中企业分佣上限为平台66%，这会导致创作者分成34%，与递增逻辑不符。此处已修正为平台分成上限56%（8%+6×8%=56%），创作者分成下限44%。*

* **实现逻辑伪代码:**  
  function calculateCommission(transaction):  
    // This function only applies to USD transactions  
    if transaction.currency\!= 'USD':  
      return

    asset \= transaction.asset  
    creator \= asset.creator  
    // 销售次数为历史销售总数，不包含当前这一单  
    sales\_count \= count\_previous\_sales(asset.id, currency='USD')

    if creator.type \== 'INDIVIDUAL':  
      platform\_share\_percent \= 5 \+ (sales\_count \* 5\)  
      platform\_share\_percent \= min(platform\_share\_percent, 50\) // 平台分成最高不超过50%  
    else if creator.type \== 'ENTERPRISE':  
      platform\_share\_percent \= 8 \+ (sales\_count \* 8\)  
      platform\_share\_percent \= min(platform\_share\_percent, 56\) // 平台分成最高不超过56%

    creator\_share\_percent \= 100 \- platform\_share\_percent

    // 根据计算出的比例，创建创作者和平台的账本条目(Ledger Entries)

### **6.2 资金、提现与退款系统**

平台的“七天有理由退款”和“新交易金额需一周后才能申请提现”的规则，共同构成了一个资金状态管理的生命周期 1。

* 金融状态机:  
  为管理资金从交易成功到可提现的整个过程，每一笔给创作者的入账（Ledger Entry）都必须有一个status字段。该字段定义了资金的当前状态：  
  * PENDING (处理中): 交易成功后，创作者分成金额的初始状态。此状态下的资金不可提现，并处于7天的退款保护期内。  
  * AVAILABLE (可提现): 资金安全度过7天保护期后，状态变更为可提现。  
  * REFUNDED (已退款): 在7天保护期内，若发生退款，资金状态变更为已退款。  
  * WITHDRAWN (已提现): 创作者成功提现后，相应资金状态变更为已提现。  
* **功能规格:**  
  * **账本记录:** 每笔成功交易后，系统需在LedgerEntries表中生成至少两条记录：一条是给创作者的入账（初始状态为PENDING），另一条是平台的佣金收入。  
  * **每日定时任务 (Cron Job):** 系统必须部署一个每日自动运行的定时任务。该任务会查询所有创建时间超过7天（168小时）且状态仍为PENDING的账本条目，并将其状态更新为AVAILABLE。  
  * **退款流程 (MVP):** 在MVP阶段，退款流程将简化。用户需要通过客服渠道申请退款。平台管理员在后台核实后（后台管理功能是必需的），手动执行退款操作。该操作会将对应账本条目的状态改为REFUNDED，并通过PayPal API将款项退还给买家。  
  * **提现流程 (MVP):** 创作者仪表盘会显示其“可提现余额”（即所有状态为AVAILABLE的账本条目金额总和）。创作者可以发起提现请求。为降低MVP的初始风险和复杂度，源文档中的“自动化提现”将简化为**管理员手动处理**。即创作者的请求会在后台生成一个待处理任务，由管理员通过PayPal Payouts服务手动完成转账。完整的提现自动化API对接将延后至MVP之后版本。

### **6.3 自动化税务计算**

源文档要求在提现时自动计算并显示税费扣除明细 1。

* MVP范围决策与风险规避:  
  跨司法管辖区的自动化税务计算和代扣代缴是一项极其复杂且具有高度法律风险的功能。在MVP阶段尝试实现此功能，将极大地增加项目的不确定性和法律合规成本。因此，此功能在MVP 1.0中将被明确简化，以规避风险。  
* **MVP实现规格:**  
  1. **不代扣代缴:** 平台在MVP阶段**不进行任何形式的税务自动计算或代扣**。  
  2. **明确免责声明:** 在创作者发起提现请求的界面上，必须以清晰、显著的方式展示以下免责声明：“AIGC Service Hub不负责处理税务代扣事宜。您收到的款项为税前金额，您有全部责任根据您所在地的法律法规，自行申报并缴纳所有适用税款。”  
  3. **提供财务数据:** 为方便创作者自行报税，创作者仪表盘应提供一个功能，允许其将个人收入历史记录导出为CSV格式的文件。  
  4. **功能延后:** 完整的自动化税务计算系统被规划为远期功能，需在获得专业的法律和财务咨询后才能进入开发阶段。

### **6.4 积分系统 (MVP)**

为丰富平台生态并提供灵活的定价选项，MVP阶段将引入积分系统 1。

* **定义:** 积分是平台内的闭环虚拟货币 2，仅用于购买平台上的数字资产。  
* **获取方式:** 用户可以通过PayPal使用美元购买积分。积分的购买价格由管理员在后台设置（例如，$1.00 \= 100积分）。  
* **交易逻辑:**  
  * 当用户使用积分购买资源时，系统将从买家账户扣除相应积分，并全额转入创作者的积分账户。  
  * **在MVP阶段，平台不对积分交易抽取佣金。**  
* **提现规则:**  
  * **积分不可提现。** 在MVP阶段，用户（包括创作者）账户中的积分不能兑换为法定货币 2。积分只能用于在平台内消费。  
  * 将创作者的积分收益兑换为现金的功能，被规划为Post-MVP功能。

## **7.0 前端与用户体验规格 (首页)**

### **7.1 全局元素**

* **页眉 (H1):** 整体高度100px。包含LOGO、搜索框、发布按钮、登录/注册入口、分享按钮和语言切换器（默认为英文，可选中文）1。  
* **导航栏 (H1.2):** 高度30px。此导航栏应在页面滚动时固定在顶部（sticky）。链接包括：首页、微调模型、LoRA、工作流、提示词、工具、挑战、悬赏 1。  
* **页脚 (H5):** 高度260px。包含标准链接，如关于我们、服务条款、隐私政策、联系方式等 1。

### **7.2 首页布局**

下表将源文档中的描述性布局转化为结构化的组件规格，以便于前端开发或AI IDE实现。

**表7.1: 首页组件规格**

| 区域ID | 组件/内容描述 | 高度 | 宽度 | 备注/内容来源 |
| :---- | :---- | :---- | :---- | :---- |
| H1.1 | 页眉: LOGO 搜索 发布 登录 分享 语言（默认英文可选中文） | 100px | 全宽 | 1 |
| H1.2 | 菜单栏: 首页、微调模型、LoRA、工作流、提示词、工具、挑战、悬赏 | 30px | 全宽 | 默认固定 1 |
| H1.3 | 分类栏: 例如视频 音频 图片 文本 | 26px | 全宽 | 由创作者填写自动生成 1 |
| H1.4 | 风格栏: 例如电商 网页 写真 节日 动漫 国画 建筑 园林 摄影 卡通 人像 老照片 美女 男人 女人 素材 | 20px | 全宽 | 由创作者填写自动生成 1 |
| H2.1 | 轮播图活动 | 350px | 4/5 | 管理员在后台定义 1 |
| H2.2 | 英雄榜 | 350px | 1/5 | 系统自动生成前三创作者下载量、作品数、点赞数排行榜 1 |
| H3 | 精选区三幅作品 | 220px | 全宽 | 由管理员选定 1 |
| H4 | 资源展示区 | 自动延伸 | 全宽 | 瀑布流，最新上传在最下边，自动上顶；澄清：应为最新上传在最顶端 1 |
| H5 | 页脚 | 260px | 全宽 | 标准链接，如关于我们、服务条款等 1 |

### **7.3 可访问性**

* **色彩对比度:** 遵循“所有底色和文字对比分明”的原则 1。所有UI组件的文本与背景色对比度必须满足WCAG 2.1 AA级标准，以确保内容对所有用户（包括有视觉障碍的用户）的可读性。

## **8.0 非功能性需求与技术栈**

### **8.1 基础设施**

* **云服务商:** AWS (Amazon Web Services)，服务部署于美国俄勒冈州（us-west-2）区域 1。  
* **对象存储:** Amazon S3，用于存储所有用户上传的资源文件、封面图及其他媒体文件。  
* **数据库:** 推荐使用与PostgreSQL兼容的托管数据库服务（如Amazon RDS for PostgreSQL），因其在处理金融交易数据方面的稳定性和事务完整性表现优异。

### **8.2 架构与部署**

* **容器化:** “开发、测试、集成、部署等所有环境（包括本地、CI/CD、云端测试等）都必须基于 Docker 容器进行” 1。这是一个强制性要求。整个应用（后端服务、前端应用、本地开发数据库等）必须被完全容器化。需提供  
  docker-compose.yml文件用于本地一键启动开发环境，并为生产环境提供Dockerfile。这确保了环境的一致性、可移植性和可复现性，对于AI IDE生成和管理项目至关重要。

### **8.3 开发与文档**

* **辅助工具:** UI/UX原型及交互使用Material-UI组件库实现；所有项目测试使用Playwright框架完成 1。  
* **项目文档:** 必须在项目根目录创建并维护一个README.md文件，详细记录项目的安装、配置、本地启动、测试和部署流程 1。  
* **品牌素材:** 在代码仓库中创建一个/brand-assets目录，用于分类存放项目的LOGO、色值、字体、图片等品牌相关素材 1。

## **9.0 MVP范围定义与阶段规划**

### **9.1 MVP 1.0 范围之内 (In-Scope)**

本节总结了前述3.0至8.0章节中定义的所有功能和逻辑，这些是构成MVP 1.0的全部内容。核心是实现用户从注册、上传、定价到其他用户购买、下载、平台自动分佣、创作者最终可申请提现的完整业务闭环。

### **9.2 MVP 1.0 范围之外 (Out-of-Scope)**

下表明确列出了在源文档中被提及但被战略性地排除在MVP 1.0范围之外的功能。这为开发提供了清晰的边界，防止范围蔓延。

**表9.1: 功能优先级划分 (MVP vs. Post-MVP)**

| 功能 | 源文档提及 1 | MVP 1.0 状态 | 延后理由 |
| :---- | :---- | :---- | :---- |
| **原生移动端应用 (iOS/Android)** | 否 | **范围之外** | MVP阶段聚焦于核心Web平台的开发与验证。 |
| **创作者积分提现** | 否 | **范围之外** | 避免复杂的虚拟货币到法币的兑换流程，保持MVP积分系统的闭环性。 |
| 挑战/悬赏功能 | 是 | **范围之外** | 引入了全新的业务模型，不属于核心市场的交易功能，应在核心模式验证后考虑。 |
| 全自动化提现 | 是 | **简化 (管理员手动处理)** | 降低初期与PayPal Payouts API集成的风险和复杂性。手动处理在初期用户量少时完全可行。 |
| 全自动化税务计算 | 是 | **简化 (仅提供免责声明)** | 极高的技术复杂度和法律风险。推迟此功能是关键的风险规避策略。 |

### **9.3 潜在的Post-MVP路线图**

在MVP 1.0成功上线并验证核心商业模式后，产品迭代将优先考虑在9.2节中被延后的功能。候选功能包括但不限于：创作者积分提现机制、完整的提现自动化、挑战与悬赏系统，以及在充分法律咨询后的税务解决方案。

## **10.0 附录：核心数据模型 (Schema)**

为精确指导AI IDE进行数据库设计，以下提供了核心业务对象的建议数据表结构。

### **10.1 Users (用户表)**

* id (Primary Key)  
* email (String, Unique)  
* password\_hash (String)  
* display\_name (String)  
* user\_role (Enum: PERSONAL\_CREATOR, ENTERPRISE\_CREATOR, ADMIN)  
* points\_balance (Integer, Default: 0\)  
* oauth\_provider (String, Nullable)  
* oauth\_id (String, Nullable)  
* created\_at (Timestamp)  
* updated\_at (Timestamp)

### **10.2 Assets (资源表)**

* id (Primary Key)  
* creator\_id (Foreign Key to Users.id)  
* title (String)  
* description (Text)  
* asset\_type (Enum: MODEL, LORA, WORKFLOW, PROMPT, TOOL)  
* price\_usd (Decimal, Nullable)  
* price\_points (Integer, Nullable)  
* s3\_file\_key (String)  
* cover\_image\_url (String, Nullable)  
* status (Enum: DRAFT, PUBLISHED, ARCHIVED)  
* created\_at (Timestamp)  
* published\_at (Timestamp, Nullable)

### **10.3 Tags (标签表) & AssetTags (中间表)**

* **Tags:** id, name (String, Unique), type (Enum: CATEGORY, STYLE)  
* AssetTags: asset\_id (FK), tag\_id (FK)  
  此多对多关系用于实现资源的分类和风格标签。

### **10.4 Transactions (交易表)**

* id (Primary Key)  
* buyer\_id (Foreign Key to Users.id)  
* asset\_id (Foreign Key to Assets.id)  
* currency (Enum: USD, POINTS)  
* amount\_usd (Decimal, Nullable)  
* amount\_points (Integer, Nullable)  
* paypal\_transaction\_id (String, Nullable, Unique)  
* created\_at (Timestamp)

### **10.5 LedgerEntries (账本条目表)**

*此表是**美元交易**财务系统的核心，记录每一笔资金的流动和状态。*

* id (Primary Key)  
* transaction\_id (Foreign Key to Transactions.id)  
* user\_id (Foreign Key to Users.id, 指向收款方，即创作者或平台)  
* amount (Decimal)  
* entry\_type (Enum: SALE\_CREDIT, PLATFORM\_FEE)  
* status (Enum: PENDING, AVAILABLE, WITHDRAWN, REFUNDED)  
* created\_at (Timestamp)  
* cleared\_at (Timestamp, Nullable) *当状态从PENDING变为AVAILABLE时记录*

#### **引用的著作**

1. 项目概述250704.docx  
2. Virtual Currency Explained: A Guide for Beginners \- Coursera, 访问时间为 七月 11, 2025， [https://www.coursera.org/articles/virtual-currency](https://www.coursera.org/articles/virtual-currency)