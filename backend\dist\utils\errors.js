"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateError = exports.asyncHandler = exports.errorHandler = exports.formatErrorResponse = exports.isOperationalError = exports.isApiError = exports.OAuthError = exports.PayPalError = exports.EmailError = exports.S3Error = exports.CacheError = exports.DatabaseError = exports.CommissionCalculationError = exports.WithdrawalError = exports.PaymentProcessingError = exports.FileUploadError = exports.AssetNotPurchasedError = exports.InsufficientBalanceError = exports.InsufficientPointsError = exports.TransactionNotFoundError = exports.AssetNotFoundError = exports.UserNotFoundError = exports.InvalidTokenError = exports.TokenExpiredError = exports.InvalidCredentialsError = exports.ServiceUnavailableError = exports.InternalServerError = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.ValidationError = exports.ApiError = void 0;
class ApiError extends Error {
    constructor(message, statusCode = 500, code = 'INTERNAL_SERVER_ERROR', details, isOperational = true) {
        super(message);
        this.statusCode = statusCode;
        this.code = code;
        this.details = details;
        this.isOperational = isOperational;
        this.name = this.constructor.name;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.ApiError = ApiError;
class ValidationError extends ApiError {
    constructor(message, details) {
        super(message, 400, 'VALIDATION_ERROR', details);
    }
}
exports.ValidationError = ValidationError;
class AuthenticationError extends ApiError {
    constructor(message = 'Authentication failed') {
        super(message, 401, 'AUTHENTICATION_ERROR');
    }
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends ApiError {
    constructor(message = 'Insufficient permissions') {
        super(message, 403, 'AUTHORIZATION_ERROR');
    }
}
exports.AuthorizationError = AuthorizationError;
class NotFoundError extends ApiError {
    constructor(message = 'Resource not found') {
        super(message, 404, 'NOT_FOUND');
    }
}
exports.NotFoundError = NotFoundError;
class ConflictError extends ApiError {
    constructor(message, details) {
        super(message, 409, 'CONFLICT', details);
    }
}
exports.ConflictError = ConflictError;
class RateLimitError extends ApiError {
    constructor(message = 'Too many requests') {
        super(message, 429, 'RATE_LIMIT_EXCEEDED');
    }
}
exports.RateLimitError = RateLimitError;
class InternalServerError extends ApiError {
    constructor(message = 'Internal server error') {
        super(message, 500, 'INTERNAL_SERVER_ERROR');
    }
}
exports.InternalServerError = InternalServerError;
class ServiceUnavailableError extends ApiError {
    constructor(message = 'Service unavailable') {
        super(message, 503, 'SERVICE_UNAVAILABLE');
    }
}
exports.ServiceUnavailableError = ServiceUnavailableError;
class InvalidCredentialsError extends AuthenticationError {
    constructor() {
        super('Invalid email or password');
    }
}
exports.InvalidCredentialsError = InvalidCredentialsError;
class TokenExpiredError extends AuthenticationError {
    constructor() {
        super('Token has expired');
    }
}
exports.TokenExpiredError = TokenExpiredError;
class InvalidTokenError extends AuthenticationError {
    constructor() {
        super('Invalid token');
    }
}
exports.InvalidTokenError = InvalidTokenError;
class UserNotFoundError extends NotFoundError {
    constructor() {
        super('User not found');
    }
}
exports.UserNotFoundError = UserNotFoundError;
class AssetNotFoundError extends NotFoundError {
    constructor() {
        super('Asset not found');
    }
}
exports.AssetNotFoundError = AssetNotFoundError;
class TransactionNotFoundError extends NotFoundError {
    constructor() {
        super('Transaction not found');
    }
}
exports.TransactionNotFoundError = TransactionNotFoundError;
class InsufficientPointsError extends ApiError {
    constructor(required, available) {
        super(`Insufficient points. Required: ${required}, Available: ${available}`, 400, 'INSUFFICIENT_POINTS', {
            required,
            available,
        });
    }
}
exports.InsufficientPointsError = InsufficientPointsError;
class InsufficientBalanceError extends ApiError {
    constructor(required, available) {
        super(`Insufficient balance. Required: ${required}, Available: ${available}`, 400, 'INSUFFICIENT_BALANCE', {
            required,
            available,
        });
    }
}
exports.InsufficientBalanceError = InsufficientBalanceError;
class AssetNotPurchasedError extends AuthorizationError {
    constructor() {
        super('Asset not purchased');
    }
}
exports.AssetNotPurchasedError = AssetNotPurchasedError;
class FileUploadError extends ApiError {
    constructor(message, details) {
        super(message, 400, 'FILE_UPLOAD_ERROR', details);
    }
}
exports.FileUploadError = FileUploadError;
class PaymentProcessingError extends ApiError {
    constructor(message, details) {
        super(message, 400, 'PAYMENT_PROCESSING_ERROR', details);
    }
}
exports.PaymentProcessingError = PaymentProcessingError;
class WithdrawalError extends ApiError {
    constructor(message, details) {
        super(message, 400, 'WITHDRAWAL_ERROR', details);
    }
}
exports.WithdrawalError = WithdrawalError;
class CommissionCalculationError extends ApiError {
    constructor(message, details) {
        super(message, 500, 'COMMISSION_CALCULATION_ERROR', details);
    }
}
exports.CommissionCalculationError = CommissionCalculationError;
class DatabaseError extends ApiError {
    constructor(message, details) {
        super(message, 500, 'DATABASE_ERROR', details);
    }
}
exports.DatabaseError = DatabaseError;
class CacheError extends ApiError {
    constructor(message, details) {
        super(message, 500, 'CACHE_ERROR', details);
    }
}
exports.CacheError = CacheError;
class S3Error extends ApiError {
    constructor(message, details) {
        super(message, 500, 'S3_ERROR', details);
    }
}
exports.S3Error = S3Error;
class EmailError extends ApiError {
    constructor(message, details) {
        super(message, 500, 'EMAIL_ERROR', details);
    }
}
exports.EmailError = EmailError;
class PayPalError extends ApiError {
    constructor(message, details) {
        super(message, 500, 'PAYPAL_ERROR', details);
    }
}
exports.PayPalError = PayPalError;
class OAuthError extends ApiError {
    constructor(message, details) {
        super(message, 400, 'OAUTH_ERROR', details);
    }
}
exports.OAuthError = OAuthError;
const isApiError = (error) => {
    return error instanceof ApiError;
};
exports.isApiError = isApiError;
const isOperationalError = (error) => {
    return (0, exports.isApiError)(error) && error.isOperational;
};
exports.isOperationalError = isOperationalError;
const formatErrorResponse = (error) => {
    if ((0, exports.isApiError)(error)) {
        return {
            success: false,
            error: {
                code: error.code,
                message: error.message,
                details: error.details,
            },
        };
    }
    return {
        success: false,
        error: {
            code: 'INTERNAL_SERVER_ERROR',
            message: 'An unexpected error occurred',
        },
    };
};
exports.formatErrorResponse = formatErrorResponse;
const errorHandler = (error, req, res, next) => {
    console.error('Error occurred:', error);
    if ((0, exports.isApiError)(error)) {
        return res.status(error.statusCode).json((0, exports.formatErrorResponse)(error));
    }
    if (error.name === 'ValidationError') {
        const validationError = new ValidationError('Validation failed', error.details);
        return res.status(validationError.statusCode).json((0, exports.formatErrorResponse)(validationError));
    }
    if (error.name === 'JsonWebTokenError') {
        const jwtError = new InvalidTokenError();
        return res.status(jwtError.statusCode).json((0, exports.formatErrorResponse)(jwtError));
    }
    if (error.name === 'TokenExpiredError') {
        const expiredError = new TokenExpiredError();
        return res.status(expiredError.statusCode).json((0, exports.formatErrorResponse)(expiredError));
    }
    if (error.code === 'LIMIT_FILE_SIZE') {
        const fileSizeError = new FileUploadError('File size too large');
        return res.status(fileSizeError.statusCode).json((0, exports.formatErrorResponse)(fileSizeError));
    }
    if (error.code === '23505') {
        const conflictError = new ConflictError('Resource already exists');
        return res.status(conflictError.statusCode).json((0, exports.formatErrorResponse)(conflictError));
    }
    if (error.code === '23503') {
        const notFoundError = new NotFoundError('Referenced resource not found');
        return res.status(notFoundError.statusCode).json((0, exports.formatErrorResponse)(notFoundError));
    }
    const serverError = new InternalServerError();
    return res.status(serverError.statusCode).json((0, exports.formatErrorResponse)(serverError));
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const validateError = (error) => {
    if ((0, exports.isApiError)(error)) {
        return error;
    }
    return new InternalServerError(error.message || 'An unexpected error occurred');
};
exports.validateError = validateError;
//# sourceMappingURL=errors.js.map