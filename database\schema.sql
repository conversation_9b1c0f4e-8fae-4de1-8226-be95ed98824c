-- AIGC Service Hub MVP 1.0 数据库Schema
-- PostgreSQL 15+ 兼容

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    display_name VARCHAR(255) NOT NULL,
    user_role VARCHAR(20) NOT NULL CHECK (user_role IN ('PERSONAL_CREATOR', 'ENTERPRISE_CREATOR', 'ADMIN')),
    points_balance INTEGER DEFAULT 0,
    oauth_provider VARCHAR(50),
    oauth_id VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(oauth_provider, oauth_id)
);

-- 标签表
CREATE TABLE tags (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('CATEGORY', 'STYLE')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 资源表
CREATE TABLE assets (
    id SERIAL PRIMARY KEY,
    creator_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    asset_type VARCHAR(20) NOT NULL CHECK (asset_type IN ('MODEL', 'LORA', 'WORKFLOW', 'PROMPT', 'TOOL')),
    price_usd DECIMAL(10,2),
    price_points INTEGER,
    s3_file_key VARCHAR(500) NOT NULL,
    cover_image_url VARCHAR(500),
    status VARCHAR(20) DEFAULT 'DRAFT' CHECK (status IN ('DRAFT', 'PUBLISHED', 'ARCHIVED')),
    download_count INTEGER DEFAULT 0,
    file_size BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    published_at TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT price_check CHECK (price_usd > 0 OR price_points > 0)
);

-- 资源标签关联表
CREATE TABLE asset_tags (
    asset_id INTEGER NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
    tag_id INTEGER NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    PRIMARY KEY (asset_id, tag_id)
);

-- 交易表
CREATE TABLE transactions (
    id SERIAL PRIMARY KEY,
    buyer_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    asset_id INTEGER NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
    currency VARCHAR(10) NOT NULL CHECK (currency IN ('USD', 'POINTS')),
    amount_usd DECIMAL(10,2),
    amount_points INTEGER,
    paypal_transaction_id VARCHAR(255) UNIQUE,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'COMPLETED', 'FAILED', 'REFUNDED')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    refunded_at TIMESTAMP,
    CONSTRAINT amount_check CHECK (
        (currency = 'USD' AND amount_usd > 0) OR 
        (currency = 'POINTS' AND amount_points > 0)
    )
);

-- 账本条目表（财务系统核心）
CREATE TABLE ledger_entries (
    id SERIAL PRIMARY KEY,
    transaction_id INTEGER REFERENCES transactions(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    entry_type VARCHAR(20) NOT NULL CHECK (entry_type IN ('SALE_CREDIT', 'PLATFORM_FEE', 'POINTS_PURCHASE', 'POINTS_DEBIT')),
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'AVAILABLE', 'WITHDRAWN', 'REFUNDED')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    cleared_at TIMESTAMP,
    withdrawn_at TIMESTAMP,
    notes TEXT
);

-- 提现请求表
CREATE TABLE withdrawal_requests (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    paypal_email VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED', 'COMPLETED')),
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    processed_by INTEGER REFERENCES users(id),
    paypal_payout_id VARCHAR(255),
    rejection_reason TEXT
);

-- 系统配置表
CREATE TABLE system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(20) DEFAULT 'STRING' CHECK (config_type IN ('STRING', 'NUMBER', 'BOOLEAN', 'JSON')),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 性能优化索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_oauth ON users(oauth_provider, oauth_id);
CREATE INDEX idx_users_role ON users(user_role);

CREATE INDEX idx_assets_creator ON assets(creator_id);
CREATE INDEX idx_assets_status ON assets(status);
CREATE INDEX idx_assets_type ON assets(asset_type);
CREATE INDEX idx_assets_published ON assets(published_at DESC) WHERE status = 'PUBLISHED';
CREATE INDEX idx_assets_created ON assets(created_at DESC);

CREATE INDEX idx_transactions_buyer ON transactions(buyer_id);
CREATE INDEX idx_transactions_asset ON transactions(asset_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_created ON transactions(created_at DESC);
CREATE INDEX idx_transactions_completed ON transactions(completed_at DESC) WHERE status = 'COMPLETED';

CREATE INDEX idx_ledger_user ON ledger_entries(user_id);
CREATE INDEX idx_ledger_transaction ON ledger_entries(transaction_id);
CREATE INDEX idx_ledger_status ON ledger_entries(status);
CREATE INDEX idx_ledger_created ON ledger_entries(created_at DESC);
CREATE INDEX idx_ledger_type ON ledger_entries(entry_type);

CREATE INDEX idx_withdrawal_user ON withdrawal_requests(user_id);
CREATE INDEX idx_withdrawal_status ON withdrawal_requests(status);
CREATE INDEX idx_withdrawal_created ON withdrawal_requests(created_at DESC);

CREATE INDEX idx_tags_type ON tags(type);
CREATE INDEX idx_tags_name ON tags(name);

-- 更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assets_updated_at 
    BEFORE UPDATE ON assets 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configs_updated_at 
    BEFORE UPDATE ON system_configs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建视图：用户收益统计
CREATE VIEW user_earnings_summary AS
SELECT 
    u.id as user_id,
    u.display_name,
    u.user_role,
    COALESCE(SUM(CASE WHEN le.status = 'AVAILABLE' THEN le.amount ELSE 0 END), 0) as available_balance,
    COALESCE(SUM(CASE WHEN le.status = 'PENDING' THEN le.amount ELSE 0 END), 0) as pending_balance,
    COALESCE(SUM(CASE WHEN le.status = 'WITHDRAWN' THEN le.amount ELSE 0 END), 0) as withdrawn_balance,
    COALESCE(SUM(CASE WHEN le.entry_type = 'SALE_CREDIT' THEN le.amount ELSE 0 END), 0) as total_earnings,
    COUNT(DISTINCT a.id) as total_assets,
    COUNT(DISTINCT t.id) as total_sales
FROM users u
LEFT JOIN assets a ON u.id = a.creator_id
LEFT JOIN transactions t ON a.id = t.asset_id AND t.status = 'COMPLETED'
LEFT JOIN ledger_entries le ON u.id = le.user_id AND le.entry_type = 'SALE_CREDIT'
WHERE u.user_role IN ('PERSONAL_CREATOR', 'ENTERPRISE_CREATOR')
GROUP BY u.id, u.display_name, u.user_role;

-- 创建视图：资产销售统计
CREATE VIEW asset_sales_summary AS
SELECT 
    a.id as asset_id,
    a.title,
    a.asset_type,
    a.price_usd,
    a.price_points,
    a.download_count,
    COUNT(t.id) as total_sales,
    COALESCE(SUM(CASE WHEN t.currency = 'USD' THEN t.amount_usd ELSE 0 END), 0) as total_usd_revenue,
    COALESCE(SUM(CASE WHEN t.currency = 'POINTS' THEN t.amount_points ELSE 0 END), 0) as total_points_revenue,
    a.created_at,
    a.published_at,
    u.display_name as creator_name
FROM assets a
LEFT JOIN transactions t ON a.id = t.asset_id AND t.status = 'COMPLETED'
LEFT JOIN users u ON a.creator_id = u.id
GROUP BY a.id, a.title, a.asset_type, a.price_usd, a.price_points, a.download_count, a.created_at, a.published_at, u.display_name;

-- 平台统计视图
CREATE VIEW platform_stats AS
SELECT 
    COUNT(DISTINCT u.id) as total_users,
    COUNT(DISTINCT CASE WHEN u.user_role IN ('PERSONAL_CREATOR', 'ENTERPRISE_CREATOR') THEN u.id END) as total_creators,
    COUNT(DISTINCT a.id) as total_assets,
    COUNT(DISTINCT CASE WHEN a.status = 'PUBLISHED' THEN a.id END) as published_assets,
    COUNT(DISTINCT t.id) as total_transactions,
    COALESCE(SUM(CASE WHEN t.currency = 'USD' AND t.status = 'COMPLETED' THEN t.amount_usd ELSE 0 END), 0) as total_usd_volume,
    COALESCE(SUM(CASE WHEN t.currency = 'POINTS' AND t.status = 'COMPLETED' THEN t.amount_points ELSE 0 END), 0) as total_points_volume,
    COALESCE(SUM(CASE WHEN le.entry_type = 'PLATFORM_FEE' THEN le.amount ELSE 0 END), 0) as total_platform_fees
FROM users u
CROSS JOIN assets a
CROSS JOIN transactions t
CROSS JOIN ledger_entries le;

-- 添加数据完整性约束
ALTER TABLE assets ADD CONSTRAINT assets_file_size_check CHECK (file_size >= 0);
ALTER TABLE users ADD CONSTRAINT users_points_balance_check CHECK (points_balance >= 0);
ALTER TABLE transactions ADD CONSTRAINT transactions_paypal_id_check 
    CHECK (currency != 'USD' OR paypal_transaction_id IS NOT NULL);

-- 创建函数：计算分佣
CREATE OR REPLACE FUNCTION calculate_commission(
    p_asset_id INTEGER,
    p_amount_usd DECIMAL(10,2)
) RETURNS TABLE(
    platform_amount DECIMAL(10,2),
    creator_amount DECIMAL(10,2),
    platform_percentage INTEGER
) AS $$
DECLARE
    v_creator_role VARCHAR(20);
    v_sales_count INTEGER;
    v_platform_percentage INTEGER;
BEGIN
    -- 获取创作者角色
    SELECT u.user_role INTO v_creator_role
    FROM users u
    JOIN assets a ON u.id = a.creator_id
    WHERE a.id = p_asset_id;
    
    -- 计算历史销售次数
    SELECT COUNT(*) INTO v_sales_count
    FROM transactions t
    WHERE t.asset_id = p_asset_id 
    AND t.status = 'COMPLETED' 
    AND t.currency = 'USD';
    
    -- 计算平台分佣比例
    IF v_creator_role = 'PERSONAL_CREATOR' THEN
        v_platform_percentage := LEAST(5 + (v_sales_count * 5), 50);
    ELSIF v_creator_role = 'ENTERPRISE_CREATOR' THEN
        v_platform_percentage := LEAST(8 + (v_sales_count * 8), 56);
    ELSE
        v_platform_percentage := 0;
    END IF;
    
    RETURN QUERY SELECT 
        ROUND(p_amount_usd * v_platform_percentage / 100, 2) as platform_amount,
        ROUND(p_amount_usd * (100 - v_platform_percentage) / 100, 2) as creator_amount,
        v_platform_percentage as platform_percentage;
END;
$$ LANGUAGE plpgsql;

-- 创建函数：处理交易完成后的账本条目
CREATE OR REPLACE FUNCTION process_transaction_completion()
RETURNS TRIGGER AS $$
DECLARE
    v_commission RECORD;
    v_creator_id INTEGER;
BEGIN
    -- 只处理USD交易的状态变更为COMPLETED
    IF OLD.status != 'COMPLETED' AND NEW.status = 'COMPLETED' AND NEW.currency = 'USD' THEN
        -- 获取创作者ID
        SELECT creator_id INTO v_creator_id FROM assets WHERE id = NEW.asset_id;
        
        -- 计算分佣
        SELECT * INTO v_commission FROM calculate_commission(NEW.asset_id, NEW.amount_usd);
        
        -- 创建创作者收益账本条目
        INSERT INTO ledger_entries (
            transaction_id, user_id, amount, entry_type, status
        ) VALUES (
            NEW.id, v_creator_id, v_commission.creator_amount, 'SALE_CREDIT', 'PENDING'
        );
        
        -- 创建平台费用账本条目
        INSERT INTO ledger_entries (
            transaction_id, user_id, amount, entry_type, status
        ) VALUES (
            NEW.id, v_creator_id, v_commission.platform_amount, 'PLATFORM_FEE', 'AVAILABLE'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器：交易完成时处理账本条目
CREATE TRIGGER trigger_transaction_completion
    AFTER UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION process_transaction_completion();

-- 创建函数：积分交易处理
CREATE OR REPLACE FUNCTION process_points_transaction()
RETURNS TRIGGER AS $$
DECLARE
    v_creator_id INTEGER;
BEGIN
    -- 处理积分交易完成
    IF OLD.status != 'COMPLETED' AND NEW.status = 'COMPLETED' AND NEW.currency = 'POINTS' THEN
        -- 获取创作者ID
        SELECT creator_id INTO v_creator_id FROM assets WHERE id = NEW.asset_id;
        
        -- 扣除买家积分
        UPDATE users SET points_balance = points_balance - NEW.amount_points 
        WHERE id = NEW.buyer_id;
        
        -- 创建积分扣除记录
        INSERT INTO ledger_entries (
            transaction_id, user_id, amount, entry_type, status
        ) VALUES (
            NEW.id, NEW.buyer_id, NEW.amount_points, 'POINTS_DEBIT', 'AVAILABLE'
        );
        
        -- 增加资产下载次数
        UPDATE assets SET download_count = download_count + 1 WHERE id = NEW.asset_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器：积分交易处理
CREATE TRIGGER trigger_points_transaction
    AFTER UPDATE ON transactions
    FOR EACH ROW
    EXECUTE FUNCTION process_points_transaction();

-- 创建数据清理函数
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- 清理超过1年的失败交易记录
    DELETE FROM transactions 
    WHERE status = 'FAILED' 
    AND created_at < CURRENT_DATE - INTERVAL '1 year';
    
    -- 清理超过3个月的草稿资产（无交易记录）
    DELETE FROM assets 
    WHERE status = 'DRAFT' 
    AND created_at < CURRENT_DATE - INTERVAL '3 months'
    AND id NOT IN (SELECT DISTINCT asset_id FROM transactions);
END;
$$ LANGUAGE plpgsql;

-- 授权相关表的权限
-- 注意：在生产环境中需要创建专门的应用用户并授予最小权限

COMMENT ON TABLE users IS '用户表，支持个人/企业创作者和管理员';
COMMENT ON TABLE assets IS '数字资产表，存储所有上传的资源信息';
COMMENT ON TABLE transactions IS '交易表，记录USD和积分交易';
COMMENT ON TABLE ledger_entries IS '账本条目表，财务系统核心，记录所有资金流动';
COMMENT ON TABLE withdrawal_requests IS '提现请求表，管理创作者收益提现';
COMMENT ON TABLE system_configs IS '系统配置表，存储平台参数';
COMMENT ON TABLE tags IS '标签表，用于资产分类';
COMMENT ON TABLE asset_tags IS '资产标签关联表';

COMMENT ON COLUMN users.user_role IS '用户角色：PERSONAL_CREATOR(个人创作者)、ENTERPRISE_CREATOR(企业创作者)、ADMIN(管理员)';
COMMENT ON COLUMN assets.asset_type IS '资产类型：MODEL(模型)、LORA(LoRA)、WORKFLOW(工作流)、PROMPT(提示词)、TOOL(工具)';
COMMENT ON COLUMN transactions.currency IS '交易货币：USD(美元)、POINTS(积分)';
COMMENT ON COLUMN ledger_entries.entry_type IS '账本类型：SALE_CREDIT(销售收入)、PLATFORM_FEE(平台费用)、POINTS_PURCHASE(积分购买)、POINTS_DEBIT(积分扣除)';
COMMENT ON COLUMN ledger_entries.status IS '资金状态：PENDING(7天保护期)、AVAILABLE(可提现)、WITHDRAWN(已提现)、REFUNDED(已退款)';