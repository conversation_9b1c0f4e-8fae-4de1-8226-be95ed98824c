{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "removeComments": true, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@controllers/*": ["controllers/*"], "@services/*": ["services/*"], "@models/*": ["models/*"], "@middlewares/*": ["middlewares/*"], "@routes/*": ["routes/*"], "@utils/*": ["utils/*"], "@config/*": ["config/*"], "@types/*": ["types/*"], "@database/*": ["database/*"], "@workers/*": ["workers/*"]}, "types": ["node", "jest"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}