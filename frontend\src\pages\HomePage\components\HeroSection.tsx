import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
  IconButton,
  useTheme,
  useMediaQuery,
  Skeleton
} from '@mui/material';
import {
  ArrowBackIos,
  ArrowForwardIos,
  Download,
  Favorite,
  Visibility,
  EmojiEvents
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { userAPI, assetsAPI } from '../../../services/api';

interface HeroSectionProps {}

interface LeaderboardData {
  downloads: Array<{
    id: string;
    name: string;
    avatar?: string;
    downloadCount: number;
  }>;
  assets: Array<{
    id: string;
    name: string;
    avatar?: string;
    assetCount: number;
  }>;
  likes: Array<{
    id: string;
    name: string;
    avatar?: string;
    likeCount: number;
  }>;
}

interface CarouselItem {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  actionText: string;
  actionUrl: string;
  type: 'activity' | 'featured' | 'announcement';
}

const HeroSection: React.FC<HeroSectionProps> = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // 状态管理
  const [currentSlide, setCurrentSlide] = useState(0);
  const [leaderboard, setLeaderboard] = useState<LeaderboardData>({
    downloads: [],
    assets: [],
    likes: []
  });
  const [loading, setLoading] = useState(true);

  // 轮播图数据（实际项目中应该从API获取）
  const carouselItems: CarouselItem[] = [
    {
      id: '1',
      title: t('home.hero.carousel.item1.title', 'AI模型创作大赛'),
      description: t('home.hero.carousel.item1.desc', '参与创作，赢取丰厚奖品'),
      imageUrl: '/images/carousel/contest.jpg',
      actionText: t('home.hero.carousel.item1.action', '立即参与'),
      actionUrl: '/contests/ai-model-contest',
      type: 'activity'
    },
    {
      id: '2',
      title: t('home.hero.carousel.item2.title', '精选LoRA模型'),
      description: t('home.hero.carousel.item2.desc', '发现最新最热门的LoRA模型'),
      imageUrl: '/images/carousel/featured-lora.jpg',
      actionText: t('home.hero.carousel.item2.action', '查看更多'),
      actionUrl: '/assets?type=LORA&featured=true',
      type: 'featured'
    },
    {
      id: '3',
      title: t('home.hero.carousel.item3.title', '平台功能更新'),
      description: t('home.hero.carousel.item3.desc', '新增批量上传和高级搜索功能'),
      imageUrl: '/images/carousel/update.jpg',
      actionText: t('home.hero.carousel.item3.action', '了解详情'),
      actionUrl: '/announcements/platform-update',
      type: 'announcement'
    }
  ];

  // 获取排行榜数据
  useEffect(() => {
    loadLeaderboardData();
  }, []);

  // 自动轮播
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselItems.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [carouselItems.length]);

  const loadLeaderboardData = async () => {
    setLoading(true);
    try {
      // 模拟API调用获取排行榜数据
      // 实际项目中应该调用真实的API
      const mockLeaderboard: LeaderboardData = {
        downloads: [
          { id: '1', name: 'AI创作者A', downloadCount: 15420, avatar: '/avatars/user1.jpg' },
          { id: '2', name: 'AI创作者B', downloadCount: 12350, avatar: '/avatars/user2.jpg' },
          { id: '3', name: 'AI创作者C', downloadCount: 9876, avatar: '/avatars/user3.jpg' }
        ],
        assets: [
          { id: '1', name: 'AI创作者D', assetCount: 234, avatar: '/avatars/user4.jpg' },
          { id: '2', name: 'AI创作者E', assetCount: 198, avatar: '/avatars/user5.jpg' },
          { id: '3', name: 'AI创作者F', assetCount: 156, avatar: '/avatars/user6.jpg' }
        ],
        likes: [
          { id: '1', name: 'AI创作者G', likeCount: 8765, avatar: '/avatars/user7.jpg' },
          { id: '2', name: 'AI创作者H', likeCount: 7432, avatar: '/avatars/user8.jpg' },
          { id: '3', name: 'AI创作者I', likeCount: 6543, avatar: '/avatars/user9.jpg' }
        ]
      };

      setLeaderboard(mockLeaderboard);
    } catch (error) {
      console.error('Failed to load leaderboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePrevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + carouselItems.length) % carouselItems.length);
  };

  const handleNextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % carouselItems.length);
  };

  const formatNumber = (num: number): string => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}万`;
    }
    return num.toLocaleString();
  };

  return (
    <Box
      sx={{
        height: 350,
        display: 'flex',
        gap: 2,
        p: 2,
        backgroundColor: 'background.default'
      }}
    >
      {/* 轮播图活动区 (4/5宽度) */}
      <Box
        sx={{
          flex: isMobile ? 1 : 4,
          position: 'relative',
          borderRadius: 2,
          overflow: 'hidden',
          backgroundColor: 'background.paper',
          boxShadow: theme.shadows[2]
        }}
      >
        {/* 轮播图内容 */}
        <Box
          sx={{
            width: '100%',
            height: '100%',
            position: 'relative',
            backgroundImage: `url(${carouselItems[currentSlide]?.imageUrl || '/images/placeholder-carousel.jpg'})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            display: 'flex',
            alignItems: 'flex-end'
          }}
        >
          {/* 渐变遮罩 */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%)'
            }}
          />

          {/* 内容区域 */}
          <Box
            sx={{
              position: 'relative',
              zIndex: 1,
              p: 3,
              color: 'white',
              width: '100%'
            }}
          >
            <Typography variant="h4" component="h2" sx={{ mb: 1, fontWeight: 'bold' }}>
              {carouselItems[currentSlide]?.title}
            </Typography>
            <Typography variant="body1" sx={{ mb: 2, opacity: 0.9 }}>
              {carouselItems[currentSlide]?.description}
            </Typography>
            <Chip
              label={carouselItems[currentSlide]?.actionText}
              color="primary"
              sx={{ 
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
              onClick={() => {
                // 处理点击事件，跳转到对应页面
                window.open(carouselItems[currentSlide]?.actionUrl, '_blank');
              }}
            />
          </Box>

          {/* 导航按钮 */}
          <IconButton
            sx={{
              position: 'absolute',
              left: 16,
              top: '50%',
              transform: 'translateY(-50%)',
              backgroundColor: 'rgba(255,255,255,0.2)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.3)'
              }
            }}
            onClick={handlePrevSlide}
          >
            <ArrowBackIos />
          </IconButton>

          <IconButton
            sx={{
              position: 'absolute',
              right: 16,
              top: '50%',
              transform: 'translateY(-50%)',
              backgroundColor: 'rgba(255,255,255,0.2)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.3)'
              }
            }}
            onClick={handleNextSlide}
          >
            <ArrowForwardIos />
          </IconButton>

          {/* 指示器 */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 16,
              left: '50%',
              transform: 'translateX(-50%)',
              display: 'flex',
              gap: 1
            }}
          >
            {carouselItems.map((_, index) => (
              <Box
                key={index}
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: index === currentSlide ? 'white' : 'rgba(255,255,255,0.5)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                onClick={() => setCurrentSlide(index)}
              />
            ))}
          </Box>
        </Box>
      </Box>

      {/* 英雄榜区域 (1/5宽度) */}
      {!isMobile && (
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            gap: 1
          }}
        >
          {/* 下载量排行榜 */}
          <Card sx={{ flex: 1, minHeight: 0 }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Download sx={{ fontSize: 16, mr: 0.5, color: 'primary.main' }} />
                <Typography variant="caption" fontWeight="bold">
                  {t('home.hero.leaderboard.downloads')}
                </Typography>
              </Box>
              <List dense sx={{ p: 0 }}>
                {loading ? (
                  Array.from({ length: 3 }).map((_, index) => (
                    <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                      <ListItemAvatar sx={{ minWidth: 32 }}>
                        <Skeleton variant="circular" width={24} height={24} />
                      </ListItemAvatar>
                      <ListItemText
                        primary={<Skeleton variant="text" width="60%" />}
                        secondary={<Skeleton variant="text" width="40%" />}
                      />
                    </ListItem>
                  ))
                ) : (
                  leaderboard.downloads.map((user, index) => (
                    <ListItem key={user.id} sx={{ px: 0, py: 0.5 }}>
                      <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                        <EmojiEvents 
                          sx={{ 
                            fontSize: 16,
                            color: index === 0 ? '#FFD700' : index === 1 ? '#C0C0C0' : '#CD7F32'
                          }} 
                        />
                      </Box>
                      <ListItemAvatar sx={{ minWidth: 32 }}>
                        <Avatar sx={{ width: 24, height: 24 }} src={user.avatar}>
                          {user.name.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography variant="caption" noWrap>
                            {user.name}
                          </Typography>
                        }
                        secondary={
                          <Typography variant="caption" color="text.secondary">
                            {formatNumber(user.downloadCount)}
                          </Typography>
                        }
                      />
                    </ListItem>
                  ))
                )}
              </List>
            </CardContent>
          </Card>

          {/* 作品数量排行榜 */}
          <Card sx={{ flex: 1, minHeight: 0 }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Visibility sx={{ fontSize: 16, mr: 0.5, color: 'secondary.main' }} />
                <Typography variant="caption" fontWeight="bold">
                  {t('home.hero.leaderboard.assets')}
                </Typography>
              </Box>
              <List dense sx={{ p: 0 }}>
                {loading ? (
                  Array.from({ length: 3 }).map((_, index) => (
                    <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                      <ListItemAvatar sx={{ minWidth: 32 }}>
                        <Skeleton variant="circular" width={24} height={24} />
                      </ListItemAvatar>
                      <ListItemText
                        primary={<Skeleton variant="text" width="60%" />}
                        secondary={<Skeleton variant="text" width="40%" />}
                      />
                    </ListItem>
                  ))
                ) : (
                  leaderboard.assets.map((user, index) => (
                    <ListItem key={user.id} sx={{ px: 0, py: 0.5 }}>
                      <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                        <EmojiEvents 
                          sx={{ 
                            fontSize: 16,
                            color: index === 0 ? '#FFD700' : index === 1 ? '#C0C0C0' : '#CD7F32'
                          }} 
                        />
                      </Box>
                      <ListItemAvatar sx={{ minWidth: 32 }}>
                        <Avatar sx={{ width: 24, height: 24 }} src={user.avatar}>
                          {user.name.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography variant="caption" noWrap>
                            {user.name}
                          </Typography>
                        }
                        secondary={
                          <Typography variant="caption" color="text.secondary">
                            {user.assetCount} {t('home.hero.leaderboard.works')}
                          </Typography>
                        }
                      />
                    </ListItem>
                  ))
                )}
              </List>
            </CardContent>
          </Card>

          {/* 点赞数排行榜 */}
          <Card sx={{ flex: 1, minHeight: 0 }}>
            <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Favorite sx={{ fontSize: 16, mr: 0.5, color: 'error.main' }} />
                <Typography variant="caption" fontWeight="bold">
                  {t('home.hero.leaderboard.likes')}
                </Typography>
              </Box>
              <List dense sx={{ p: 0 }}>
                {loading ? (
                  Array.from({ length: 3 }).map((_, index) => (
                    <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                      <ListItemAvatar sx={{ minWidth: 32 }}>
                        <Skeleton variant="circular" width={24} height={24} />
                      </ListItemAvatar>
                      <ListItemText
                        primary={<Skeleton variant="text" width="60%" />}
                        secondary={<Skeleton variant="text" width="40%" />}
                      />
                    </ListItem>
                  ))
                ) : (
                  leaderboard.likes.map((user, index) => (
                    <ListItem key={user.id} sx={{ px: 0, py: 0.5 }}>
                      <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                        <EmojiEvents 
                          sx={{ 
                            fontSize: 16,
                            color: index === 0 ? '#FFD700' : index === 1 ? '#C0C0C0' : '#CD7F32'
                          }} 
                        />
                      </Box>
                      <ListItemAvatar sx={{ minWidth: 32 }}>
                        <Avatar sx={{ width: 24, height: 24 }} src={user.avatar}>
                          {user.name.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography variant="caption" noWrap>
                            {user.name}
                          </Typography>
                        }
                        secondary={
                          <Typography variant="caption" color="text.secondary">
                            {formatNumber(user.likeCount)}
                          </Typography>
                        }
                      />
                    </ListItem>
                  ))
                )}
              </List>
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default HeroSection;
