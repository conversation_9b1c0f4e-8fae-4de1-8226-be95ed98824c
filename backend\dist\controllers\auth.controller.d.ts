export declare class AuthController {
    private authService;
    constructor();
    register: (req: any, res: any, next: any) => void;
    login: (req: any, res: any, next: any) => void;
    refreshToken: (req: any, res: any, next: any) => void;
    getProfile: (req: any, res: any, next: any) => void;
    changePassword: (req: any, res: any, next: any) => void;
    requestPasswordReset: (req: any, res: any, next: any) => void;
    resetPassword: (req: any, res: any, next: any) => void;
    logout: (req: any, res: any, next: any) => void;
    googleCallback: (req: any, res: any, next: any) => void;
    githubCallback: (req: any, res: any, next: any) => void;
    verifyEmail: (req: any, res: any, next: any) => void;
    resendVerification: (req: any, res: any, next: any) => void;
    checkEmailAvailability: (req: any, res: any, next: any) => void;
    getUserPermissions: (req: any, res: any, next: any) => void;
}
export default AuthController;
//# sourceMappingURL=auth.controller.d.ts.map