import { Router } from 'express';
import { AdminController } from '@/controllers/admin.controller';
import authMiddleware from '@/middlewares/auth.middleware';

const router = Router();
const adminController = new AdminController();

// 所有管理员路由都需要认证和管理员权限
router.use(authMiddleware.authenticate);
router.use(authMiddleware.requireAdmin);

// 用户管理
router.get('/users', adminController.getUsers);
router.get('/users/:userId', adminController.getUserById);
router.put('/users/:userId', adminController.updateUser);
router.delete('/users/:userId', adminController.deleteUser);
router.post('/users/:userId/activate', adminController.activateUser);
router.post('/users/:userId/deactivate', adminController.deactivateUser);

// 资产管理
router.get('/assets', adminController.getAssets);
router.get('/assets/:assetId', adminController.getAssetById);
router.put('/assets/:assetId', adminController.updateAsset);
router.delete('/assets/:assetId', adminController.deleteAsset);
router.post('/assets/:assetId/approve', adminController.approveAsset);
router.post('/assets/:assetId/reject', adminController.rejectAsset);

// 交易管理
router.get('/transactions', adminController.getTransactions);
router.get('/transactions/:transactionId', adminController.getTransactionById);
router.post('/transactions/:transactionId/refund', adminController.refundTransaction);

// 财务管理
router.get('/withdrawals', adminController.getWithdrawals);
router.get('/withdrawals/:withdrawalId', adminController.getWithdrawalById);
router.post('/withdrawals/:withdrawalId/approve', adminController.approveWithdrawal);
router.post('/withdrawals/:withdrawalId/reject', adminController.rejectWithdrawal);

// 系统统计
router.get('/stats/overview', adminController.getOverviewStats);
router.get('/stats/users', adminController.getUserStats);
router.get('/stats/assets', adminController.getAssetStats);
router.get('/stats/transactions', adminController.getTransactionStats);
router.get('/stats/revenue', adminController.getRevenueStats);

// 系统设置
router.get('/settings', adminController.getSettings);
router.put('/settings', adminController.updateSettings);

// 日志查看
router.get('/logs/security', adminController.getSecurityLogs);
router.get('/logs/error', adminController.getErrorLogs);
router.get('/logs/audit', adminController.getAuditLogs);

// 系统监控
router.get('/health', adminController.getSystemHealth);
router.get('/metrics', adminController.getSystemMetrics);

// 内容管理
router.get('/reports', adminController.getReports);
router.post('/reports/:reportId/resolve', adminController.resolveReport);

// 批量操作
router.post('/bulk/users/activate', adminController.bulkActivateUsers);
router.post('/bulk/users/deactivate', adminController.bulkDeactivateUsers);
router.post('/bulk/assets/approve', adminController.bulkApproveAssets);
router.post('/bulk/assets/reject', adminController.bulkRejectAssets);

export default router;