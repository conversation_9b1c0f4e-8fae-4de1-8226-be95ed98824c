"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("../config");
const logger_1 = require("../utils/logger");
const errors_1 = require("../utils/errors");
const connection_1 = require("../database/connection");
class AuthService {
    constructor() {
        this.db = (0, connection_1.getDb)();
    }
    async register(registerData) {
        const { email, password, displayName, userRole } = registerData;
        try {
            const existingUser = await this.findUserByEmail(email);
            if (existingUser) {
                throw new errors_1.ConflictError('User with this email already exists');
            }
            const saltRounds = 12;
            const passwordHash = await bcrypt_1.default.hash(password, saltRounds);
            const query = `
        INSERT INTO users (email, password_hash, display_name, user_role)
        VALUES ($1, $2, $3, $4)
        RETURNING id, email, display_name, user_role, points_balance, is_active, created_at, updated_at
      `;
            const result = await this.db.query(query, [email, passwordHash, displayName, userRole]);
            const user = result.rows[0];
            const tokens = this.generateTokens(user);
            logger_1.logger.info('User registered', { userId: user.id, email, userRole });
            return {
                ...tokens,
                user: {
                    id: user.id,
                    email: user.email,
                    displayName: user.display_name,
                    userRole: user.user_role,
                    pointsBalance: user.points_balance,
                    isActive: user.is_active,
                    createdAt: user.created_at,
                    updatedAt: user.updated_at,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Registration failed:', error);
            throw error;
        }
    }
    async login(loginData) {
        const { email, password } = loginData;
        try {
            const user = await this.findUserByEmail(email);
            if (!user) {
                throw new errors_1.AuthenticationError('Invalid credentials');
            }
            if (!user.is_active) {
                throw new errors_1.AuthenticationError('Account is not active');
            }
            const isPasswordValid = await bcrypt_1.default.compare(password, user.password_hash);
            if (!isPasswordValid) {
                throw new errors_1.AuthenticationError('Invalid credentials');
            }
            const tokens = this.generateTokens(user);
            logger_1.logger.info('User logged in', { userId: user.id, email });
            return {
                ...tokens,
                user: {
                    id: user.id,
                    email: user.email,
                    displayName: user.display_name,
                    userRole: user.user_role,
                    pointsBalance: user.points_balance,
                    isActive: user.is_active,
                    createdAt: user.created_at,
                    updatedAt: user.updated_at,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Login failed:', error);
            throw error;
        }
    }
    async refreshToken(refreshToken) {
        try {
            const decoded = jsonwebtoken_1.default.verify(refreshToken, config_1.config.jwt.secret);
            const user = await this.findUserById(decoded.userId);
            if (!user || !user.is_active) {
                throw new errors_1.AuthenticationError('Invalid token');
            }
            const tokens = this.generateTokens(user);
            logger_1.logger.info('Token refreshed', { userId: user.id });
            return tokens;
        }
        catch (error) {
            logger_1.logger.error('Token refresh failed:', error);
            throw new errors_1.AuthenticationError('Invalid refresh token');
        }
    }
    async verifyAccessToken(token) {
        try {
            const decoded = jsonwebtoken_1.default.verify(token, config_1.config.jwt.secret);
            const user = await this.findUserById(decoded.userId);
            if (!user || !user.is_active) {
                throw new errors_1.AuthenticationError('Invalid token');
            }
            return user;
        }
        catch (error) {
            throw new errors_1.AuthenticationError('Invalid access token');
        }
    }
    async changePassword(userId, currentPassword, newPassword) {
        try {
            const user = await this.findUserById(userId);
            if (!user) {
                throw new errors_1.ValidationError('User not found');
            }
            const isCurrentPasswordValid = await bcrypt_1.default.compare(currentPassword, user.password_hash);
            if (!isCurrentPasswordValid) {
                throw new errors_1.AuthenticationError('Current password is incorrect');
            }
            const saltRounds = 12;
            const newPasswordHash = await bcrypt_1.default.hash(newPassword, saltRounds);
            const query = `
        UPDATE users 
        SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `;
            await this.db.query(query, [newPasswordHash, userId]);
            logger_1.logger.info('Password changed', { userId });
        }
        catch (error) {
            logger_1.logger.error('Password change failed:', error);
            throw error;
        }
    }
    generateTokens(user) {
        const payload = {
            userId: user.id,
            email: user.email,
            userRole: user.user_role,
        };
        const accessToken = jsonwebtoken_1.default.sign(payload, config_1.config.jwt.secret, {
            expiresIn: config_1.config.jwt.expiresIn,
        });
        const refreshToken = jsonwebtoken_1.default.sign({ userId: user.id }, config_1.config.jwt.secret, { expiresIn: config_1.config.jwt.refreshExpiresIn });
        return { accessToken, refreshToken };
    }
    async requestPasswordReset(email) {
        try {
            const user = await this.findUserByEmail(email);
            if (!user) {
                return;
            }
            const resetToken = jsonwebtoken_1.default.sign({ userId: user.id, type: 'password_reset' }, config_1.config.jwt.secret, { expiresIn: '1h' });
            logger_1.logger.info('Password reset requested', { userId: user.id, email });
        }
        catch (error) {
            logger_1.logger.error('Password reset request failed:', error);
            throw error;
        }
    }
    async resetPassword(resetToken, newPassword) {
        try {
            const decoded = jsonwebtoken_1.default.verify(resetToken, config_1.config.jwt.secret);
            if (decoded.type !== 'password_reset') {
                throw new errors_1.AuthenticationError('Invalid reset token');
            }
            const saltRounds = 12;
            const passwordHash = await bcrypt_1.default.hash(newPassword, saltRounds);
            const query = `
        UPDATE users
        SET password_hash = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `;
            await this.db.query(query, [passwordHash, decoded.userId]);
            logger_1.logger.info('Password reset completed', { userId: decoded.userId });
        }
        catch (error) {
            logger_1.logger.error('Password reset failed:', error);
            throw new errors_1.AuthenticationError('Invalid or expired reset token');
        }
    }
    async checkEmailAvailability(email) {
        try {
            const existingUser = await this.findUserByEmail(email);
            return { available: !existingUser };
        }
        catch (error) {
            logger_1.logger.error('Check email availability failed:', error);
            throw error;
        }
    }
    async findUserByEmail(email) {
        const query = `
      SELECT id, email, password_hash, display_name, user_role, points_balance, 
             is_active, created_at, updated_at
      FROM users
      WHERE email = $1
    `;
        const result = await this.db.query(query, [email]);
        return result.rows[0];
    }
    async findUserById(id) {
        const query = `
      SELECT id, email, password_hash, display_name, user_role, points_balance, 
             is_active, created_at, updated_at
      FROM users
      WHERE id = $1
    `;
        const result = await this.db.query(query, [id]);
        return result.rows[0];
    }
}
exports.AuthService = AuthService;
exports.default = AuthService;
//# sourceMappingURL=auth.service.js.map