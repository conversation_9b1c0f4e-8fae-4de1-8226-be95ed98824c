export declare class AssetController {
    private assetService;
    constructor();
    createAsset: (req: any, res: any, next: any) => void;
    updateAsset: (req: any, res: any, next: any) => void;
    publishAsset: (req: any, res: any, next: any) => void;
    deleteAsset: (req: any, res: any, next: any) => void;
    getAssetById: (req: any, res: any, next: any) => void;
    getAssetList: (req: any, res: any, next: any) => void;
    getCreatorAssets: (req: any, res: any, next: any) => void;
    getMyAssets: (req: any, res: any, next: any) => void;
    getAssetStats: (req: any, res: any, next: any) => void;
    updateAssetFile: (req: any, res: any, next: any) => void;
    downloadAsset: (req: any, res: any, next: any) => void;
}
export default AssetController;
//# sourceMappingURL=asset.controller.d.ts.map