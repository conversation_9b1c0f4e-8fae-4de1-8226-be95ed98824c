#!/bin/bash

# ===========================================
# AIGC Service Hub MVP 1.0 - 开发环境重置脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AIGC Service Hub MVP 1.0 - 开发环境重置脚本

使用方法: $0 [OPTIONS]

OPTIONS:
    -h, --help          显示帮助信息
    -v, --verbose       详细输出
    -f, --force         强制重置 (不询问确认)
    -q, --quick         快速重置 (保留镜像)
    -d, --data-only     仅重置数据
    -i, --images        重置并重新构建镜像
    -s, --seed          重置后添加种子数据
    -b, --backup        重置前创建备份
    --keep-logs         保留日志文件
    --keep-uploads      保留上传文件

示例:
    $0                  # 完整重置开发环境
    $0 -f               # 强制重置
    $0 -q               # 快速重置
    $0 -d               # 仅重置数据
    $0 -b -s            # 备份后重置并添加种子数据

EOF
}

# 确认重置操作
confirm_reset() {
    if [ "$FORCE_RESET" = false ]; then
        log_warning "即将重置开发环境，这将删除所有数据"
        echo
        echo "重置内容:"
        echo "  - 停止所有容器"
        echo "  - 删除数据库数据"
        echo "  - 删除Redis数据"
        if [ "$KEEP_LOGS" = false ]; then
            echo "  - 删除日志文件"
        fi
        if [ "$KEEP_UPLOADS" = false ]; then
            echo "  - 删除上传文件"
        fi
        if [ "$REBUILD_IMAGES" = true ]; then
            echo "  - 重新构建镜像"
        fi
        echo
        
        read -p "确定要继续吗？ (y/N): " -n 1 -r
        echo
        
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消重置操作"
            exit 0
        fi
    fi
}

# 创建备份
create_backup() {
    if [ "$CREATE_BACKUP" = true ]; then
        log_info "创建重置前备份..."
        
        if [ -f "scripts/backup.sh" ]; then
            ./scripts/backup.sh -v
            log_success "备份创建完成"
        else
            log_warning "备份脚本不存在，跳过备份"
        fi
    fi
}

# 停止所有服务
stop_services() {
    log_info "停止所有开发环境服务..."
    
    # 停止所有服务
    docker-compose -f docker-compose.dev.yml down --remove-orphans 2>/dev/null || true
    
    # 强制停止相关容器
    docker ps -a --format "{{.Names}}" | grep -E "(aigc|postgres|redis|nginx|adminer|mailhog)" | xargs -r docker stop 2>/dev/null || true
    docker ps -a --format "{{.Names}}" | grep -E "(aigc|postgres|redis|nginx|adminer|mailhog)" | xargs -r docker rm 2>/dev/null || true
    
    log_success "服务停止完成"
}

# 清理数据
clean_data() {
    log_info "清理数据..."
    
    # 清理数据库数据
    if [ -d "database/postgres-data" ]; then
        rm -rf database/postgres-data/*
        log_info "清理PostgreSQL数据"
    fi
    
    # 清理Redis数据
    if [ -d "database/redis-data" ]; then
        rm -rf database/redis-data/*
        log_info "清理Redis数据"
    fi
    
    # 清理日志文件
    if [ "$KEEP_LOGS" = false ]; then
        if [ -d "backend/logs" ]; then
            rm -rf backend/logs/*
            log_info "清理后端日志"
        fi
        
        if [ -d "nginx/logs" ]; then
            rm -rf nginx/logs/*
            log_info "清理Nginx日志"
        fi
        
        if [ -d "scripts/logs" ]; then
            rm -rf scripts/logs/*
            log_info "清理脚本日志"
        fi
    fi
    
    # 清理上传文件
    if [ "$KEEP_UPLOADS" = false ]; then
        if [ -d "backend/uploads" ]; then
            rm -rf backend/uploads/*
            log_info "清理上传文件"
        fi
    fi
    
    log_success "数据清理完成"
}

# 清理Docker资源
clean_docker() {
    log_info "清理Docker资源..."
    
    # 清理数据卷
    docker volume ls -q | grep -E "(aigc|postgres|redis)" | xargs -r docker volume rm 2>/dev/null || true
    
    # 清理网络
    docker network ls -q | grep -E "(aigc|dev)" | xargs -r docker network rm 2>/dev/null || true
    
    # 清理未使用的资源
    docker system prune -f
    
    log_success "Docker资源清理完成"
}

# 重建镜像
rebuild_images() {
    if [ "$REBUILD_IMAGES" = true ]; then
        log_info "重新构建镜像..."
        
        # 删除现有镜像
        docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "(aigc|backend|frontend)" | xargs -r docker rmi -f 2>/dev/null || true
        
        # 重新构建
        docker-compose -f docker-compose.dev.yml build --no-cache
        
        log_success "镜像重建完成"
    fi
}

# 重新创建目录结构
recreate_directories() {
    log_info "重新创建目录结构..."
    
    directories=(
        "database/backups"
        "database/postgres-data"
        "database/redis-data"
        "backend/logs"
        "backend/uploads"
        "backend/coverage"
        "frontend/build"
        "frontend/coverage"
        "nginx/logs"
        "scripts/logs"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        # 添加.gitkeep文件
        touch "$dir/.gitkeep"
    done
    
    log_success "目录结构创建完成"
}

# 重置环境配置
reset_environment() {
    log_info "重置环境配置..."
    
    # 重新复制开发环境配置
    if [ -f ".env.development" ]; then
        cp ".env.development" ".env"
        log_info "重置环境变量配置"
    fi
    
    # 重置数据库配置
    if [ -f "database/postgresql.conf" ]; then
        log_info "数据库配置已就绪"
    fi
    
    # 重置Redis配置
    if [ -f "database/redis.conf" ]; then
        log_info "Redis配置已就绪"
    fi
    
    log_success "环境配置重置完成"
}

# 启动服务
start_services() {
    log_info "启动重置后的服务..."
    
    # 启动数据库服务
    docker-compose -f docker-compose.dev.yml up -d postgres redis
    
    # 等待数据库启动
    sleep 15
    
    # 启动后端服务
    docker-compose -f docker-compose.dev.yml up -d backend
    
    # 等待后端启动
    sleep 10
    
    # 启动前端服务
    docker-compose -f docker-compose.dev.yml up -d frontend
    
    # 启动开发工具
    docker-compose -f docker-compose.dev.yml up -d adminer redis-commander mailhog
    
    log_success "服务启动完成"
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    # 等待后端服务完全启动
    sleep 10
    
    # 运行迁移
    docker-compose -f docker-compose.dev.yml exec backend npm run db:migrate
    
    log_success "数据库迁移完成"
}

# 添加种子数据
seed_database() {
    if [ "$ADD_SEED_DATA" = true ]; then
        log_info "添加种子数据..."
        
        # 运行种子数据脚本
        docker-compose -f docker-compose.dev.yml exec backend npm run db:seed
        
        log_success "种子数据添加完成"
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查PostgreSQL
    for i in {1..30}; do
        if docker-compose -f docker-compose.dev.yml exec postgres pg_isready -U postgres -q; then
            log_success "PostgreSQL 健康检查通过"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "PostgreSQL 健康检查失败"
            return 1
        fi
        
        sleep 2
    done
    
    # 检查Redis
    for i in {1..30}; do
        if docker-compose -f docker-compose.dev.yml exec redis redis-cli ping | grep -q "PONG"; then
            log_success "Redis 健康检查通过"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "Redis 健康检查失败"
            return 1
        fi
        
        sleep 2
    done
    
    # 检查后端API
    for i in {1..30}; do
        if curl -f -s "http://localhost:3000/api/v1/health" > /dev/null; then
            log_success "后端API 健康检查通过"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "后端API 健康检查失败"
            return 1
        fi
        
        sleep 2
    done
    
    # 检查前端
    for i in {1..30}; do
        if curl -f -s "http://localhost:3001" > /dev/null; then
            log_success "前端应用 健康检查通过"
            break
        fi
        
        if [ $i -eq 30 ]; then
            log_error "前端应用 健康检查失败"
            return 1
        fi
        
        sleep 2
    done
    
    log_success "健康检查完成"
}

# 显示重置结果
show_reset_result() {
    log_success "开发环境重置完成！"
    
    echo
    echo "=================== 重置结果 ==================="
    echo "时间: $(date)"
    echo "环境: 开发环境"
    echo
    echo "服务状态:"
    docker-compose -f docker-compose.dev.yml ps
    echo
    echo "服务访问地址:"
    echo "  🌐 前端应用: http://localhost:3001"
    echo "  🚀 后端API: http://localhost:3000"
    echo "  📚 API文档: http://localhost:3000/api/docs"
    echo "  🗄️  数据库管理: http://localhost:8080"
    echo "  🔧 Redis管理: http://localhost:8081"
    echo "  📧 邮件测试: http://localhost:8025"
    echo
    echo "常用命令:"
    echo "  查看日志: ./scripts/logs.sh"
    echo "  停止服务: ./scripts/dev-stop.sh"
    echo "  重新启动: ./scripts/dev-start.sh"
    echo "=============================================="
}

# 主函数
main() {
    # 默认参数
    VERBOSE=false
    FORCE_RESET=false
    QUICK_RESET=false
    DATA_ONLY=false
    REBUILD_IMAGES=false
    ADD_SEED_DATA=false
    CREATE_BACKUP=false
    KEEP_LOGS=false
    KEEP_UPLOADS=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -f|--force)
                FORCE_RESET=true
                shift
                ;;
            -q|--quick)
                QUICK_RESET=true
                shift
                ;;
            -d|--data-only)
                DATA_ONLY=true
                shift
                ;;
            -i|--images)
                REBUILD_IMAGES=true
                shift
                ;;
            -s|--seed)
                ADD_SEED_DATA=true
                shift
                ;;
            -b|--backup)
                CREATE_BACKUP=true
                shift
                ;;
            --keep-logs)
                KEEP_LOGS=true
                shift
                ;;
            --keep-uploads)
                KEEP_UPLOADS=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置详细输出
    if [ "$VERBOSE" = true ]; then
        set -x
    fi
    
    # 检查配置文件
    if [ ! -f "docker-compose.dev.yml" ]; then
        log_error "开发环境配置文件不存在: docker-compose.dev.yml"
        exit 1
    fi
    
    log_info "重置 AIGC Service Hub MVP 1.0 开发环境"
    
    # 确认重置
    confirm_reset
    
    # 创建备份
    create_backup
    
    # 停止服务
    stop_services
    
    # 清理数据
    clean_data
    
    # 如果不是快速重置，清理Docker资源
    if [ "$QUICK_RESET" = false ]; then
        clean_docker
    fi
    
    # 重建镜像
    rebuild_images
    
    # 重新创建目录结构
    recreate_directories
    
    # 重置环境配置
    reset_environment
    
    # 如果不是仅重置数据，重新启动服务
    if [ "$DATA_ONLY" = false ]; then
        start_services
        run_migrations
        seed_database
        health_check
    fi
    
    # 显示结果
    show_reset_result
    
    log_success "开发环境重置完成！"
}

# 执行主函数
main "$@"