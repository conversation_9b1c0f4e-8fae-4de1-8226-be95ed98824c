import { Pool } from 'pg';
import { getDb } from '@/database/connection';
import { 
  Transaction, 
  TransactionStatus, 
  Currency, 
  PurchaseRequest, 
  TransactionQuery 
} from '@/types';
import { 
  TransactionNotFoundError, 
  ValidationError, 
  InsufficientPointsError,
  AssetNotFoundError
} from '@/utils/errors';
import { logger, logBusinessOperation } from '@/utils/logger';

export class TransactionService {
  private db: Pool;

  constructor() {
    this.db = getDb();
  }

  // 创建购买交易
  async createPurchaseTransaction(buyerId: number, purchaseData: PurchaseRequest): Promise<Transaction> {
    const client = await this.db.connect();
    
    try {
      await client.query('BEGIN');

      const { assetId, currency } = purchaseData;

      // 获取资产信息
      const assetQuery = `
        SELECT id, title, price_usd, price_points, creator_id, status
        FROM assets
        WHERE id = $1 AND status = 'PUBLISHED'
      `;
      
      const assetResult = await client.query(assetQuery, [assetId]);
      
      if (assetResult.rows.length === 0) {
        throw new AssetNotFoundError();
      }

      const asset = assetResult.rows[0];

      // 检查用户是否试图购买自己的资产
      if (asset.creator_id === buyerId) {
        throw new ValidationError('Cannot purchase your own asset');
      }

      // 检查用户是否已经购买过这个资产
      const existingPurchase = await client.query(
        'SELECT id FROM transactions WHERE buyer_id = $1 AND asset_id = $2 AND status = $3',
        [buyerId, assetId, TransactionStatus.COMPLETED]
      );

      if (existingPurchase.rows.length > 0) {
        throw new ValidationError('Asset already purchased');
      }

      let amountUsd = null;
      let amountPoints = null;

      if (currency === Currency.USD) {
        amountUsd = asset.price_usd;
        if (!amountUsd) {
          throw new ValidationError('Asset is not available for USD purchase');
        }
      } else if (currency === Currency.POINTS) {
        amountPoints = asset.price_points;
        if (!amountPoints) {
          throw new ValidationError('Asset is not available for points purchase');
        }

        // 检查用户积分余额
        const userQuery = `
          SELECT points_balance FROM users WHERE id = $1
        `;
        const userResult = await client.query(userQuery, [buyerId]);
        
        if (userResult.rows.length === 0) {
          throw new Error('User not found');
        }

        const userBalance = userResult.rows[0].points_balance;
        if (userBalance < amountPoints) {
          throw new InsufficientPointsError(amountPoints, userBalance);
        }
      }

      // 创建交易记录
      const transactionQuery = `
        INSERT INTO transactions (buyer_id, asset_id, currency, amount_usd, amount_points, status)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, buyer_id, asset_id, currency, amount_usd, amount_points, status, created_at
      `;
      
      const transactionResult = await client.query(transactionQuery, [
        buyerId,
        assetId,
        currency,
        amountUsd,
        amountPoints,
        TransactionStatus.PENDING
      ]);

      const transaction = transactionResult.rows[0];

      await client.query('COMMIT');

      logBusinessOperation('TRANSACTION_CREATE', buyerId, { 
        transactionId: transaction.id, 
        assetId, 
        currency, 
        amount: amountUsd || amountPoints 
      });

      return {
        id: transaction.id,
        buyerId: transaction.buyer_id,
        assetId: transaction.asset_id,
        currency: transaction.currency,
        amountUsd: transaction.amount_usd,
        amountPoints: transaction.amount_points,
        status: transaction.status,
        createdAt: transaction.created_at,
      };
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to create purchase transaction:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // 确认购买
  async confirmPurchase(transactionId: number, paypalTransactionId?: string): Promise<Transaction> {
    const client = await this.db.connect();
    
    try {
      await client.query('BEGIN');

      // 获取交易信息
      const transactionQuery = `
        SELECT t.*, a.creator_id, a.title
        FROM transactions t
        JOIN assets a ON t.asset_id = a.id
        WHERE t.id = $1 AND t.status = $2
      `;
      
      const transactionResult = await client.query(transactionQuery, [transactionId, TransactionStatus.PENDING]);
      
      if (transactionResult.rows.length === 0) {
        throw new TransactionNotFoundError();
      }

      const transaction = transactionResult.rows[0];

      // 如果是积分购买，扣除用户积分
      if (transaction.currency === Currency.POINTS) {
        const updateBalanceQuery = `
          UPDATE users 
          SET points_balance = points_balance - $1, updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
        `;
        await client.query(updateBalanceQuery, [transaction.amount_points, transaction.buyer_id]);
      }

      // 更新交易状态
      const updateTransactionQuery = `
        UPDATE transactions 
        SET status = $1, completed_at = CURRENT_TIMESTAMP, paypal_transaction_id = $2, updated_at = CURRENT_TIMESTAMP
        WHERE id = $3
        RETURNING *
      `;
      
      const updatedResult = await client.query(updateTransactionQuery, [
        TransactionStatus.COMPLETED,
        paypalTransactionId,
        transactionId
      ]);

      const updatedTransaction = updatedResult.rows[0];

      // 创建创作者收益记录
      const creatorEarnings = this.calculateCreatorEarnings(transaction.amount_usd || 0);
      
      if (creatorEarnings > 0) {
        await client.query(`
          INSERT INTO ledger_entries (transaction_id, user_id, amount, entry_type, status)
          VALUES ($1, $2, $3, $4, $5)
        `, [transactionId, transaction.creator_id, creatorEarnings, 'SALE_CREDIT', 'PENDING']);
      }

      await client.query('COMMIT');

      logBusinessOperation('TRANSACTION_CONFIRM', transaction.buyer_id, { 
        transactionId, 
        paypalTransactionId 
      });

      return {
        id: updatedTransaction.id,
        buyerId: updatedTransaction.buyer_id,
        assetId: updatedTransaction.asset_id,
        currency: updatedTransaction.currency,
        amountUsd: updatedTransaction.amount_usd,
        amountPoints: updatedTransaction.amount_points,
        paypalTransactionId: updatedTransaction.paypal_transaction_id,
        status: updatedTransaction.status,
        createdAt: updatedTransaction.created_at,
        completedAt: updatedTransaction.completed_at,
      };
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Failed to confirm purchase:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // 取消交易
  async cancelTransaction(transactionId: number): Promise<void> {
    try {
      const query = `
        UPDATE transactions 
        SET status = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2 AND status = $3
      `;
      
      const result = await this.db.query(query, [TransactionStatus.FAILED, transactionId, TransactionStatus.PENDING]);
      
      if (result.rowCount === 0) {
        throw new TransactionNotFoundError();
      }

      logBusinessOperation('TRANSACTION_CANCEL', 0, { transactionId });
    } catch (error) {
      logger.error('Failed to cancel transaction:', error);
      throw error;
    }
  }

  // 获取交易详情
  async getTransactionById(transactionId: number): Promise<Transaction> {
    try {
      const query = `
        SELECT t.*, a.title as asset_title, u.display_name as buyer_name
        FROM transactions t
        JOIN assets a ON t.asset_id = a.id
        JOIN users u ON t.buyer_id = u.id
        WHERE t.id = $1
      `;
      
      const result = await this.db.query(query, [transactionId]);
      
      if (result.rows.length === 0) {
        throw new TransactionNotFoundError();
      }

      const transaction = result.rows[0];
      
      return {
        id: transaction.id,
        buyerId: transaction.buyer_id,
        assetId: transaction.asset_id,
        currency: transaction.currency,
        amountUsd: transaction.amount_usd,
        amountPoints: transaction.amount_points,
        paypalTransactionId: transaction.paypal_transaction_id,
        status: transaction.status,
        createdAt: transaction.created_at,
        completedAt: transaction.completed_at,
        refundedAt: transaction.refunded_at,
      };
    } catch (error) {
      logger.error('Failed to get transaction by ID:', error);
      throw error;
    }
  }

  // 获取用户交易记录
  async getUserTransactions(userId: number, query: TransactionQuery) {
    try {
      const { page = 1, limit = 20, currency, status, startDate, endDate } = query;
      const offset = (page - 1) * limit;

      const conditions = ['t.buyer_id = $1'];
      const params: any[] = [userId];
      let paramIndex = 2;

      if (currency) {
        conditions.push(`t.currency = $${paramIndex}`);
        params.push(currency);
        paramIndex++;
      }

      if (status) {
        conditions.push(`t.status = $${paramIndex}`);
        params.push(status);
        paramIndex++;
      }

      if (startDate) {
        conditions.push(`t.created_at >= $${paramIndex}`);
        params.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        conditions.push(`t.created_at <= $${paramIndex}`);
        params.push(endDate);
        paramIndex++;
      }

      const whereClause = `WHERE ${conditions.join(' AND ')}`;

      const transactionsQuery = `
        SELECT 
          t.*, 
          a.title as asset_title,
          a.cover_image_url as asset_cover_image
        FROM transactions t
        JOIN assets a ON t.asset_id = a.id
        ${whereClause}
        ORDER BY t.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const countQuery = `
        SELECT COUNT(*) as total
        FROM transactions t
        ${whereClause}
      `;

      params.push(limit, offset);

      const [transactionsResult, countResult] = await Promise.all([
        this.db.query(transactionsQuery, params),
        this.db.query(countQuery, params.slice(0, -2))
      ]);

      const transactions = transactionsResult.rows;
      const total = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / limit);

      return {
        transactions,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Failed to get user transactions:', error);
      throw error;
    }
  }

  // 获取所有交易记录（管理员）
  async getAllTransactions(query: TransactionQuery) {
    try {
      const { page = 1, limit = 20, currency, status, startDate, endDate } = query;
      const offset = (page - 1) * limit;

      const conditions = [];
      const params: any[] = [];
      let paramIndex = 1;

      if (currency) {
        conditions.push(`t.currency = $${paramIndex}`);
        params.push(currency);
        paramIndex++;
      }

      if (status) {
        conditions.push(`t.status = $${paramIndex}`);
        params.push(status);
        paramIndex++;
      }

      if (startDate) {
        conditions.push(`t.created_at >= $${paramIndex}`);
        params.push(startDate);
        paramIndex++;
      }

      if (endDate) {
        conditions.push(`t.created_at <= $${paramIndex}`);
        params.push(endDate);
        paramIndex++;
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      const transactionsQuery = `
        SELECT 
          t.*, 
          a.title as asset_title,
          u.display_name as buyer_name,
          c.display_name as creator_name
        FROM transactions t
        JOIN assets a ON t.asset_id = a.id
        JOIN users u ON t.buyer_id = u.id
        JOIN users c ON a.creator_id = c.id
        ${whereClause}
        ORDER BY t.created_at DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const countQuery = `
        SELECT COUNT(*) as total
        FROM transactions t
        ${whereClause}
      `;

      params.push(limit, offset);

      const [transactionsResult, countResult] = await Promise.all([
        this.db.query(transactionsQuery, params),
        this.db.query(countQuery, params.slice(0, -2))
      ]);

      const transactions = transactionsResult.rows;
      const total = parseInt(countResult.rows[0].total);
      const totalPages = Math.ceil(total / limit);

      return {
        transactions,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Failed to get all transactions:', error);
      throw error;
    }
  }

  // 更新交易状态
  async updateTransactionStatus(transactionId: number, status: TransactionStatus): Promise<void> {
    try {
      const query = `
        UPDATE transactions 
        SET status = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `;
      
      const result = await this.db.query(query, [status, transactionId]);
      
      if (result.rowCount === 0) {
        throw new TransactionNotFoundError();
      }

      logBusinessOperation('TRANSACTION_STATUS_UPDATE', 0, { transactionId, status });
    } catch (error) {
      logger.error('Failed to update transaction status:', error);
      throw error;
    }
  }

  // 私有方法：计算创作者收益
  private calculateCreatorEarnings(amountUsd: number): number {
    // 平台抽成20%，创作者获得80%
    const platformFeeRate = 0.2;
    return amountUsd * (1 - platformFeeRate);
  }
}

export default TransactionService;