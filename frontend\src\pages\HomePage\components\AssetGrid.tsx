import React, { useCallback, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  Avatar,
  Chip,
  IconButton,
  Skeleton,
  CircularProgress,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Favorite,
  FavoriteBorder,
  Download,
  Visibility,
  Star,
  AttachMoney,
} from '@mui/icons-material';
import { FixedSizeGrid as Grid } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import AutoSizer from 'react-virtualized-auto-sizer';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Asset, AssetSearchParams, PaginationParams } from '../../../types';

// 扩展的搜索参数类型
type ExtendedSearchParams = AssetSearchParams & PaginationParams;

interface AssetGridProps {
  assets: Asset[];
  loading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
  searchQuery: string;
  currentFilters: ExtendedSearchParams;
  onFiltersChange: (filters: ExtendedSearchParams) => void;
}

const AssetGrid: React.FC<AssetGridProps> = ({
  assets,
  loading,
  hasMore,
  onLoadMore,
  searchQuery,
  currentFilters,
  onFiltersChange
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  
  const gridRef = useRef<any>(null);

  // 响应式列数
  const getColumnCount = () => {
    if (isMobile) return 2;
    if (isTablet) return 3;
    return 4;
  };

  const columnCount = getColumnCount();
  const itemWidth = isMobile ? 180 : 220;
  const itemHeight = isMobile ? 280 : 320;

  // 排序选项
  const sortOptions = [
    { value: 'newest', label: t('common.sort.newest') },
    { value: 'oldest', label: t('common.sort.oldest') },
    { value: 'popular', label: t('common.sort.popular') },
    { value: 'trending', label: t('common.sort.trending') },
    { value: 'price_asc', label: t('common.sort.price_asc') },
    { value: 'price_desc', label: t('common.sort.price_desc') },
    { value: 'rating', label: t('common.sort.rating') }
  ];

  const handleAssetClick = (assetId: string) => {
    navigate(`/assets/${assetId}`);
  };

  const handleCreatorClick = (creatorId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    navigate(`/creators/${creatorId}`);
  };

  const handleLikeToggle = (assetId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    // 实现点赞功能
    console.log('Toggle like for asset:', assetId);
  };

  const handleSortChange = (sortBy: string) => {
    onFiltersChange({ ...currentFilters, sortBy: sortBy as any });
  };

  const formatPrice = (price: number, currency: string) => {
    if (price === 0) {
      return t('common.free');
    }
    return currency === 'USD' ? `$${price}` : `${price} ${currency}`;
  };

  const formatNumber = (num: number): string => {
    if (num >= 10000) {
      return `${(num / 10000).toFixed(1)}万`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  // 检查项目是否已加载
  const isItemLoaded = useCallback((index: number) => {
    return !!assets[index];
  }, [assets]);

  // 加载更多项目
  const loadMoreItems = useCallback(() => {
    if (!loading && hasMore) {
      onLoadMore();
    }
  }, [loading, hasMore, onLoadMore]);

  // 资源卡片组件
  const AssetCard: React.FC<{ asset: Asset; style: React.CSSProperties }> = ({ asset, style }) => (
    <div style={style}>
      <Card
        sx={{
          height: itemHeight - 16,
          m: 1,
          display: 'flex',
          flexDirection: 'column',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[6]
          }
        }}
        onClick={() => handleAssetClick(asset.id)}
      >
        {/* 缩略图区域 */}
        <Box sx={{ position: 'relative' }}>
          <CardMedia
            component="img"
            height={isMobile ? 100 : 120}
            image={asset.thumbnailUrl || '/images/placeholder-asset.jpg'}
            alt={asset.title}
            sx={{
              objectFit: 'cover',
              backgroundColor: 'grey.100'
            }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/images/placeholder-asset.jpg';
            }}
          />
          
          {/* 价格标签 */}
          <Chip
            label={formatPrice(asset.price, asset.currency)}
            size="small"
            color={asset.isFree ? 'success' : 'primary'}
            sx={{
              position: 'absolute',
              top: 4,
              right: 4,
              fontSize: '0.7rem',
              height: 20,
              backgroundColor: asset.isFree ? 'success.main' : 'primary.main',
              color: 'white'
            }}
          />

          {/* 类型标签 */}
          <Chip
            label={asset.type}
            size="small"
            variant="outlined"
            sx={{
              position: 'absolute',
              top: 4,
              left: 4,
              fontSize: '0.6rem',
              height: 18,
              backgroundColor: 'rgba(255,255,255,0.9)'
            }}
          />

          {/* 点赞按钮 */}
          <IconButton
            size="small"
            sx={{
              position: 'absolute',
              bottom: 4,
              right: 4,
              backgroundColor: 'rgba(255,255,255,0.9)',
              width: 24,
              height: 24,
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,1)'
              }
            }}
            onClick={(e) => handleLikeToggle(asset.id, e)}
          >
            {asset.stats?.likes > 0 ? (
              <Favorite sx={{ fontSize: 14, color: 'error.main' }} />
            ) : (
              <FavoriteBorder sx={{ fontSize: 14 }} />
            )}
          </IconButton>
        </Box>

        {/* 内容区域 */}
        <CardContent sx={{ flexGrow: 1, p: 1.5, pb: 1 }}>
          <Typography
            variant="subtitle2"
            component="h3"
            sx={{
              fontWeight: 'bold',
              mb: 1,
              fontSize: isMobile ? '0.8rem' : '0.875rem',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              lineHeight: 1.2
            }}
          >
            {asset.title}
          </Typography>

          {/* 创作者信息 */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              mb: 1,
              cursor: 'pointer'
            }}
            onClick={(e) => handleCreatorClick(asset.creatorId, e)}
          >
            <Avatar
              src={asset.creator?.avatar}
              sx={{ width: 16, height: 16, mr: 0.5 }}
            >
              {asset.creator?.firstName?.charAt(0)}
            </Avatar>
            <Typography 
              variant="caption" 
              color="text.secondary" 
              noWrap
              sx={{ fontSize: '0.7rem' }}
            >
              {asset.creator?.firstName} {asset.creator?.lastName}
            </Typography>
          </Box>

          {/* 统计信息 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.25 }}>
              <Download sx={{ fontSize: 10, color: 'text.secondary' }} />
              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.65rem' }}>
                {formatNumber(asset.stats?.downloads || 0)}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.25 }}>
              <Visibility sx={{ fontSize: 10, color: 'text.secondary' }} />
              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.65rem' }}>
                {formatNumber(asset.stats?.views || 0)}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.25 }}>
              <Star sx={{ fontSize: 10, color: 'warning.main' }} />
              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.65rem' }}>
                {asset.stats?.avgRating?.toFixed(1) || '0.0'}
              </Typography>
            </Box>
          </Box>

          {/* 标签 */}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.25 }}>
            {asset.tags?.slice(0, 2).map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                size="small"
                variant="outlined"
                sx={{
                  fontSize: '0.6rem',
                  height: 16,
                  '& .MuiChip-label': {
                    px: 0.5
                  }
                }}
              />
            ))}
            {asset.tags?.length > 2 && (
              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.6rem' }}>
                +{asset.tags.length - 2}
              </Typography>
            )}
          </Box>
        </CardContent>

        {/* 操作区域 */}
        <CardActions sx={{ p: 1.5, pt: 0 }}>
          <Button
            size="small"
            variant="contained"
            fullWidth
            startIcon={asset.isFree ? <Download /> : <AttachMoney />}
            sx={{ 
              fontSize: '0.7rem',
              height: 28
            }}
            onClick={(e) => {
              e.stopPropagation();
              // 处理下载或购买
              console.log('Download/Purchase asset:', asset.id);
            }}
          >
            {asset.isFree ? t('common.download') : t('common.purchase')}
          </Button>
        </CardActions>
      </Card>
    </div>
  );

  // 骨架屏组件
  const SkeletonCard: React.FC<{ style: React.CSSProperties }> = ({ style }) => (
    <div style={style}>
      <Card sx={{ height: itemHeight - 16, m: 1, display: 'flex', flexDirection: 'column' }}>
        <Skeleton variant="rectangular" height={isMobile ? 100 : 120} />
        <CardContent sx={{ flexGrow: 1, p: 1.5 }}>
          <Skeleton variant="text" height={20} sx={{ mb: 1 }} />
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Skeleton variant="circular" width={16} height={16} sx={{ mr: 0.5 }} />
            <Skeleton variant="text" width="60%" />
          </Box>
          <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
            <Skeleton variant="text" width="25%" />
            <Skeleton variant="text" width="25%" />
            <Skeleton variant="text" width="25%" />
          </Box>
          <Box sx={{ display: 'flex', gap: 0.25 }}>
            <Skeleton variant="rounded" width={30} height={16} />
            <Skeleton variant="rounded" width={35} height={16} />
          </Box>
        </CardContent>
        <CardActions sx={{ p: 1.5, pt: 0 }}>
          <Skeleton variant="rounded" width="100%" height={28} />
        </CardActions>
      </Card>
    </div>
  );

  // 网格项目渲染器
  const Cell = ({ columnIndex, rowIndex, style }: any) => {
    const index = rowIndex * columnCount + columnIndex;
    const asset = assets[index];

    if (!asset) {
      return <SkeletonCard style={style} />;
    }

    return <AssetCard asset={asset} style={style} />;
  };

  const rowCount = Math.ceil((assets.length + (hasMore ? columnCount : 0)) / columnCount);

  return (
    <Box sx={{ flexGrow: 1, p: 2 }}>
      {/* 标题和控制栏 */}
      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="h5" component="h2" sx={{ fontWeight: 'bold' }}>
            {searchQuery ? 
              t('home.assets.search_results', { query: searchQuery }) : 
              t('home.assets.title')
            }
          </Typography>
          
          {/* 排序控制 */}
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>{t('common.sort.label')}</InputLabel>
            <Select
              value={currentFilters.sortBy || 'newest'}
              label={t('common.sort.label')}
              onChange={(e) => handleSortChange(e.target.value)}
            >
              {sortOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <Typography variant="body2" color="text.secondary">
          {assets.length > 0 ? 
            t('home.assets.count', { count: assets.length }) : 
            t('home.assets.empty')
          }
        </Typography>
      </Box>

      {/* 虚拟化网格 */}
      <Box sx={{ height: 'calc(100vh - 400px)', minHeight: 400 }}>
        <AutoSizer>
          {({ height, width }: { height: number; width: number }) => (
            <InfiniteLoader
              isItemLoaded={isItemLoaded}
              itemCount={assets.length + (hasMore ? columnCount : 0)}
              loadMoreItems={loadMoreItems}
            >
              {({ onItemsRendered, ref }) => (
                <Grid
                  ref={(grid) => {
                    gridRef.current = grid;
                    ref(grid);
                  }}
                  columnCount={columnCount}
                  columnWidth={itemWidth}
                  height={height}
                  rowCount={rowCount}
                  rowHeight={itemHeight}
                  width={width}
                  onItemsRendered={({
                    visibleRowStartIndex,
                    visibleRowStopIndex,
                    visibleColumnStartIndex,
                    visibleColumnStopIndex,
                  }) => {
                    onItemsRendered({
                      overscanStartIndex: visibleRowStartIndex * columnCount + visibleColumnStartIndex,
                      overscanStopIndex: visibleRowStopIndex * columnCount + visibleColumnStopIndex,
                      visibleStartIndex: visibleRowStartIndex * columnCount + visibleColumnStartIndex,
                      visibleStopIndex: visibleRowStopIndex * columnCount + visibleColumnStopIndex,
                    });
                  }}
                >
                  {Cell}
                </Grid>
              )}
            </InfiniteLoader>
          )}
        </AutoSizer>
      </Box>

      {/* 加载指示器 */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};

export default AssetGrid;
