import React, { useState } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Avatar,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tab,
  Tabs,
  LinearProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  useTheme,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as MoneyIcon,
  ShoppingCart as ShoppingCartIcon,
  Receipt as ReceiptIcon,
  Visibility as VisibilityIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Payment as PaymentIcon,
  AccountBalance as AccountBalanceIcon,
  CreditCard as CreditCardIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

interface Transaction {
  id: string;
  type: 'purchase' | 'sale' | 'refund' | 'withdrawal';
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  date: string;
  assetName: string;
  buyer: string;
  seller: string;
  paymentMethod: string;
  commission: number;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div role="tabpanel" hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const TradingCenter: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [selectedTab, setSelectedTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);

  // 模拟交易数据
  const transactions: Transaction[] = [
    {
      id: '1',
      type: 'purchase',
      amount: 299,
      status: 'completed',
      date: '2024-01-15 10:30:00',
      assetName: 'AI生成的现代建筑渲染图',
      buyer: '李小明',
      seller: '张设计师',
      paymentMethod: '支付宝',
      commission: 29.9,
    },
    {
      id: '2',
      type: 'sale',
      amount: 149,
      status: 'pending',
      date: '2024-01-15 09:15:00',
      assetName: '科技感UI图标集',
      buyer: '王大华',
      seller: 'UI设计师',
      paymentMethod: '微信支付',
      commission: 14.9,
    },
    {
      id: '3',
      type: 'refund',
      amount: -199,
      status: 'completed',
      date: '2024-01-14 16:45:00',
      assetName: '商业摄影素材包',
      buyer: '刘先生',
      seller: '摄影师',
      paymentMethod: '银行卡',
      commission: -19.9,
    },
  ];

  const salesData = [
    { period: '今日', sales: 12, revenue: 3450, growth: 15.5 },
    { period: '本周', sales: 89, revenue: 24670, growth: 8.2 },
    { period: '本月', sales: 345, revenue: 95280, growth: 23.1 },
    { period: '本年', sales: 1234, revenue: 456780, growth: 34.5 },
  ];

  const recentOrders = [
    {
      id: '1',
      asset: 'AI生成头像包',
      buyer: '张三',
      amount: 99,
      status: 'completed',
      time: '2分钟前',
    },
    {
      id: '2',
      asset: '3D建筑模型',
      buyer: '李四',
      amount: 199,
      status: 'pending',
      time: '5分钟前',
    },
    {
      id: '3',
      asset: '背景音乐合集',
      buyer: '王五',
      amount: 79,
      status: 'completed',
      time: '10分钟前',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'pending':
        return '待处理';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      default:
        return status;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'purchase':
        return <ShoppingCartIcon />;
      case 'sale':
        return <MoneyIcon />;
      case 'refund':
        return <ReceiptIcon />;
      case 'withdrawal':
        return <AccountBalanceIcon />;
      default:
        return <PaymentIcon />;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'purchase':
        return '购买';
      case 'sale':
        return '销售';
      case 'refund':
        return '退款';
      case 'withdrawal':
        return '提现';
      default:
        return type;
    }
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold' }}>
        交易中心
      </Typography>

      {/* 销售统计卡片 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {salesData.map((item, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h6" color="text.secondary">
                    {item.period}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {item.growth > 0 ? (
                      <TrendingUpIcon color="success" />
                    ) : (
                      <TrendingDownIcon color="error" />
                    )}
                    <Typography
                      variant="caption"
                      color={item.growth > 0 ? 'success.main' : 'error.main'}
                      sx={{ ml: 0.5 }}
                    >
                      {item.growth > 0 ? '+' : ''}{item.growth}%
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="h4" sx={{ mb: 1, fontWeight: 'bold' }}>
                  {item.sales}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  销售订单
                </Typography>
                <Typography variant="h6" color="primary" sx={{ mt: 1 }}>
                  ¥{item.revenue.toLocaleString()}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* 标签页 */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={selectedTab} onChange={(e, v) => setSelectedTab(v)}>
            <Tab label="交易概览" />
            <Tab label="交易记录" />
            <Tab label="财务报表" />
            <Tab label="支付管理" />
          </Tabs>
        </Box>

        <TabPanel value={selectedTab} index={0}>
          {/* 交易概览 */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Typography variant="h6" gutterBottom>
                最近订单
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>资产名称</TableCell>
                      <TableCell>购买者</TableCell>
                      <TableCell align="right">金额</TableCell>
                      <TableCell>状态</TableCell>
                      <TableCell>时间</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentOrders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell>{order.asset}</TableCell>
                        <TableCell>{order.buyer}</TableCell>
                        <TableCell align="right">¥{order.amount}</TableCell>
                        <TableCell>
                          <Chip
                            label={getStatusText(order.status)}
                            color={getStatusColor(order.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>{order.time}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>

            <Grid item xs={12} md={4}>
              <Typography variant="h6" gutterBottom>
                快速操作
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="contained"
                  startIcon={<DownloadIcon />}
                  fullWidth
                >
                  导出交易记录
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<AssessmentIcon />}
                  fullWidth
                >
                  生成财务报表
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<AccountBalanceIcon />}
                  fullWidth
                >
                  提现管理
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<CreditCardIcon />}
                  fullWidth
                >
                  支付设置
                </Button>
              </Box>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={selectedTab} index={1}>
          {/* 交易记录 */}
          <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}>
            <TextField
              placeholder="搜索交易记录..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ flex: 1 }}
            />
            <FormControl sx={{ minWidth: 120 }}>
              <InputLabel>状态</InputLabel>
              <Select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                label="状态"
              >
                <MenuItem value="all">全部</MenuItem>
                <MenuItem value="completed">已完成</MenuItem>
                <MenuItem value="pending">待处理</MenuItem>
                <MenuItem value="failed">失败</MenuItem>
                <MenuItem value="cancelled">已取消</MenuItem>
              </Select>
            </FormControl>
          </Box>

          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>交易ID</TableCell>
                  <TableCell>类型</TableCell>
                  <TableCell>资产名称</TableCell>
                  <TableCell>买家/卖家</TableCell>
                  <TableCell align="right">金额</TableCell>
                  <TableCell>状态</TableCell>
                  <TableCell>时间</TableCell>
                  <TableCell>操作</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>{transaction.id}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {getTypeIcon(transaction.type)}
                        <Typography variant="body2" sx={{ ml: 1 }}>
                          {getTypeText(transaction.type)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{transaction.assetName}</TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          买家: {transaction.buyer}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          卖家: {transaction.seller}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Typography
                        variant="body2"
                        color={transaction.amount > 0 ? 'success.main' : 'error.main'}
                        fontWeight="medium"
                      >
                        {transaction.amount > 0 ? '+' : ''}¥{Math.abs(transaction.amount)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusText(transaction.status)}
                        color={getStatusColor(transaction.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{transaction.date}</TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => setSelectedTransaction(transaction)}
                      >
                        <VisibilityIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={selectedTab} index={2}>
          {/* 财务报表 */}
          <Typography variant="h6" gutterBottom>
            财务报表功能开发中...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            此功能将包含详细的财务分析和报表生成功能。
          </Typography>
        </TabPanel>

        <TabPanel value={selectedTab} index={3}>
          {/* 支付管理 */}
          <Typography variant="h6" gutterBottom>
            支付管理功能开发中...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            此功能将包含支付方式配置、手续费设置等功能。
          </Typography>
        </TabPanel>
      </Card>

      {/* 交易详情对话框 */}
      <Dialog
        open={Boolean(selectedTransaction)}
        onClose={() => setSelectedTransaction(null)}
        maxWidth="md"
        fullWidth
      >
        {selectedTransaction && (
          <>
            <DialogTitle>
              交易详情 - {selectedTransaction.id}
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    交易类型
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {getTypeText(selectedTransaction.type)}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    交易金额
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    ¥{Math.abs(selectedTransaction.amount)}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    资产名称
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedTransaction.assetName}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    支付方式
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedTransaction.paymentMethod}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    买家
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedTransaction.buyer}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    卖家
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedTransaction.seller}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    手续费
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    ¥{Math.abs(selectedTransaction.commission)}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="body2" color="text.secondary">
                    交易时间
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {selectedTransaction.date}
                  </Typography>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setSelectedTransaction(null)}>
                关闭
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default TradingCenter;