export declare const config: {
    app: {
        env: string;
        port: number;
        name: string;
        version: string;
        corsOrigins: string[];
    };
    database: {
        url: string;
        poolSize: number;
        ssl: boolean;
    };
    redis: {
        url: string;
        password: string;
        db: number;
    };
    aws: {
        region: string;
        accessKeyId: string;
        secretAccessKey: string;
        s3: {
            privateBucket: string;
            publicBucket: string;
        };
        ses: {
            region: string;
            fromEmail: string;
        };
    };
    jwt: {
        secret: string;
        expiresIn: string;
        refreshExpiresIn: string;
    };
    paypal: {
        clientId: string;
        clientSecret: string;
        sandbox: boolean;
        webhookId: string;
    };
    oauth: {
        google: {
            clientId: string;
            clientSecret: string;
            redirectUri: string;
        };
        github: {
            clientId: string;
            clientSecret: string;
            redirectUri: string;
        };
    };
    email: {
        host: string;
        port: number;
        secure: boolean;
        auth: {
            user: string;
            pass: string;
        };
        from: string;
    };
    upload: {
        maxFileSize: number;
        uploadUrlExpiresIn: number;
        downloadUrlExpiresIn: number;
        allowedMimeTypes: string[];
    };
    cache: {
        defaultTtl: number;
        assetsTtl: number;
        userProfileTtl: number;
        transactionsTtl: number;
        systemConfigsTtl: number;
    };
    rateLimit: {
        windowMs: number;
        maxRequests: number;
        sensitiveOperations: {
            windowMs: number;
            maxRequests: number;
        };
    };
    logging: {
        level: string;
        file: string;
        maxSize: string;
        maxFiles: number;
        enableConsole: boolean;
    };
    system: {
        pointsRate: number;
        commission: {
            individual: {
                base: number;
                increment: number;
                max: number;
            };
            enterprise: {
                base: number;
                increment: number;
                max: number;
            };
        };
        ledger: {
            pendingDays: number;
        };
        withdrawal: {
            minAmount: number;
        };
    };
    monitoring: {
        enableMetrics: boolean;
        metricsInterval: number;
    };
    frontend: {
        url: string;
        loginSuccessUrl: string;
        loginFailureUrl: string;
    };
    swagger: {
        title: string;
        description: string;
        version: string;
        enabled: boolean;
    };
};
export default config;
//# sourceMappingURL=index.d.ts.map