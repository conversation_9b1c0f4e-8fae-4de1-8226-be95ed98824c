# ===========================================
# AIGC Service Hub MVP 1.0 - 测试环境配置
# ===========================================

# 应用环境配置
NODE_ENV=test
APP_NAME=AIGC Service Hub (Test)
API_VERSION=v1

# 服务端口配置
BACKEND_PORT=3000
FRONTEND_PORT=3001
HTTP_PORT=80
HTTPS_PORT=443

# 数据库配置
POSTGRES_DB=aigc_service_hub_test
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
POSTGRES_PORT=5432
DATABASE_POOL_SIZE=5

# Redis配置
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1

# JWT配置
JWT_SECRET=test-jwt-secret-key-for-testing-only
JWT_EXPIRES_IN=1h
REFRESH_TOKEN_EXPIRES_IN=2h

# AWS配置（测试环境使用模拟服务）
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=test-access-key
AWS_SECRET_ACCESS_KEY=test-secret-key
S3_PRIVATE_BUCKET=test-private-bucket
S3_PUBLIC_BUCKET=test-public-bucket

# PayPal配置（测试环境）
PAYPAL_CLIENT_ID=test-paypal-client-id
PAYPAL_CLIENT_SECRET=test-paypal-client-secret
PAYPAL_SANDBOX=true
PAYPAL_WEBHOOK_ID=test-paypal-webhook-id

# OAuth配置（测试环境）
GOOGLE_CLIENT_ID=test-google-client-id
GOOGLE_CLIENT_SECRET=test-google-client-secret
GITHUB_CLIENT_ID=test-github-client-id
GITHUB_CLIENT_SECRET=test-github-client-secret

# 邮件配置（使用MailHog）
FROM_EMAIL=<EMAIL>
SMTP_HOST=mailhog-test
SMTP_PORT=1025
SMTP_USER=test-smtp-user
SMTP_PASSWORD=test-smtp-password
AWS_SES_REGION=us-west-2

# 应用URL配置
FRONTEND_URL=http://localhost:3003
BACKEND_URL=http://localhost:3002
DOMAIN=localhost

# React应用配置
REACT_APP_API_URL=http://localhost:3002/api/v1
REACT_APP_WS_URL=ws://localhost:3002
REACT_APP_PAYPAL_CLIENT_ID=test-paypal-client-id
REACT_APP_GOOGLE_CLIENT_ID=test-google-client-id
REACT_APP_GITHUB_CLIENT_ID=test-github-client-id
REACT_APP_AWS_REGION=us-west-2
REACT_APP_S3_PUBLIC_BUCKET=test-public-bucket

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_URL_EXPIRES_IN=300
DOWNLOAD_URL_EXPIRES_IN=120

# 缓存配置
CACHE_DEFAULT_TTL=10
CACHE_ASSETS_TTL=10
CACHE_USER_PROFILE_TTL=10

# 速率限制配置（测试环境宽松）
RATE_LIMIT_WINDOW_MS=10000
RATE_LIMIT_MAX_REQUESTS=10000

# 日志配置
LOG_LEVEL=error
LOG_FILE=logs/test.log
LOG_MAX_SIZE=1m
LOG_MAX_FILES=1

# 监控配置
ENABLE_METRICS=false
METRICS_INTERVAL=10000

# 系统配置
POINTS_RATE=100
COMMISSION_INDIVIDUAL_BASE=5
COMMISSION_INDIVIDUAL_INCREMENT=5
COMMISSION_INDIVIDUAL_MAX=50
COMMISSION_ENTERPRISE_BASE=8
COMMISSION_ENTERPRISE_INCREMENT=8
COMMISSION_ENTERPRISE_MAX=56

# 财务配置
LEDGER_PENDING_DAYS=0
MIN_WITHDRAWAL_AMOUNT=0.01

# 工具服务端口配置
ADMINER_PORT=8080
REDIS_COMMANDER_PORT=8081
MAILHOG_WEB_PORT=8025
MAILHOG_SMTP_PORT=1025
DOCS_PORT=8082
WEBPACK_DEV_PORT=8083

# 测试环境端口配置
POSTGRES_TEST_PORT=5433
REDIS_TEST_PORT=6380
BACKEND_TEST_PORT=3002
FRONTEND_TEST_PORT=3003
MAILHOG_TEST_WEB_PORT=8026
MAILHOG_TEST_SMTP_PORT=1026

# 监控服务配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3004
POSTGRES_EXPORTER_PORT=9187

# 备份配置
BACKUP_SCHEDULE=0 6 * * *
BACKUP_RETENTION_DAYS=3

# SSL配置（测试环境不使用）
SSL_CERT_PATH=/etc/nginx/ssl/server.crt
SSL_KEY_PATH=/etc/nginx/ssl/server.key
SSL_DHPARAM_PATH=/etc/nginx/ssl/dhparam.pem

# 安全配置
ENABLE_HTTPS=false
FORCE_HTTPS=false
HSTS_MAX_AGE=0
SECURITY_HEADERS=false

# 性能配置
NGINX_WORKER_PROCESSES=1
NGINX_WORKER_CONNECTIONS=256
POSTGRES_MAX_CONNECTIONS=20
REDIS_MAX_MEMORY=64mb

# 开发工具配置
ENABLE_SWAGGER=false
ENABLE_CORS=true
ENABLE_MORGAN_LOGGING=false
ENABLE_DEBUG_MODE=false

# 测试配置
TEST_TIMEOUT=30000
TEST_PARALLEL=true
TEST_COVERAGE=true
TEST_WATCH=false
TEST_VERBOSE=false

# E2E测试配置
E2E_HEADLESS=true
E2E_SLOW_MO=0
E2E_TIMEOUT=30000
E2E_RETRY=2
E2E_BROWSER=chromium

# 性能测试配置
LOAD_TEST_DURATION=60s
LOAD_TEST_VUS=10
LOAD_TEST_THRESHOLD_AVG=1000
LOAD_TEST_THRESHOLD_P95=2000

# 安全测试配置
SECURITY_SCAN_TIMEOUT=300
SECURITY_SCAN_LEVEL=Medium
SECURITY_SCAN_FORMAT=json

# 数据库测试配置
DB_TEST_TRANSACTIONS=true
DB_TEST_ISOLATION=true
DB_TEST_CLEANUP=true

# 模拟服务配置
MOCK_PAYPAL_API=true
MOCK_AWS_S3=true
MOCK_EMAIL_SERVICE=true
MOCK_OAUTH_PROVIDERS=true