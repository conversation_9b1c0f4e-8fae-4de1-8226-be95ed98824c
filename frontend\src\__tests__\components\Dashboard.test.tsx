import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { configureStore } from '@reduxjs/toolkit';
import Dashboard from '../../pages/Dashboard/Dashboard';
import { theme } from '../../theme';

// Mock API calls
jest.mock('../../services/api', () => ({
  getDashboardStats: jest.fn(),
  getRecentAssets: jest.fn(),
  getRecentTransactions: jest.fn()
}));

const mockStore = configureStore({
  reducer: {
    auth: (state = { user: null, isAuthenticated: false }, action) => state,
    dashboard: (state = { stats: null, loading: false }, action) => state
  }
});

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {component}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('Dashboard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders dashboard title', () => {
    renderWithProviders(<Dashboard />);
    
    expect(screen.getByText('仪表板')).toBeInTheDocument();
  });

  test('displays welcome message', () => {
    renderWithProviders(<Dashboard />);
    
    expect(screen.getByText(/欢迎/)).toBeInTheDocument();
  });

  test('shows loading state initially', () => {
    renderWithProviders(<Dashboard />);
    
    // Should show some loading indicator or skeleton
    expect(screen.getByText('概览')).toBeInTheDocument();
  });

  test('renders stats cards when data is loaded', async () => {
    const mockStats = {
      totalAssets: 25,
      totalSales: 150,
      totalRevenue: 2500.50,
      pendingWithdrawals: 500.00
    };

    // Mock the API response
    const { getDashboardStats } = require('../../services/api');
    getDashboardStats.mockResolvedValue(mockStats);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('25')).toBeInTheDocument(); // Total assets
      expect(screen.getByText('150')).toBeInTheDocument(); // Total sales
      expect(screen.getByText('$2,500.50')).toBeInTheDocument(); // Total revenue
      expect(screen.getByText('$500.00')).toBeInTheDocument(); // Pending withdrawals
    });
  });

  test('displays recent assets section', async () => {
    const mockAssets = [
      {
        id: '1',
        title: 'AI Model v1.0',
        assetType: 'MODEL',
        priceUsd: 49.99,
        status: 'PUBLISHED',
        createdAt: '2025-01-01T00:00:00Z'
      },
      {
        id: '2',
        title: 'LoRA Training Set',
        assetType: 'LORA',
        priceUsd: 29.99,
        status: 'DRAFT',
        createdAt: '2025-01-02T00:00:00Z'
      }
    ];

    const { getRecentAssets } = require('../../services/api');
    getRecentAssets.mockResolvedValue(mockAssets);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('AI Model v1.0')).toBeInTheDocument();
      expect(screen.getByText('LoRA Training Set')).toBeInTheDocument();
      expect(screen.getByText('$49.99')).toBeInTheDocument();
      expect(screen.getByText('$29.99')).toBeInTheDocument();
    });
  });

  test('displays recent transactions section', async () => {
    const mockTransactions = [
      {
        id: 'tx-1',
        assetTitle: 'AI Model v1.0',
        amount: 49.99,
        buyerEmail: '<EMAIL>',
        createdAt: '2025-01-01T12:00:00Z',
        status: 'COMPLETED'
      },
      {
        id: 'tx-2',
        assetTitle: 'LoRA Training Set',
        amount: 29.99,
        buyerEmail: '<EMAIL>',
        createdAt: '2025-01-01T14:00:00Z',
        status: 'PENDING'
      }
    ];

    const { getRecentTransactions } = require('../../services/api');
    getRecentTransactions.mockResolvedValue(mockTransactions);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('AI Model v1.0')).toBeInTheDocument();
      expect(screen.getByText('LoRA Training Set')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });

  test('handles API errors gracefully', async () => {
    const { getDashboardStats } = require('../../services/api');
    getDashboardStats.mockRejectedValue(new Error('API Error'));

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      // Should show error message or fallback UI
      expect(screen.getByText(/错误/)).toBeInTheDocument();
    });
  });

  test('shows empty state when no data available', async () => {
    const { getRecentAssets, getRecentTransactions } = require('../../services/api');
    getRecentAssets.mockResolvedValue([]);
    getRecentTransactions.mockResolvedValue([]);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText(/暂无资源/)).toBeInTheDocument();
      expect(screen.getByText(/暂无交易/)).toBeInTheDocument();
    });
  });

  test('navigates to asset management when clicking view all assets', async () => {
    renderWithProviders(<Dashboard />);

    const viewAllAssetsButton = screen.getByText('查看全部资源');
    expect(viewAllAssetsButton).toBeInTheDocument();
    expect(viewAllAssetsButton.closest('a')).toHaveAttribute('href', '/assets');
  });

  test('navigates to transaction history when clicking view all transactions', async () => {
    renderWithProviders(<Dashboard />);

    const viewAllTransactionsButton = screen.getByText('查看全部交易');
    expect(viewAllTransactionsButton).toBeInTheDocument();
    expect(viewAllTransactionsButton.closest('a')).toHaveAttribute('href', '/transactions');
  });

  test('displays correct asset type badges', async () => {
    const mockAssets = [
      {
        id: '1',
        title: 'AI Model',
        assetType: 'MODEL',
        priceUsd: 49.99,
        status: 'PUBLISHED'
      },
      {
        id: '2',
        title: 'LoRA Set',
        assetType: 'LORA',
        priceUsd: 29.99,
        status: 'DRAFT'
      },
      {
        id: '3',
        title: 'Workflow',
        assetType: 'WORKFLOW',
        priceUsd: 19.99,
        status: 'PUBLISHED'
      }
    ];

    const { getRecentAssets } = require('../../services/api');
    getRecentAssets.mockResolvedValue(mockAssets);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('模型')).toBeInTheDocument(); // MODEL badge
      expect(screen.getByText('LoRA')).toBeInTheDocument(); // LORA badge
      expect(screen.getByText('工作流')).toBeInTheDocument(); // WORKFLOW badge
    });
  });

  test('displays correct status indicators', async () => {
    const mockAssets = [
      {
        id: '1',
        title: 'Published Asset',
        assetType: 'MODEL',
        priceUsd: 49.99,
        status: 'PUBLISHED'
      },
      {
        id: '2',
        title: 'Draft Asset',
        assetType: 'LORA',
        priceUsd: 29.99,
        status: 'DRAFT'
      }
    ];

    const { getRecentAssets } = require('../../services/api');
    getRecentAssets.mockResolvedValue(mockAssets);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('已发布')).toBeInTheDocument(); // PUBLISHED status
      expect(screen.getByText('草稿')).toBeInTheDocument(); // DRAFT status
    });
  });

  test('formats currency correctly', async () => {
    const mockStats = {
      totalRevenue: 1234.56,
      pendingWithdrawals: 999.99
    };

    const { getDashboardStats } = require('../../services/api');
    getDashboardStats.mockResolvedValue(mockStats);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('$1,234.56')).toBeInTheDocument();
      expect(screen.getByText('$999.99')).toBeInTheDocument();
    });
  });

  test('formats dates correctly', async () => {
    const mockTransactions = [
      {
        id: 'tx-1',
        assetTitle: 'Test Asset',
        amount: 49.99,
        buyerEmail: '<EMAIL>',
        createdAt: '2025-01-15T10:30:00Z',
        status: 'COMPLETED'
      }
    ];

    const { getRecentTransactions } = require('../../services/api');
    getRecentTransactions.mockResolvedValue(mockTransactions);

    renderWithProviders(<Dashboard />);

    await waitFor(() => {
      // Should display formatted date
      expect(screen.getByText(/2025-01-15/)).toBeInTheDocument();
    });
  });
});
