export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: {
        code: string;
        message: string;
        details?: any;
    };
    pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
export declare enum UserRole {
    PERSONAL_CREATOR = "PERSONAL_CREATOR",
    ENTERPRISE_CREATOR = "ENTERPRISE_CREATOR",
    ADMIN = "ADMIN"
}
export interface User {
    id: number;
    email: string;
    displayName: string;
    userRole: UserRole;
    pointsBalance: number;
    oauthProvider?: string;
    oauthId?: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export interface JwtPayload {
    userId: number;
    email: string;
    userRole: UserRole;
    iat: number;
    exp: number;
}
export declare enum AssetType {
    MODEL = "MODEL",
    LORA = "LORA",
    WORKFLOW = "WORKFLOW",
    PROMPT = "PROMPT",
    TOOL = "TOOL"
}
export declare enum AssetStatus {
    DRAFT = "DRAFT",
    PUBLISHED = "PUBLISHED",
    ARCHIVED = "ARCHIVED"
}
export interface Asset {
    id: number;
    creatorId: number;
    title: string;
    description?: string;
    assetType: AssetType;
    priceUsd?: number;
    pricePoints?: number;
    s3FileKey: string;
    coverImageUrl?: string;
    status: AssetStatus;
    downloadCount: number;
    fileSize: number;
    createdAt: Date;
    publishedAt?: Date;
    updatedAt: Date;
}
export declare enum TagType {
    CATEGORY = "CATEGORY",
    STYLE = "STYLE"
}
export interface Tag {
    id: number;
    name: string;
    type: TagType;
    createdAt: Date;
}
export declare enum Currency {
    USD = "USD",
    POINTS = "POINTS"
}
export declare enum TransactionStatus {
    PENDING = "PENDING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    REFUNDED = "REFUNDED"
}
export interface Transaction {
    id: number;
    buyerId: number;
    assetId: number;
    currency: Currency;
    amountUsd?: number;
    amountPoints?: number;
    paypalTransactionId?: string;
    status: TransactionStatus;
    createdAt: Date;
    completedAt?: Date;
    refundedAt?: Date;
}
export declare enum LedgerEntryType {
    SALE_CREDIT = "SALE_CREDIT",
    PLATFORM_FEE = "PLATFORM_FEE",
    POINTS_PURCHASE = "POINTS_PURCHASE",
    POINTS_DEBIT = "POINTS_DEBIT"
}
export declare enum LedgerStatus {
    PENDING = "PENDING",
    AVAILABLE = "AVAILABLE",
    WITHDRAWN = "WITHDRAWN",
    REFUNDED = "REFUNDED"
}
export interface LedgerEntry {
    id: number;
    transactionId?: number;
    userId: number;
    amount: number;
    entryType: LedgerEntryType;
    status: LedgerStatus;
    createdAt: Date;
    clearedAt?: Date;
    withdrawnAt?: Date;
    notes?: string;
}
export declare enum WithdrawalStatus {
    PENDING = "PENDING",
    APPROVED = "APPROVED",
    REJECTED = "REJECTED",
    COMPLETED = "COMPLETED"
}
export interface WithdrawalRequest {
    id: number;
    userId: number;
    amount: number;
    paypalEmail: string;
    status: WithdrawalStatus;
    adminNotes?: string;
    createdAt: Date;
    processedAt?: Date;
    processedBy?: number;
    paypalPayoutId?: string;
    rejectionReason?: string;
}
export declare enum ConfigType {
    STRING = "STRING",
    NUMBER = "NUMBER",
    BOOLEAN = "BOOLEAN",
    JSON = "JSON"
}
export interface SystemConfig {
    id: number;
    configKey: string;
    configValue: string;
    configType: ConfigType;
    description?: string;
    createdAt: Date;
    updatedAt: Date;
}
export interface RegisterRequest {
    email: string;
    password: string;
    displayName: string;
    userRole: UserRole;
}
export interface LoginRequest {
    email: string;
    password: string;
}
export interface RefreshTokenRequest {
    refreshToken: string;
}
export interface CreateAssetRequest {
    title: string;
    description?: string;
    assetType: AssetType;
    priceUsd?: number;
    pricePoints?: number;
    categories: string[];
    styles: string[];
    coverImageUrl?: string;
}
export interface PurchaseRequest {
    assetId: number;
    currency: Currency;
}
export interface WithdrawalRequestBody {
    amount: number;
    paypalEmail: string;
}
export interface AuthResponse {
    accessToken: string;
    refreshToken: string;
    user: Omit<User, 'passwordHash'>;
}
export interface BalanceResponse {
    pointsBalance: number;
    availableBalance: number;
    pendingBalance: number;
}
export interface UploadUrlResponse {
    uploadUrl: string;
    fileKey: string;
    expiresAt: string;
}
export interface DownloadUrlResponse {
    downloadUrl: string;
    expiresAt: string;
}
export interface CommissionResult {
    platformAmount: number;
    creatorAmount: number;
    platformSharePercent: number;
    creatorSharePercent: number;
}
export declare enum Permission {
    READ_PUBLIC = "read:public",
    READ_PRIVATE = "read:private",
    WRITE_ASSET = "write:asset",
    ADMIN_PANEL = "admin:panel",
    FINANCE_WITHDRAW = "finance:withdraw"
}
export interface ApiError extends Error {
    statusCode: number;
    code: string;
    details?: any;
}
export interface PaginationQuery {
    page?: number;
    limit?: number;
}
export interface AssetQuery extends PaginationQuery {
    category?: string;
    style?: string;
    assetType?: AssetType;
    sortBy?: 'created_at' | 'price_usd' | 'download_count';
    sortOrder?: 'asc' | 'desc';
    search?: string;
}
export interface TransactionQuery extends PaginationQuery {
    currency?: Currency;
    status?: TransactionStatus;
    startDate?: Date;
    endDate?: Date;
}
export interface UserEarningsStats {
    totalEarnings: number;
    availableBalance: number;
    pendingBalance: number;
    withdrawnBalance: number;
    totalSales: number;
    totalAssets: number;
}
export interface AssetStats {
    totalSales: number;
    totalUsdRevenue: number;
    totalPointsRevenue: number;
    downloadCount: number;
}
export interface PlatformStats {
    totalUsers: number;
    totalCreators: number;
    totalAssets: number;
    publishedAssets: number;
    totalTransactions: number;
    totalUsdVolume: number;
    totalPointsVolume: number;
    totalPlatformFees: number;
}
export interface FileUploadConfirmation {
    fileKey: string;
    fileSize: number;
    fileName: string;
}
export interface PayPalPayment {
    id: string;
    state: string;
    amount: {
        total: string;
        currency: string;
    };
    payer: {
        payment_method: string;
        payer_info: {
            email: string;
        };
    };
}
export interface EmailTemplate {
    to: string;
    subject: string;
    html: string;
    text?: string;
}
export interface CacheOptions {
    ttl?: number;
    prefix?: string;
}
export interface DatabaseConfig {
    host: string;
    port: number;
    database: string;
    username: string;
    password: string;
    ssl?: boolean;
    poolSize?: number;
}
export interface HealthCheckResult {
    status: 'healthy' | 'unhealthy';
    timestamp: string;
    services: {
        database: boolean;
        redis: boolean;
        s3: boolean;
        paypal: boolean;
    };
    uptime: number;
    version: string;
}
declare global {
    namespace Express {
        interface Request {
            user?: User;
            file?: any;
        }
    }
}
//# sourceMappingURL=index.d.ts.map