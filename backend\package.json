{"name": "aigc-service-hub-backend", "version": "1.0.0", "description": "AIGC Service Hub MVP 1.0 后端API服务", "main": "dist/bootstrap.js", "scripts": {"build": "tsc && tsc-alias", "dev": "nodemon --exec ts-node -r tsconfig-paths/register src/main.ts", "start": "node -r tsconfig-paths/register dist/bootstrap.js", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:e2e": "jest --config jest-e2e.json", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "db:migrate": "ts-node src/database/migrate.ts", "db:seed": "ts-node src/database/seed.ts", "db:rollback": "ts-node src/database/rollback.ts", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:prod": "docker-compose -f docker-compose.prod.yml up -d", "docker:down": "docker-compose down", "cron:start": "ts-node src/workers/cron.ts", "swagger": "ts-node src/utils/swagger-generator.ts"}, "dependencies": {"@types/bcrypt": "^5.0.0", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/helmet": "^4.0.0", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.7", "@types/node": "^20.5.0", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "aws-sdk": "^2.1439.0", "axios": "^1.10.0", "bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.8.1", "helmet": "^7.0.0", "ioredis": "^5.3.2", "joi": "^17.9.2", "jsonwebtoken": "^9.0.1", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "nodemailer": "^6.9.4", "paypal-rest-sdk": "^1.8.1", "pg": "^8.11.3", "reflect-metadata": "^0.2.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.6", "winston": "^3.10.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node-cron": "^3.0.8", "@types/nodemailer": "^6.4.9", "@types/paypal-rest-sdk": "^1.7.6", "@types/pg": "^8.10.2", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.4.0", "eslint": "^8.47.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "prettier": "^3.0.1", "supertest": "^6.3.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.1", "tsc-alias": "^1.8.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["aigc", "service-hub", "api", "backend", "express", "typescript", "postgres", "redis", "paypal", "aws"], "author": "AIGC Service Hub Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/aigc-service-hub.git"}, "bugs": {"url": "https://github.com/your-org/aigc-service-hub/issues"}, "homepage": "https://github.com/your-org/aigc-service-hub#readme"}