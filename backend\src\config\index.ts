import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

export const config = {
  // 应用配置
  app: {
    env: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || '3000'),
    name: process.env.APP_NAME || 'AIGC Service Hub',
    version: process.env.API_VERSION || 'v1',
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3001'],
  },

  // 数据库配置
  database: {
    url: process.env.DATABASE_URL || 'postgresql://postgres:postgres123@localhost:5432/aigc_service_hub',
    poolSize: parseInt(process.env.DATABASE_POOL_SIZE || '20'),
    ssl: process.env.NODE_ENV === 'production',
  },

  // Redis配置
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB || '0'),
  },

  // AWS配置
  aws: {
    region: process.env.AWS_REGION || 'us-west-2',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
    s3: {
      privateBucket: process.env.S3_PRIVATE_BUCKET || 'aigc-private-assets',
      publicBucket: process.env.S3_PUBLIC_BUCKET || 'aigc-public-assets',
    },
    ses: {
      region: process.env.AWS_SES_REGION || 'us-west-2',
      fromEmail: process.env.FROM_EMAIL || '<EMAIL>',
    },
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-jwt-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '1d',
    refreshExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '7d',
  },

  // PayPal配置
  paypal: {
    clientId: process.env.PAYPAL_CLIENT_ID || '',
    clientSecret: process.env.PAYPAL_CLIENT_SECRET || '',
    sandbox: process.env.PAYPAL_SANDBOX === 'true',
    webhookId: process.env.PAYPAL_WEBHOOK_ID || '',
  },

  // OAuth配置
  oauth: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      redirectUri: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:3000/auth/google/callback',
    },
    github: {
      clientId: process.env.GITHUB_CLIENT_ID || '',
      clientSecret: process.env.GITHUB_CLIENT_SECRET || '',
      redirectUri: process.env.GITHUB_REDIRECT_URI || 'http://localhost:3000/auth/github/callback',
    },
  },

  // 邮件配置
  email: {
    host: process.env.SMTP_HOST || 'localhost',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER || '',
      pass: process.env.SMTP_PASSWORD || '',
    },
    from: process.env.FROM_EMAIL || '<EMAIL>',
  },

  // 文件上传配置
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '32212254720'), // 30GB
    uploadUrlExpiresIn: parseInt(process.env.UPLOAD_URL_EXPIRES_IN || '900'), // 15分钟
    downloadUrlExpiresIn: parseInt(process.env.DOWNLOAD_URL_EXPIRES_IN || '300'), // 5分钟
    allowedMimeTypes: [
      'application/zip',
      'application/x-zip-compressed',
      'application/x-rar-compressed',
      'application/x-7z-compressed',
      'application/gzip',
      'application/x-tar',
      'application/octet-stream',
    ],
  },

  // 缓存配置
  cache: {
    defaultTtl: parseInt(process.env.CACHE_DEFAULT_TTL || '300'),
    assetsTtl: parseInt(process.env.CACHE_ASSETS_TTL || '300'),
    userProfileTtl: parseInt(process.env.CACHE_USER_PROFILE_TTL || '600'),
    transactionsTtl: parseInt(process.env.CACHE_TRANSACTIONS_TTL || '60'),
    systemConfigsTtl: parseInt(process.env.CACHE_SYSTEM_CONFIGS_TTL || '3600'),
  },

  // 速率限制配置
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15分钟
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
    sensitiveOperations: {
      windowMs: parseInt(process.env.SENSITIVE_RATE_LIMIT_WINDOW_MS || '3600000'), // 1小时
      maxRequests: parseInt(process.env.SENSITIVE_RATE_LIMIT_MAX_REQUESTS || '10'),
    },
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || 'logs/app.log',
    maxSize: process.env.LOG_MAX_SIZE || '10m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES || '5'),
    enableConsole: process.env.NODE_ENV !== 'production',
  },

  // 系统配置
  system: {
    pointsRate: parseInt(process.env.POINTS_RATE || '100'), // 100 积分 = $1
    commission: {
      individual: {
        base: parseInt(process.env.COMMISSION_INDIVIDUAL_BASE || '5'),
        increment: parseInt(process.env.COMMISSION_INDIVIDUAL_INCREMENT || '5'),
        max: parseInt(process.env.COMMISSION_INDIVIDUAL_MAX || '50'),
      },
      enterprise: {
        base: parseInt(process.env.COMMISSION_ENTERPRISE_BASE || '8'),
        increment: parseInt(process.env.COMMISSION_ENTERPRISE_INCREMENT || '8'),
        max: parseInt(process.env.COMMISSION_ENTERPRISE_MAX || '56'),
      },
    },
    ledger: {
      pendingDays: parseInt(process.env.LEDGER_PENDING_DAYS || '7'),
    },
    withdrawal: {
      minAmount: parseFloat(process.env.MIN_WITHDRAWAL_AMOUNT || '10.00'),
    },
  },

  // 监控配置
  monitoring: {
    enableMetrics: process.env.ENABLE_METRICS === 'true',
    metricsInterval: parseInt(process.env.METRICS_INTERVAL || '60000'),
  },

  // 前端URL配置
  frontend: {
    url: process.env.FRONTEND_URL || 'http://localhost:3001',
    loginSuccessUrl: process.env.FRONTEND_LOGIN_SUCCESS_URL || 'http://localhost:3001/dashboard',
    loginFailureUrl: process.env.FRONTEND_LOGIN_FAILURE_URL || 'http://localhost:3001/login?error=oauth_failed',
  },

  // API文档配置
  swagger: {
    title: 'AIGC Service Hub API',
    description: 'API documentation for AIGC Service Hub MVP 1.0',
    version: '1.0.0',
    enabled: process.env.NODE_ENV !== 'production',
  },
};

// 验证必要的环境变量
const requiredEnvVars = [
  'JWT_SECRET',
  'DATABASE_URL',
  'AWS_ACCESS_KEY_ID',
  'AWS_SECRET_ACCESS_KEY',
  'S3_PRIVATE_BUCKET',
  'PAYPAL_CLIENT_ID',
  'PAYPAL_CLIENT_SECRET',
];

if (config.app.env === 'production') {
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      throw new Error(`Missing required environment variable: ${envVar}`);
    }
  }
}

export default config;