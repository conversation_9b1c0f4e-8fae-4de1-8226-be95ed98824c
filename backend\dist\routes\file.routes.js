"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const file_controller_1 = require("../controllers/file.controller");
const auth_middleware_1 = __importDefault(require("../middlewares/auth.middleware"));
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const router = (0, express_1.Router)();
const fileController = new file_controller_1.FileController();
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path_1.default.extname(file.originalname));
    }
});
const upload = (0, multer_1.default)({
    storage: storage,
    limits: {
        fileSize: 50 * 1024 * 1024,
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|txt|zip|rar/;
        const extname = allowedTypes.test(path_1.default.extname(file.originalname).toLowerCase());
        const mimetype = allowedTypes.test(file.mimetype);
        if (mimetype && extname) {
            return cb(null, true);
        }
        else {
            cb(new Error('Invalid file type'));
        }
    }
});
router.post('/upload', auth_middleware_1.default.authenticate, upload.single('file'), fileController.uploadFile);
router.post('/upload-multiple', auth_middleware_1.default.authenticate, upload.array('files', 10), fileController.uploadMultipleFiles);
router.get('/', auth_middleware_1.default.authenticate, fileController.getFiles);
router.get('/user/:userId', auth_middleware_1.default.authenticate, fileController.getUserFiles);
router.get('/download/:fileId', auth_middleware_1.default.authenticate, fileController.downloadFile);
router.get('/:fileId', auth_middleware_1.default.authenticate, fileController.getFileInfo);
router.delete('/:fileId', auth_middleware_1.default.authenticate, fileController.deleteFile);
router.put('/:fileId', auth_middleware_1.default.authenticate, fileController.updateFile);
router.get('/:fileId/thumbnail', auth_middleware_1.default.authenticate, fileController.getThumbnail);
exports.default = router;
//# sourceMappingURL=file.routes.js.map