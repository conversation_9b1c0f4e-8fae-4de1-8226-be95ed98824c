import { Request, Response, NextFunction } from 'express';
import { AuthService } from '@/services/auth.service';
import { 
  RegisterRequest, 
  LoginRequest, 
  RefreshTokenRequest, 
  AuthResponse 
} from '@/types';
import { 
  ValidationError, 
  InvalidCredentialsError,
  ConflictError 
} from '@/utils/errors';
import { asyncHandler } from '@/utils/errors';
import { logger, logBusinessOperation, logSecurityEvent } from '@/utils/logger';

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  // 用户注册
  register = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const registerData: RegisterRequest = req.body;

    try {
      const result = await this.authService.register(registerData);

      res.status(201).json({
        success: true,
        data: result,
        message: 'User registered successfully',
      });

      logBusinessOperation('USER_REGISTER_SUCCESS', result.user.id, {
        email: result.user.email,
        userRole: result.user.userRole,
      });
    } catch (error) {
      logger.error('Registration failed:', error);
      
      if (error instanceof ConflictError) {
        res.status(409).json({
          success: false,
          error: {
            code: 'EMAIL_ALREADY_EXISTS',
            message: 'An account with this email already exists',
          },
        });
      } else {
        throw error;
      }
    }
  });

  // 用户登录
  login = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const loginData: LoginRequest = req.body;

    try {
      const result = await this.authService.login(loginData);

      res.json({
        success: true,
        data: result,
        message: 'Login successful',
      });

      logBusinessOperation('USER_LOGIN_SUCCESS', result.user.id, {
        email: result.user.email,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
    } catch (error) {
      if (error instanceof InvalidCredentialsError) {
        logSecurityEvent('LOGIN_FAILED', undefined, {
          email: loginData.email,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        });
        
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password',
          },
        });
      } else {
        logger.error('Login failed:', error);
        throw error;
      }
    }
  });

  // 刷新令牌
  refreshToken = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { refreshToken }: RefreshTokenRequest = req.body;

    try {
      const result = await this.authService.refreshToken(refreshToken);

      res.json({
        success: true,
        data: result,
        message: 'Token refreshed successfully',
      });
    } catch (error) {
      logger.error('Token refresh failed:', error);
      
      res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_REFRESH_TOKEN',
          message: 'Invalid or expired refresh token',
        },
      });
    }
  });

  // 获取当前用户信息
  getProfile = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    res.json({
      success: true,
      data: {
        user: req.user,
      },
      message: 'User profile retrieved successfully',
    });
  });

  // 修改密码
  changePassword = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      throw new ValidationError('Current password and new password are required');
    }

    try {
      await this.authService.changePassword(req.user.id, currentPassword, newPassword);

      res.json({
        success: true,
        message: 'Password changed successfully',
      });

      logBusinessOperation('PASSWORD_CHANGE_SUCCESS', req.user.id, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
    } catch (error) {
      if (error instanceof InvalidCredentialsError) {
        logSecurityEvent('PASSWORD_CHANGE_FAILED', req.user.id, {
          reason: 'Invalid current password',
          ip: req.ip,
        });
        
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_CURRENT_PASSWORD',
            message: 'Current password is incorrect',
          },
        });
      } else {
        logger.error('Password change failed:', error);
        throw error;
      }
    }
  });

  // 请求密码重置
  requestPasswordReset = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { email } = req.body;

    if (!email) {
      throw new ValidationError('Email is required');
    }

    try {
      await this.authService.requestPasswordReset(email);

      // 出于安全考虑，无论用户是否存在都返回成功
      res.json({
        success: true,
        message: 'If an account with this email exists, a password reset link has been sent',
      });

      logBusinessOperation('PASSWORD_RESET_REQUESTED', 0, {
        email,
        ip: req.ip,
      });
    } catch (error) {
      logger.error('Password reset request failed:', error);
      
      // 出于安全考虑，不暴露具体错误信息
      res.json({
        success: true,
        message: 'If an account with this email exists, a password reset link has been sent',
      });
    }
  });

  // 重置密码
  resetPassword = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { resetToken, newPassword } = req.body;

    if (!resetToken || !newPassword) {
      throw new ValidationError('Reset token and new password are required');
    }

    try {
      await this.authService.resetPassword(resetToken, newPassword);

      res.json({
        success: true,
        message: 'Password reset successfully',
      });

      logBusinessOperation('PASSWORD_RESET_SUCCESS', 0, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
    } catch (error) {
      logger.error('Password reset failed:', error);
      
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_RESET_TOKEN',
          message: 'Invalid or expired reset token',
        },
      });
    }
  });

  // 注销（客户端处理）
  logout = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (req.user) {
      logBusinessOperation('USER_LOGOUT', req.user.id, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
    }

    res.json({
      success: true,
      message: 'Logout successful',
    });
  });

  // Google OAuth回调
  googleCallback = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    // 这里应该处理Google OAuth回调
    // 暂时返回未实现
    res.status(501).json({
      success: false,
      error: {
        code: 'NOT_IMPLEMENTED',
        message: 'Google OAuth not implemented yet',
      },
    });
  });

  // GitHub OAuth回调
  githubCallback = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    // 这里应该处理GitHub OAuth回调
    // 暂时返回未实现
    res.status(501).json({
      success: false,
      error: {
        code: 'NOT_IMPLEMENTED',
        message: 'GitHub OAuth not implemented yet',
      },
    });
  });

  // 验证邮箱
  verifyEmail = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { token } = req.query;

    if (!token) {
      throw new ValidationError('Verification token is required');
    }

    try {
      // 这里应该实现邮箱验证逻辑
      // 暂时返回未实现
      res.status(501).json({
        success: false,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'Email verification not implemented yet',
        },
      });
    } catch (error) {
      logger.error('Email verification failed:', error);
      throw error;
    }
  });

  // 重新发送验证邮件
  resendVerification = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { email } = req.body;

    if (!email) {
      throw new ValidationError('Email is required');
    }

    try {
      // 这里应该实现重新发送验证邮件逻辑
      // 暂时返回未实现
      res.status(501).json({
        success: false,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'Email verification not implemented yet',
        },
      });
    } catch (error) {
      logger.error('Resend verification failed:', error);
      throw error;
    }
  });

  // 检查邮箱是否可用
  checkEmailAvailability = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    const { email } = req.query;

    if (!email) {
      throw new ValidationError('Email is required');
    }

    try {
      // 这里应该检查邮箱是否已被使用
      // 暂时返回可用
      res.json({
        success: true,
        data: {
          available: true,
        },
        message: 'Email availability checked',
      });
    } catch (error) {
      logger.error('Email availability check failed:', error);
      throw error;
    }
  });

  // 获取用户权限
  getUserPermissions = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw new Error('User not authenticated');
    }

    // 这里应该返回用户的权限列表
    // 暂时返回基本权限
    const permissions = {
      canCreateAssets: req.user.userRole !== 'ADMIN',
      canManageAssets: req.user.userRole !== 'ADMIN',
      canWithdrawFunds: req.user.userRole !== 'ADMIN',
      canAccessAdminPanel: req.user.userRole === 'ADMIN',
    };

    res.json({
      success: true,
      data: {
        permissions,
      },
      message: 'User permissions retrieved successfully',
    });
  });
}

export default AuthController;