import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Button,
  Stack,
  Chip,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  ToggleButton,
  ToggleButtonGroup,
  Skeleton,
  Fade,
  useTheme,
  useMediaQuery,
  Fab,
  Zoom,
} from '@mui/material';
import {
  GridView,
  ViewList,
  FilterList,
  Sort,
  KeyboardArrowUp,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { Asset, AssetSearchParams, PaginationParams } from '../../../types';
import ModernAssetCard from './ModernAssetCard';
import ModernSearchBar from './ModernSearchBar';

// 扩展的搜索参数类型
type ExtendedSearchParams = AssetSearchParams & PaginationParams;

interface ModernAssetGridProps {
  assets: Asset[];
  loading: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
  searchQuery: string;
  currentFilters: ExtendedSearchParams;
  onFiltersChange: (filters: ExtendedSearchParams) => void;
  onSearch: (query: string) => void;
}

type ViewMode = 'grid' | 'list';
type SortOption = 'newest' | 'oldest' | 'popular' | 'trending' | 'price_asc' | 'price_desc' | 'rating';

const ModernAssetGrid: React.FC<ModernAssetGridProps> = ({
  assets,
  loading,
  hasMore,
  onLoadMore,
  searchQuery,
  currentFilters,
  onFiltersChange,
  onSearch,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [showScrollTop, setShowScrollTop] = useState(false);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // 模拟数据
  const mockAssets: any[] = Array.from({ length: 20 }, (_, index) => ({
    id: `asset-${index + 1}`,
    title: `AI Generated Asset ${index + 1}`,
    description: `This is a high-quality AI generated asset perfect for your creative projects. Asset number ${index + 1}.`,
    shortDescription: `AI Asset ${index + 1}`,
    category: ['image', 'video', 'audio', 'model', 'tool'][index % 5],
    type: 'image',
    tags: [`tag${index + 1}`, `ai`, `generated`, `creative`],
    price: index % 3 === 0 ? 0 : Math.floor(Math.random() * 100) + 10,
    currency: 'USD',
    isPremium: index % 5 === 0,
    isFree: index % 3 === 0,
    isExclusive: index % 7 === 0,
    isPublic: true,
    thumbnailUrl: `https://picsum.photos/300/200?random=${index + 1}`,
    previewUrl: `https://picsum.photos/600/400?random=${index + 1}`,
    fileUrl: '',
    downloadUrl: '',
    fileSize: Math.floor(Math.random() * 50) + 1,
    fileSizeUnit: 'MB',
    format: 'PNG',
    dimensions: { width: 1920, height: 1080 },
    duration: null,
    license: 'standard',
    visibility: 'public',
    status: 'published',
    creator: {
      id: `creator-${index + 1}`,
      email: `creator${index + 1}@example.com`,
      username: `Creator${index + 1}`,
      firstName: `Creator`,
      lastName: `${index + 1}`,
      avatar: `https://i.pravatar.cc/40?img=${index + 1}`,
      role: 'creator' as any,
      userRole: 'creator' as any,
      status: 'active' as any,
      isEmailVerified: true,
      isTwoFactorEnabled: false,
      isVerified: index % 4 === 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    stats: {
      downloads: Math.floor(Math.random() * 10000) + 100,
      views: Math.floor(Math.random() * 50000) + 500,
      likes: Math.floor(Math.random() * 1000) + 50,
      comments: Math.floor(Math.random() * 100) + 10,
      shares: Math.floor(Math.random() * 50) + 5,
      purchases: Math.floor(Math.random() * 500) + 20,
      revenue: Math.floor(Math.random() * 5000) + 100,
      avgRating: 3.5 + Math.random() * 1.5,
      totalRatings: Math.floor(Math.random() * 200) + 50,
      conversionRate: Math.random() * 0.1 + 0.05,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }));

  const displayAssets = assets.length > 0 ? assets : mockAssets;

  const sortOptions = [
    { value: 'newest', label: '最新发布' },
    { value: 'popular', label: '最受欢迎' },
    { value: 'trending', label: '热门趋势' },
    { value: 'rating', label: '评分最高' },
    { value: 'price_asc', label: '价格从低到高' },
    { value: 'price_desc', label: '价格从高到低' },
  ];

  const categories = [
    { value: '', label: '全部分类' },
    { value: 'image', label: '图像生成' },
    { value: 'video', label: '视频制作' },
    { value: 'audio', label: '音频处理' },
    { value: 'model', label: 'AI模型' },
    { value: 'tool', label: '创作工具' },
  ];

  // 监听滚动显示回到顶部按钮
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 400);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 无限滚动
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          onLoadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading, onLoadMore]);

  const handleSortChange = (event: any) => {
    const sortBy = event.target.value as SortOption;
    onFiltersChange({
      ...currentFilters,
      sortBy,
      page: 1,
    });
  };

  const handleCategoryChange = (event: any) => {
    const category = event.target.value;
    onFiltersChange({
      ...currentFilters,
      category: category || undefined,
      page: 1,
    });
  };

  const handleViewModeChange = (
    event: React.MouseEvent<HTMLElement>,
    newViewMode: ViewMode | null,
  ) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
    }
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const renderSkeletons = () => (
    <Grid container spacing={3}>
      {Array.from({ length: 8 }).map((_, index) => (
        <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
          <Skeleton variant="rectangular" height={200} sx={{ borderRadius: 2 }} />
          <Skeleton variant="text" sx={{ mt: 1 }} />
          <Skeleton variant="text" width="60%" />
          <Stack direction="row" spacing={1} sx={{ mt: 1 }}>
            <Skeleton variant="circular" width={24} height={24} />
            <Skeleton variant="text" width="40%" />
          </Stack>
        </Grid>
      ))}
    </Grid>
  );

  const getGridColumns = () => {
    if (viewMode === 'list') return { xs: 1 };
    return { xs: 1, sm: 2, md: 3, lg: 4, xl: 5 };
  };

  return (
    <Box sx={{ py: { xs: 4, md: 6 }, bgcolor: 'background.default' }}>
      <Container maxWidth="xl">
        {/* 搜索栏 */}
        <Box sx={{ mb: 4 }}>
          <ModernSearchBar
            value={searchQuery}
            onChange={(value) => {/* 处理搜索输入变化 */}}
            onSearch={onSearch}
            placeholder="搜索AI生成内容、工具、模型..."
          />
        </Box>

        {/* 过滤和排序控件 */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'stretch', md: 'center' },
            gap: 2,
            mb: 4,
            p: 3,
            bgcolor: 'background.paper',
            borderRadius: 2,
            boxShadow: 1,
          }}
        >
          <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap" gap={1}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>分类</InputLabel>
              <Select
                value={currentFilters.category || ''}
                onChange={handleCategoryChange}
                label="分类"
              >
                {categories.map((category) => (
                  <MenuItem key={category.value} value={category.value}>
                    {category.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>排序</InputLabel>
              <Select
                value={currentFilters.sortBy || 'newest'}
                onChange={handleSortChange}
                label="排序"
              >
                {sortOptions.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* 活跃过滤器显示 */}
            {currentFilters.category && (
              <Chip
                label={`分类: ${categories.find(c => c.value === currentFilters.category)?.label}`}
                onDelete={() => handleCategoryChange({ target: { value: '' } })}
                size="small"
                color="primary"
                variant="outlined"
              />
            )}
          </Stack>

          <Stack direction="row" spacing={1} alignItems="center">
            <Typography variant="body2" color="text.secondary">
              {displayAssets.length} 个结果
            </Typography>
            
            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={handleViewModeChange}
              size="small"
            >
              <ToggleButton value="grid">
                <GridView />
              </ToggleButton>
              <ToggleButton value="list">
                <ViewList />
              </ToggleButton>
            </ToggleButtonGroup>
          </Stack>
        </Box>

        {/* 资源网格 */}
        {loading && displayAssets.length === 0 ? (
          renderSkeletons()
        ) : (
          <Fade in timeout={300}>
            <Grid container spacing={3}>
              {displayAssets.map((asset, index) => (
                <Grid item {...getGridColumns()} key={asset.id}>
                  <Fade in timeout={300 + index * 50}>
                    <div>
                      <ModernAssetCard
                        asset={asset}
                        onLike={(assetId) => console.log('Like:', assetId)}
                        onDownload={(assetId) => console.log('Download:', assetId)}
                        onAddToCart={(assetId) => console.log('Add to cart:', assetId)}
                        onPreview={(assetId) => console.log('Preview:', assetId)}
                      />
                    </div>
                  </Fade>
                </Grid>
              ))}
            </Grid>
          </Fade>
        )}

        {/* 加载更多 */}
        <Box ref={loadMoreRef} sx={{ mt: 4, textAlign: 'center' }}>
          {loading && displayAssets.length > 0 && (
            <Stack spacing={2} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                正在加载更多内容...
              </Typography>
              {renderSkeletons()}
            </Stack>
          )}
          
          {!hasMore && displayAssets.length > 0 && (
            <Typography variant="body2" color="text.secondary">
              已显示全部内容
            </Typography>
          )}
        </Box>

        {/* 回到顶部按钮 */}
        <Zoom in={showScrollTop}>
          <Fab
            color="primary"
            size="medium"
            onClick={scrollToTop}
            sx={{
              position: 'fixed',
              bottom: 24,
              right: 24,
              zIndex: 1000,
            }}
          >
            <KeyboardArrowUp />
          </Fab>
        </Zoom>
      </Container>
    </Box>
  );
};

export default ModernAssetGrid;
