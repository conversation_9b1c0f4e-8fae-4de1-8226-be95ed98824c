# AIGC Service Hub - 数据库实现文档

## 概述

本目录包含了 AIGC Service Hub MVP 1.0 的完整数据库实现，基于 PostgreSQL 15+ 和 Redis 7+。

## 文件结构

```
database/
├── schema.sql                    # 完整数据库schema
├── init.sql                     # 数据库初始化脚本
├── config.js                    # 数据库连接配置
├── migrate.js                   # 迁移管理工具
├── backup-script.sh             # 数据库备份脚本
├── postgresql.conf              # PostgreSQL 配置
├── redis.conf                   # Redis 配置
├── migrations/                  # 迁移文件目录
│   └── 001_initial_schema.sql   # 初始schema迁移
├── backups/                     # 备份文件目录
├── postgres-data/               # PostgreSQL 数据目录
├── redis-data/                  # Redis 数据目录
└── README.md                    # 本文档
```

## 核心数据表

### 1. 用户表 (users)
- 支持个人创作者、企业创作者和管理员
- OAuth登录支持（Google、GitHub）
- 积分余额管理

### 2. 资源表 (assets)
- 数字资产信息存储
- 支持多种资产类型（MODEL、LORA、WORKFLOW、PROMPT、TOOL）
- 价格支持USD和积分双重定价

### 3. 交易表 (transactions)
- USD和积分交易记录
- PayPal集成支持
- 完整的交易状态管理

### 4. 账本条目表 (ledger_entries)
- 财务系统核心表
- 支持7天资金保护期
- 阶梯式分佣算法实现

### 5. 提现请求表 (withdrawal_requests)
- 创作者收益提现管理
- 管理员审批工作流
- PayPal批量付款支持

### 6. 系统配置表 (system_configs)
- 平台参数配置
- 支持多种数据类型
- 运行时动态配置

## 快速开始

### 1. 使用 Docker 启动数据库

```bash
# 启动数据库服务
docker-compose -f docker-compose.db.yml up -d

# 查看服务状态
docker-compose -f docker-compose.db.yml ps

# 查看日志
docker-compose -f docker-compose.db.yml logs -f postgres
```

### 2. 手动安装（本地开发）

```bash
# 安装依赖
npm install pg ioredis

# 设置环境变量
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export POSTGRES_DB=aigc_service_hub
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=your_password

# 创建数据库
createdb aigc_service_hub

# 执行初始化
psql -h localhost -U postgres -d aigc_service_hub -f schema.sql
psql -h localhost -U postgres -d aigc_service_hub -f init.sql
```

### 3. 使用迁移工具

```bash
# 初始化迁移表
node migrate.js init

# 执行所有迁移
node migrate.js up

# 查看迁移状态
node migrate.js status

# 创建新迁移
node migrate.js create "add new feature"

# 回滚迁移
node migrate.js rollback 001_initial_schema
```

## 配置说明

### 环境变量

创建 `.env` 文件：

```bash
# 数据库配置
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=aigc_service_hub
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# 连接池配置
DB_POOL_MIN=2
DB_POOL_MAX=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=2000

# 缓存配置
CACHE_DEFAULT_TTL=300
CACHE_TTL_ASSETS=300
CACHE_TTL_USER_PROFILE=600
CACHE_TTL_TRANSACTIONS=60

# 备份配置
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE="0 2 * * *"
```

### 应用中使用

```javascript
const { init, getDb, getRedis } = require('./database/config');

// 初始化数据库连接
async function startApp() {
  await init();
  
  // 使用 PostgreSQL
  const db = getDb();
  const result = await db.query('SELECT * FROM users WHERE id = $1', [1]);
  
  // 使用 Redis
  const redis = getRedis();
  await redis.set('key', 'value', 'EX', 300);
  
  console.log('应用启动成功');
}
```

## 性能优化

### 1. 索引策略
- 所有外键都有相应索引
- 查询频繁的字段添加复合索引
- 使用部分索引优化特定查询

### 2. 分区表（适用于大数据量）
```sql
-- 按月分区交易表
CREATE TABLE transactions_partitioned (
    LIKE transactions INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- 创建分区
CREATE TABLE transactions_2024_01 PARTITION OF transactions_partitioned
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### 3. 缓存策略
- 资产列表缓存 5 分钟
- 用户资料缓存 10 分钟
- 系统配置缓存 1 小时

## 数据备份

### 1. 自动备份

```bash
# 设置定时任务
crontab -e

# 添加每日凌晨2点备份
0 2 * * * /path/to/backup-script.sh
```

### 2. 手动备份

```bash
# 执行备份
./backup-script.sh

# 列出备份文件
./backup-script.sh --list

# 恢复备份
./backup-script.sh --restore /backups/backup_file.sql.gz
```

### 3. 备份验证

```bash
# 测试数据库连接
./backup-script.sh --test

# 清理旧备份
./backup-script.sh --cleanup
```

## 监控和维护

### 1. 健康检查

```javascript
const { healthCheck } = require('./database/config');

// 检查数据库健康状态
const health = await healthCheck();
console.log(health);
// 输出: { postgres: true, redis: true, timestamp: '2024-01-01T00:00:00.000Z' }
```

### 2. 连接池监控

```javascript
const { getStats } = require('./database/config');

// 获取连接池统计
const stats = getStats();
console.log(stats);
// 输出: { postgres: { totalCount: 5, idleCount: 3, waitingCount: 0 }, redis: { status: 'ready' } }
```

### 3. 慢查询监控

```sql
-- 查看慢查询
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;
```

## 安全配置

### 1. 数据库安全
- 使用强密码
- 启用SSL连接
- 限制网络访问
- 定期更新密码

### 2. 备份安全
- 备份文件加密
- 安全的备份存储
- 访问权限控制

### 3. 连接安全
- 使用连接池
- 设置连接超时
- 防止SQL注入

## 常见问题

### Q: 如何重置数据库？
```bash
# 停止应用
docker-compose -f docker-compose.db.yml down

# 删除数据卷
docker-compose -f docker-compose.db.yml down -v

# 重新启动
docker-compose -f docker-compose.db.yml up -d
```

### Q: 如何升级数据库版本？
```bash
# 1. 备份当前数据
./backup-script.sh

# 2. 创建新的迁移文件
node migrate.js create "upgrade database version"

# 3. 编辑迁移文件添加升级SQL

# 4. 执行迁移
node migrate.js up
```

### Q: 如何处理连接问题？
```javascript
// 检查连接状态
const health = await healthCheck();
if (!health.postgres) {
  console.error('PostgreSQL连接失败');
}
if (!health.redis) {
  console.error('Redis连接失败');
}
```

## 财务系统实现

### 1. 分佣计算
```sql
-- 计算创作者分佣
SELECT * FROM calculate_commission(asset_id, amount_usd);
```

### 2. 资金状态管理
- PENDING: 7天保护期
- AVAILABLE: 可提现
- WITHDRAWN: 已提现
- REFUNDED: 已退款

### 3. 积分系统
- 积分购买：100积分 = $1
- 积分消费：直接扣除
- 积分记录：完整的账本记录

## 扩展功能

### 1. 读写分离
```javascript
// 主库写入
const masterDb = new Pool(masterConfig);
await masterDb.query('INSERT INTO users ...');

// 从库读取
const slaveDb = new Pool(slaveConfig);
const result = await slaveDb.query('SELECT * FROM users ...');
```

### 2. 连接池配置
```javascript
const poolConfig = {
  min: 2,           // 最小连接数
  max: 20,          // 最大连接数
  idleTimeoutMillis: 30000,  // 空闲超时
  connectionTimeoutMillis: 2000,  // 连接超时
};
```

### 3. 缓存策略
```javascript
// 多级缓存
const cache = {
  L1: new Map(),    // 内存缓存
  L2: redisClient,  // Redis缓存
  L3: database,     // 数据库
};
```

## 技术支持

如有问题，请查看：
1. 应用日志
2. 数据库日志
3. 性能监控指标
4. 错误堆栈信息

联系开发团队获取进一步支持。